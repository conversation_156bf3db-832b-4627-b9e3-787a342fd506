<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="ir_cron_queue_job_garbage_collector" model="ir.cron">
            <field name="name">Jobs Garbage Collector</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field ref="model_queue_job" name="model_id"/>
            <field name="state">code</field>
            <field eval="False" name="active"/>
            <field name="code">model.requeue_stuck_jobs()</field>
        </record>

        <!-- Queue-job-related subtypes for messaging / Chatter -->
        <record id="mt_job_failed" model="mail.message.subtype">
            <field name="name">Job failed</field>
            <field name="res_model">queue.job</field>
            <field name="default" eval="True"/>
        </record>

        <record id="ir_cron_autovacuum_queue_jobs" model="ir.cron">
            <field name="name">AutoVacuum Job Queue</field>
            <field ref="model_queue_job" name="model_id"/>
            <field eval="False" name="active"/>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field eval="False" name="doall"/>
            <field name="state">code</field>
            <field name="code">model.autovacuum()</field>
        </record>

    </data>

    <data noupdate="0">

        <record model="queue.job.channel" id="channel_root">
            <field name="name">root</field>
        </record>

    </data>
</odoo>
