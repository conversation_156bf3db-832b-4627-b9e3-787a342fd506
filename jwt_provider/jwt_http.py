from odoo import http
from odoo.http import request, Response
from .validator import validator
import simplej<PERSON> as json
from .constants import message_keys
return_fields = ['id', 'login', 'name', 'company_id' , 'groups_id']


class JwtHttp:

    def get_state(self):
        return {
            'd': request.session.db
        }

    def parse_request(self):
        http_method = request.httprequest.method
        try:
            body = http.request.params
        except Exception:
            body = {}

        headers = dict(list(request.httprequest.headers.items()))
        if 'wsgi.input' in headers:
            del headers['wsgi.input']
        if 'wsgi.errors' in headers:
            del headers['wsgi.errors']
        if 'HTTP_AUTHORIZATION' in headers:
            headers['Authorization'] = headers['HTTP_AUTHORIZATION']

        # extract token
        token = ''
        if 'Authorization' in headers:
            try:
                # Bearer token_string
                token = headers['Authorization'].split(' ')[1]
            except Exception:
                pass

        return http_method, body, headers, token

    def date2str(self, d, f='%Y-%m-%d %H:%M:%S'):
        """
        Convert datetime to string
            :param self: 
            :param d: datetime object
            :param f='%Y-%m-%d%H:%M:%S': string format
        """
        try:
            s = d.strftime(f)
        except:
            s = None
        finally:
            return s

    def response(self, success=True, message=None, data=None, code=200):
        """
        Create a HTTP Response for controller 
            :param success=True indicate this response is successful or not
            :param message=None message string
            :param data=None data to return
            :param code=200 http status code
        """
        payload = json.dumps({
            'success': success,
            'message': message,
            'data': data,
        })

        return Response(payload, status=code, headers=[
            ('Content-Type', 'application/json'),
        ])

    def response_500(self, message='Internal Server Error', data=None):
        raise Exception(message,data)

    def response_404(self, message='404 Not Found', data=None):
        raise Exception(message,data)

    def response_403(self, message='403 Forbidden', data=None):
        raise Exception(message,data)

    def errcode(self, code, message=None):
        raise Exception(message)
    
    def successcode(self, code, message=None,user_info=False):
        return {"success":True, "code":200, "message":message,"user_info":user_info}

    def do_login(self, login, password):
        # get current db
        olivery_user=request.env['rb_delivery.user'].sudo().search(['|',('mobile_number','=',login),('email','=',login)])
        if olivery_user==False or len(olivery_user)==0:
            return self.errcode(code=400, message=message_keys.EMAIL_IS_NOT_REGISTERED_ON_THE_SYSTEM)
        elif olivery_user:
            if olivery_user.state!='confirmed' and olivery_user.state!='reconfirmed':
                return self.errcode(code=401, message=message_keys.YOUR_ACCOUNT_IS_NOT_CONFIRMED_YET_PLEASE_CONTACT_YOUR_ADMINISTRATOR)
        
            try:
                state = self.get_state()
                request.session.authenticate(state['d'], login, password)
            except:
                return self.errcode(code=401, message=message_keys.USER_NOT_AUTHORIZED)
            # login success, generate token
            user = request.env.user.read(return_fields)[0]
            user_info=request.env['rb_delivery.user'].sudo().search_read([('user_id','=',request.env.user.id)],['id', 'user_id', 'state', 'username', 'mobile_number', 'area_id', 'email', 'address', 'group_id', 'role_name','role_code','inclusive_delivery','commercial_name','has_customers','player_id','forgot_password','online','is_block_delivery_fee','default_payment_type','default_payment_detail','bank_name','bank_number','wallet_name', 'wallet_number','holder_name', 'user_parent_id','collection_in_main_user_name','account_manager_mobile', 'hide_totals_from_dashboard_parent'])
            
            token = validator.create_token(user)
            return { "success":True, "code":200,'res_user': user,'rb_delivery_user':user_info,'context':request.env['ir.http'].session_info(), 'token': token }

    def do_logout(self, token):
        request.session.logout()
        if token :
            exsit_token = request.env['jwt_provider.access_token'].sudo().search([('token', '=', token)])
            if exsit_token:
                exsit_token.unlink()

    def cleanup(self):
        # Clean up things after success request
        # use logout here to make request as stateless as possible



        request.session.logout()


jwt_http = JwtHttp()