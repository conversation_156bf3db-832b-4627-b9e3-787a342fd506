import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ObjectService {

  deepClone(obj:any) {
    if (typeof obj !== "object" || obj === null) {
      return obj;
    }
  
    let clone:any
    if(obj instanceof Array){
      clone=[]
      for (let i=0;i< obj.length;i++) {
        clone[i] = this.deepClone(obj[i]);
      }
    }else{
      clone={}
      for (let key in obj) {
        clone[key] = this.deepClone(obj[key]);
      }
    }
    
  
    return clone;
  }
}

