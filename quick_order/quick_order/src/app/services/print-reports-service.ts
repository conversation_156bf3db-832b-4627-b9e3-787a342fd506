import { Injectable } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { OdooJsonRPC } from './odooJsonRPC';
import { TranslateService } from '@ngx-translate/core';
import { PdfDialogComponent } from '../components/pdf-dialog/pdf-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class PrintReportService {
  constructor(
    private odooRpc: OdooJsonRPC,
    private translate: TranslateService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  fetchReports(modelName: string, userGroupId: number) {
    return this.odooRpc.searchRead(
      'ir.actions.report',
      [['model', '=', modelName], '|', ['groups_id', 'in', userGroupId], ['groups_id', '=', false]],
      ['name', 'report_name'],
      0,
      0,
      ''
    ).then(reports => {
      if (reports?.body?.result?.success) {
        return reports.body.result.result;
      }
      return false;
    });
  }

  printReport(reportName: string, modelName: string, recordsIds: any, displayName: string) {
    return this.odooRpc.call('rb_delivery.utility', 'print_report_job_queued', [reportName, modelName, recordsIds])
      .then(reportData => {
        if (reportData?.body?.result?.result?.success) {
          let waitTime = recordsIds.length / 20;
          waitTime = Math.max(5, Math.min(waitTime, 10)) * 1000;

          this.checkReportStatus(reportData.body.result.result.result, waitTime, displayName);
          
        }
      });
  }

  async printDefaultReport(modelName: string, recordsIds: number[], fallBackIfReportName: string, displayName: string) {
    let reportName = fallBackIfReportName;
    let reportNameCall = await this.odooRpc.call('rb_delivery.mobile_default_print', 'get_default_report_name', [modelName]);

    if (reportNameCall?.body?.result?.success) {
      reportName = reportNameCall.body.result.result;
    }

    return this.printReport(reportName, modelName, recordsIds, displayName);
  }

  async checkReportStatus(jq_id: string, interval: number, displayName: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.width = 'fit-content;';  
    dialogConfig.height = 'auto'; 
    dialogConfig.data = { jq_id, interval, displayName };
    dialogConfig.disableClose = true; 
    dialogConfig.autoFocus = false; 
  
    this.dialog.open(PdfDialogComponent, dialogConfig);
  }
  
  async openPDF(reportData: string, displayName: string): Promise<void> {
    const fileName = `${displayName}.pdf`;
    const blob = this.base64ToBlob(reportData, 'application/pdf');
    const url = window.URL.createObjectURL(blob);
    window.open(url);
  }

  private base64ToBlob(base64: string, contentType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: contentType });
  }
}
