<div style="display: flex; gap: 4px; align-items: center;">
  <div [formGroup]="form" class="trigger-div" style="flex: 1;">
    <mat-form-field appearance="outline" style="width: 100%;">
      <input #searchBox type="text" matInput formControlName="searchControl" [matAutocomplete]="auto"
        (keydown)="onKeydownDontScroll($event)" (blur)="onBlur()" />
      <mat-autocomplete #auto="matAutocomplete" (keydown)="onKeydownDontScroll($event)"
        (optionSelected)="selectValue($event.option.value)">
        <mat-option *ngFor="let value of selectionItems" [value]="value">
          {{ value.display_name }}
        </mat-option>
      </mat-autocomplete>
      <button (click)="openSelector($event)" tabindex="-1" mat-icon-button matSuffix>
        <mat-icon>arrow_drop_down</mat-icon>
      </button>
    </mat-form-field>
  </div>
  <button *ngIf="showCreateButton"  (click)="onCreateClicked()"
      style="cursor: pointer; display: flex; align-items: center; background: transparent !important; border: none; border-radius: 4px;">
      <mat-icon style="color: rgb(51, 51, 51) !important;">add_circle_outline</mat-icon>
  </button>
</div>