import { Component, Input, OnInit, Output, EventEmitter, SimpleChanges, ViewChild, ElementRef, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslateService } from '@ngx-translate/core';
import { OdooJsonRPC } from '../../services/odooJsonRPC';
import { Directionality } from '@angular/cdk/bidi';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ObjectService } from '../../services/object-service';
import { debounceTime, Subscription } from 'rxjs';
import { MatAutocompleteTrigger } from '@angular/material/autocomplete';

@Component({
  selector: 'app-dynamic-selection-template',
  templateUrl: './dynamic-selection-template.component.html',
  styleUrls: ['./dynamic-selection-template.component.scss'],
})
export class DynamicSelectionTemplateComponent implements OnInit, On<PERSON><PERSON>roy {
  @Input() modelName!: string;
  @Input() selectionItems!: any[];
  @Input() domain: any[] = [];
  @Input() selectedValue: any;
  @Input() showCreateButton!: boolean;
  @Input() field!: {
    id: number;
    ttype: string;
    name: string;
    placeholder: string;
    model_name: string;
    input_type: string;
    readonly: boolean;
    is_location: boolean;
    is_signature: boolean;
    barcode_verified: boolean;
    options?: { value: string; label: string }[];
  };
  @Input() searchDomain: any[] = [];
  @Input() limitPerSearch: number = 0;
  @Input() parentField: any;
  @Input() selectionType!: string;
  @Input() placeholder!: string;
  @Input() isTouchSelection!: boolean;
  @Input() imageFieldName!: string;
  @Input() isAutoFillMappingFieldsEnabled!: boolean;
  @Input() InTouchSelection!: boolean;
  @Input() mappingFields!: any[];
  @Output() buttonPress: any = new EventEmitter<any>();
  @Output() openSelectorEmiter: any = new EventEmitter<any>();
  @Output() validationEmiter:any = new EventEmitter<any>();
  @Input() validations!: { email: boolean; required: boolean; max_length: number; min_length: number };
  form: FormGroup;
  valueToSearch: any;
  isRtl: boolean = false;

  @Input() value!: any;

  @ViewChild('searchBox') searchBox!: ElementRef;

  @ViewChild(MatAutocompleteTrigger)
  autocompleteTrigger!: MatAutocompleteTrigger;

  navigatingOptions: boolean = false

  selectedIds: number[] = [];
  filteredItems!: any[];
  isInfiniteScrollDisabled!: boolean;
  isInSearch!: boolean;
  isLocalSearch!: boolean;
  tempSelectionItems!: any[];
  valueChangesSubscription!: Subscription;

  constructor(
    private odooRpc: OdooJsonRPC,
    private snackBar: MatSnackBar,
    private dir: Directionality,
    private translate: TranslateService,
    private fb: FormBuilder,
    private objectService: ObjectService
  ) {
    this.form = this.fb.group({
      searchControl: new FormControl(''),
    });
  }

  ngOnInit() {
    this.searchDomain = this.searchDomain ? this.searchDomain : [['name', 'ilike', 'value']]
    if (this.limitPerSearch === 0) {
      this.limitPerSearch = 15;
    }
    if ((this.selectionType === 'many2many' || this.selectionType === 'one2many') && this.selectedValue) {
      for (let v of this.selectedValue) {
        this.selectedIds.push(v.id);
      }
    }
    if (!this.selectionItems) {
      // this.fetchSelectionItems();
    } else {
      this.isLocalSearch = false;
      this.isInfiniteScrollDisabled = false;
    }

    this.isRtl = this.dir.value === 'rtl';
    this.dir.change.subscribe((direction) => {
      this.isRtl = direction === 'rtl';
    });

    this.form.get('searchControl')!.setValue(this.value || '');

    this.subscribeToValueChanges();

    this.isLocalSearch = true;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['value'] && changes['value'].currentValue && changes['value'].currentValue.display_name) {
      this.value = changes['value'].currentValue.display_name;
      this.form.get('searchControl')!.setValue(this.value, { emitEvent: false });
    } 

    if (this.value === undefined || this.value === 'undefined') {
      this.value = '';
      this.form.get('searchControl')!.setValue(this.value, { emitEvent: false });
      this.subscribeToValueChanges();
      this.fetchSelectionItems();
    } else
    if (changes['value'] && !this.selectionItems) {
      this.fetchSelectionItems();
    }
  }

  ngOnDestroy() {
    if (this.valueChangesSubscription) {
      this.valueChangesSubscription.unsubscribe();
    }
  }

  private subscribeToValueChanges() {
    if (this.valueChangesSubscription) {
      this.valueChangesSubscription.unsubscribe();
    }

    this.valueChangesSubscription = this.form.get('searchControl')!.valueChanges
      .pipe(debounceTime(100))
      .subscribe(value => {
        if (value === undefined && this.selectedValue === undefined 
          || value === 'undefined' && this.selectedValue === undefined 
          || value === null && this.selectedValue === undefined) return;
        if (this.selectedValue && this.selectedValue === value) return;

        this.filterItems({ target: { value } });
      });
  }

  onBlur() {
    this.validationEmiter.emit()
  }

  async fetchSelectionItems() {
    let fields = ['id', 'display_name'];
    if (this.isAutoFillMappingFieldsEnabled) {
      for (let field of this.mappingFields) {
        fields.push(field[1]);
      }
    }
    if (this.parentField) fields.push(this.parentField.field_map);
    this.limitPerSearch = 150;
    await this.odooRpc.searchRead(this.modelName, this.isInSearch ? this.domain.concat(this.searchDomain) : this.domain, fields, this.limitPerSearch, 0, '').then(responseData => {
      if (responseData && responseData.body && responseData.body.result && responseData.body.result.result.success) {
        this.selectionItems = responseData.body.result.result.result;
        this.filteredItems = this.selectionItems;
      } else {
        this.selectionItems = [];
        this.filteredItems = [];
      }
    });
  }

  openSelector(event: Event) {
    if (this.InTouchSelection) {
      event.preventDefault();
      event.stopPropagation();
      this.openSelectorEmiter.emit(true);
    } else {
      this.searchDomain = [];
    }
  }

  onKeydownDontScroll(event: KeyboardEvent) {
    const keyboardEvent = event as KeyboardEvent;

    const activeOption = this.autocompleteTrigger?.activeOption;
    if (activeOption && !this.navigatingOptions) {
      this.navigatingOptions = true
    }
    if ((event.key === 'Enter') && this.selectedValue == undefined) {
      event.preventDefault();
      event.stopPropagation();

      if (this.filteredItems.length > 0 && !this.navigatingOptions) {
        this.selectValue(this.filteredItems[0])
        if (this.autocompleteTrigger) {
          this.autocompleteTrigger.closePanel()
        }
      }

      if (this.validations.required && this.selectedValue == undefined) {
        keyboardEvent.preventDefault();
        this.validationEmiter.emit()
        return
      }
      const focusableElements = 'input, button, select, textarea, a[href], [tabindex]:not([tabindex="-1"])';
      const currentElement = event.target as HTMLElement;
      const focusable = Array.from(document.querySelectorAll(focusableElements))
        .filter((el: Element) => !el.classList.contains('mat-mdc-button-base'));
  
      const currentIndex = focusable.indexOf(currentElement);
  
      if (currentIndex >= 0 && currentIndex < focusable.length - 1) {
        (focusable[currentIndex + 1] as HTMLElement).focus();
      } else if (currentIndex === focusable.length - 1) {
        (focusable[0] as HTMLElement).focus();
      }
    } else if ((event.key === 'Tab') && !this.selectedValue && this.validations.required) {
      this.validationEmiter.emit()
      keyboardEvent.preventDefault();
      event.preventDefault();
      event.stopPropagation();
      return
    }
  }

  loadMoreSelectionItems(event: Event) {
    if (this.isInfiniteScrollDisabled) {
      return;
    }
    let fields = ['id', 'display_name'];
    if (this.isAutoFillMappingFieldsEnabled) {
      for (let field of this.mappingFields) {
        fields.push(field[1]);
      }
    }
    if (this.parentField) fields.push(this.parentField.field_map);

    this.odooRpc.searchRead(this.modelName, this.domain, fields, this.limitPerSearch, this.selectionItems.length, '').then(responseData => {
      if (responseData && responseData.body && responseData.body.result && responseData.body.result.success && responseData.body.result.result.length > 0) {
        this.selectionItems = this.selectionItems.concat(responseData.body.result.result);
        this.filteredItems = this.selectionItems;
        if (responseData.body.result.result.length < this.limitPerSearch) {
          this.isInfiniteScrollDisabled = true;
        }
      } else if (responseData.body.result.success && responseData.body.result.result.length === 0) {
        this.isInfiniteScrollDisabled = true;
      } else {
        this.snackBar.open(this.translate.instant('FETCHING_SELECTION_VALUE_FAIL'), 'Close', {
          duration: 6000,
        });
      }
    });
  }

  handleCardKeyPress(event: KeyboardEvent, value: any) {
    const keyCode = event.keyCode;
    if (keyCode === 13 || keyCode === 32) {
      const cardValue = value;
      this.selectValue(cardValue);
    }
  }

  updateSelectedValues(selectedValue: any) {
    if (!this.selectedValue) {
      this.selectedValue = [];
    }

    if (!this.selectedIds.includes(selectedValue.id)) {
      this.selectedValue.push(selectedValue);
      this.selectedIds.push(selectedValue.id);
    } else {
      this.selectedValue = this.selectedValue.filter((value: any) => value.id !== selectedValue.id);
      this.selectedIds = this.selectedIds.filter(id => selectedValue.id !== id);
    }
  }

  emitValues() {
    if (this.selectionType === 'many2many' || this.selectionType === 'one2many') {
      if (this.selectedValue.length === 0) this.selectedValue = undefined;
    }
    this.buttonPress.emit(this.selectedValue);
  }

  selectValue(value: any) {
    this.selectedValue = value;
    this.form.get('searchControl')!.setValue(value.display_name, { emitEvent: false });
    this.emitValues();
  }

  async filterItems(event: any) {
    this.valueToSearch = event.target.value;
    this.isInfiniteScrollDisabled = false;
    this.buttonPress.emit([]);
    if (this.valueToSearch) {
      this.isInSearch = true;
      this.isLocalSearch = false;
      if (!this.isLocalSearch) {
        for (let domainItem of this.searchDomain) {
          if (typeof domainItem === 'string') {
            continue;
          } else {
            (domainItem as string[]).pop();
            (domainItem as string[]).push(this.valueToSearch);
          }
        }
        await this.fetchSelectionItems();
        this.tempSelectionItems = this.selectionItems;
        this.filteredItems = this.selectionItems.filter(item => item.display_name.toLowerCase().includes(this.valueToSearch.toLowerCase()));
      } else {
        this.tempSelectionItems = this.selectionItems;
        this.filteredItems = this.selectionItems.filter(item => item.display_name.toLowerCase().includes(this.valueToSearch.toLowerCase()));
      }
    } else {
      if (!this.isLocalSearch) {
        
        this.isInSearch = false;
        await this.fetchSelectionItems();
        this.tempSelectionItems = this.selectionItems;
        this.filteredItems = this.selectionItems.filter(item => item.display_name.toLowerCase().includes(this.valueToSearch.toLowerCase()));
      } else {
        this.filteredItems = this.tempSelectionItems;
      }
    }
  }

  onCreateClicked(): void {
    const message = {
      topic: 'create_record',
      payload: {
        model: this.field.model_name,
        view_type: 'form',
        string: this.translate.instant(this.field.placeholder),
        field_name: this.field.name,
      }
    };
    if (window.parent) {
      window.parent.postMessage(message, '*'); 

      window.addEventListener('message', this.handleOdooMessage.bind(this));
      window.removeEventListener('message', this.handleOdooMessage.bind(this));
    }
  }
  async handleOdooMessage(event: MessageEvent): Promise<void> {
    const data = event.data;
    if (data.topic === 'record_created') {
      const newRecord = data.payload;      
      await this.fetchSelectionItems();
      this.selectedValue = this.selectionItems.find(item => item.id === newRecord.id) || null;
      this.selectValue(this.selectedValue)
    }
  }

}
