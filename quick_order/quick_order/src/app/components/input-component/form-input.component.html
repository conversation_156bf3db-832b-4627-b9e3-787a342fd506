<div *ngIf="field" class="input-field-container" #inputFieldContainer id="inputFieldContainer">
  <div [ngStyle]="{'pointer-events': field.readonly ? 'none' : 'auto'}" *ngIf="field" [id]="field.id" class="no-padding"
    style="height: fit-content; width: inherit;flex: 9 0;">
    <div *ngIf="field.readonly"
      style="position: absolute; z-index: 99; height: 90%; width: 100%; border-radius: 10px; font-weight: bold; color: black;">
    </div>
    <div style="padding-bottom: 5px; width: inherit; justify-content: space-evenly;"
    *ngIf="field && (field.ttype === 'many2one' || field.ttype === 'many2many' || field.ttype === 'one2many')">
    <app-dynamic-selection-template
      *ngIf="field && field.ttype == 'many2one'"
      [validations]="validations"
      [ngClass]="validationMessage && validationMessage.length > 0 ? 'input-error-style' : 'input-style'"
      [modelName]="field.model_name" [selectionItems]="selectionItems"
      [domain]="getDomain()" [selectedValue]="value"
      [searchDomain]="searchDomain ? searchDomain : [['name', 'ilike', 'value']]"
      [limitPerSearch]="limitPerSearch || 0" [parentField]="parentField" [selectionType]="'one2many'"
      [placeholder]="field.placeholder" [imageFieldName]="imageFieldName"
      (openSelectorEmiter)="emitSelector($event)"
      [InTouchSelection]="InTouchSelection"
      (validationEmiter)="validateInput()"
      [showCreateButton]="showCreateButton"
      [field]="field"
      [isAutoFillMappingFieldsEnabled]="isAutoFillMappingFieldsEnabled" (buttonPress)="emitValueSelected($event)" [value]="value"
      [mappingFields]="mappingFields">
    </app-dynamic-selection-template>
    <app-multi-selection-template
      *ngIf="field && field.ttype != 'many2one'"
      [ngClass]="validationMessage && validationMessage.length > 0 ? 'input-error-style' : 'input-style'"
      [modelName]="field.model_name" [selectionItems]="selectionItems"
      [domain]="getDomain()"
      [searchDomain]="searchDomain ? searchDomain : [['name', 'ilike', 'value']]"
      [limitPerSearch]="limitPerSearch || 0" [parentField]="parentField"
      [placeholder]="field.placeholder" [imageFieldName]="imageFieldName"
      (openSelectorEmiter)="emitSelector($event)"
      [InTouchSelection]="InTouchSelection"
      (validationEmiter)="validateInput()"
      [isAutoFillMappingFieldsEnabled]="isAutoFillMappingFieldsEnabled" (buttonPress)="emitValueSelected($event)" [value]="value"
      [mappingFields]="mappingFields">
  </app-multi-selection-template>
  
  </div>
  

    <mat-form-field floatLabel="always"
      *ngIf="field && !field.is_location && (field.ttype === 'text' || field.ttype === 'char' || field.ttype === 'integer' || field.ttype === 'float')">
      <input matInput class="input-class"
        style="background: #EAEAEA !important;padding: 0;width: 100.5% !important;padding-inline-start:6px;box-sizing: border-box;"
        [(ngModel)]="value === false ? '' : value"
        type="text"
        [id]="field.placeholder"
        (keydown.tab)="preventTabChange($event)"
        (blur)="onBlur()"
        [attr.maxlength]="validations.max_length > 1 ? validations.max_length : null"
        (focus)="checkOpenSelection()" (input)="onChangeValue($event)"
        [ngClass]="{'input-error-style': validationMessage && validationMessage.length > 0, 'input-style': !(validationMessage && validationMessage.length > 0), 'input-bold': field.readonly}">
    </mat-form-field>

    <div *ngIf="field && field.ttype === 'selection'">
      <div [class.disabled]="loading" class="olivery-item-for-input"
        [ngClass]="validationMessage && validationMessage.length > 0 ? 'input-error-style' : 'input-style'"
        (click)="openSelection()">
        <div *ngIf="!loading"
          [ngClass]="validationMessage && validationMessage.length > 0 ? 'input-error-style' : 'input-style'"
          class="ripple-container"></div>
        <label *ngIf="!value || value.length == 0 || loading">{{field.placeholder | translate}}</label>
        <ng-container *ngIf="field.ttype === 'selection' && value && !loading">
          <div style="color: var(--primary-color); display: flex; flex-direction: row; align-items: center;">
            <label>{{value[1]}}</label>
          </div>
          <button mat-icon-button (click)="resetValue($event)">
            <mat-icon>close</mat-icon>
          </button>
        </ng-container>
      </div>
    </div>

    <div (click)="setDateValue()" class="datetime-input" *ngIf="field && (field.ttype === 'datetime' || field.ttype === 'date')">
      <mat-form-field *ngIf="field.ttype === 'datetime'">
        <mat-datetimepicker-toggle [for]="datetimePicker" matSuffix class="custom-icon"></mat-datetimepicker-toggle>
        <mat-datetimepicker #datetimePicker type="datetime"></mat-datetimepicker>
        <input style="    border: none !important;
        padding-top: 6px;
        padding-inline-start: 5px;" (dateChange)="onChangeValue($event)" matInput [matDatetimepicker]="datetimePicker" [formControl]="datetimeControl" required autocomplete="off" />
      </mat-form-field>
    
      <mat-form-field *ngIf="field.ttype === 'date'">
        <mat-datepicker-toggle [for]="picker" matSuffix class="custom-icon"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <input style="border: none !important; padding-inline-start: 5px; padding-top: 6px;" (dateChange)="onChangeValue($event)" matInput formControlName="start" [formControl]="datetimeControl" [matDatepicker]="picker" required autocomplete="off" />
      </mat-form-field>
    </div>

    <div class="boolean-style" *ngIf="field && field.ttype === 'boolean'">
      <mat-slide-toggle [checked]="value" [(ngModel)]="value" (change)="onChangeValue($event)"></mat-slide-toggle>
    </div>

    <div *ngIf="field && field.ttype === 'binary' && !field.is_signature">
      <img style="border-radius: 4px; margin: 0 auto; width: 173px; border: 1px solid #3D3937; margin-top: 30px;" alt=""
        src="../assets/photo.PNG">
    </div>

  </div>
</div>

<div *ngIf="isButton" class="no-padding">
  <button mat-raised-button [color]="success ? 'success' : fail ? 'danger' : 'primary'" [disabled]="loading"
    (click)="emitFunction(buttonFunction)" class="button-style">
    <span *ngIf="buttonText">{{buttonText | translate}}</span>
    <mat-icon *ngIf="buttonIcon">{{buttonIcon}}</mat-icon>
    <mat-spinner *ngIf="loading"
      [color]="success ? 'success' : fail ? 'danger' : buttonFunction === 'getCurrentLocation' ? 'primary' : 'light'"
      style="zoom: 0.8;"></mat-spinner>
  </button>
</div>

<div *ngIf="isSeparator && separatorTitle" class="no-padding" style="padding-top: 0px;">
  <mat-divider style="padding: 0;margin: 0;color: #E9E9E9;"></mat-divider>
</div>

<ng-container *ngFor="let error of validationMessage">
  <div *ngIf="!field.is_location" class="validation"><mat-icon>info</mat-icon>{{error.messageKey | translate : error.messageParams}}</div>
</ng-container>

<ng-container *ngIf="field && value">
  <div *ngIf="warning_message" class="warning"><mat-icon>info</mat-icon>{{warning_message | translate}}</div>
</ng-container>