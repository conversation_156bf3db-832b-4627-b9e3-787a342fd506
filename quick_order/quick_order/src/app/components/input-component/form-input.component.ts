import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild, AfterViewInit, Renderer2 } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DatePipe } from '@angular/common';
import { FormControl } from '@angular/forms';
import { DynamicSelectionComponent } from '../dynamic-selection/dynamic-selection.component';
import { OdooJsonRPC } from '../../services/odooJsonRPC';
import { MatInput } from '@angular/material/input';
import { of } from 'rxjs';

@Component({
  selector: 'app-form-input',
  templateUrl: './form-input.component.html',
  styleUrls: ['./form-input.component.scss'],
})
export class InputItemComponent implements OnInit,AfterViewInit {
  @ViewChild('dateModal', {}) dateModal: any;
  @ViewChild('sPad', { static: false }) signaturePadElement: any;
  @ViewChild('matInputEl', {}) matInputEl: MatInput | undefined;

  signaturePad: any;
  @Input() searchBy!: string[];
  @Input() isSeparator!: boolean;
  @Input() showCreateButton!: boolean;
  @Input() separatorTitle!: string;
  @Input() background!: string;
  @Input() color!: string;
  @Input() isButton!: boolean;
  @Input() buttonText!: string;
  @Input() buttonIcon!: string;
  @Input() buttonFunction!: string;
  @Input() field!: { 
    id: number;
    ttype: string; 
    name: string;
    placeholder: string; 
    model_name: string; 
    input_type: string;
    readonly: boolean;
    is_location: boolean;
    is_signature: boolean;
    barcode_verified: boolean;
    options?: { value: string; label: string }[];
  };
  private _internalValue: any;
  @Input()
  get value(): any {
    return this._internalValue === false ? '' : this._internalValue;
  }

  set value(val: any) {
    this._internalValue = val === '' ? false : val;
    this.datetimeControl.setValue(this._internalValue, { emitEvent: false });
  }
  @Input() warning_message !: any;
  @Input() validations!: { email: boolean; required: boolean; max_length: number; min_length: number };
  @Input() invisible!: boolean;
  @Input() parentField!: { ttype: string; name: string; model_name: string; field_map: string; reflect_to_parent: string };
  @Input() haveImage!: boolean;
  @Input() imageFieldName!: string;
  @Input() domain!: any;
  @Input() searchDomain!: string[];
  @Input() limitPerSearch!: number;
  @Input() showBarcodeScanner!: boolean;
  @Input() showVoiceToTextAbility: boolean = false;
  @Input() parentValue!: any;
  @Input() loading: string = 'false';
  @Input() success: string = 'false';
  @Input() fail: string = 'false';
  @Output() selectedValue: any = new EventEmitter<any>();
  @Output() selectedParentValue: any = new EventEmitter<any>();
  @Input() isFormCreator: boolean = false;
  @Input() isMonetary: boolean = false;
  @Input() formName!: string;
  @Input() firstSeparator!: boolean;
  @Input() selectionItems!: any[];
  @Output() buttonPress: any = new EventEmitter<any>();
  @Output() setParentLoading: any = new EventEmitter<any>();
  @Output() updateMappingFields: any = new EventEmitter<any>();
  @Input() isAutoFillMappingFieldsEnabled!: boolean;
  @Input() connected_field_to_selection_modal!: any;
  @Input() search_domain_inside_selection_modal!: string[];
  @Input() showClientHistory!: boolean;
  @Input() InTouchSelection!: boolean;
  @Output() computeEmmiter:any = new EventEmitter<any>();
  @Output() validationEmiter:any = new EventEmitter<any>();
  @ViewChild('inputFieldContainer',{static:false}) inputFieldContainer!: ElementRef ;
  selectedDateTime!: string;
  displayName!: string;
  noOfElements!: number;
  isVoiceToTextInProgress: boolean = false;
  voiceToTextResults!: String[];
  @Input() validationMessage!: any[];
  clientMatched: boolean = false;
  clients: any[] = [];
  @Input() mappingFields!: any[];
  barcodeList: string[] = [];
  referenceBarcodeList: string[] = [];
  sequenceBarcodeList: string[] = [];
  validBarcodes: string[] = [];
  attachments: any[] = [];
  @Input() numberOfInputs: number = 0;
  datetimeControl = new FormControl();

  constructor(
    private dialog: MatDialog,
    private odooRpc: OdooJsonRPC,
    public datePipe: DatePipe,
    private snackBar: MatSnackBar,
    private changeDetectorRef: ChangeDetectorRef,
    private render: Renderer2
  ) { }

  ngAfterViewInit(): void {
    this.setFocus();
  }

  setFocus(){
    if(this.inputFieldContainer){
      this.inputFieldContainer.nativeElement.focus()
    }
  }

  saveSignature() {
    if (this.signaturePad) {
      const signatureDataURL = this.signaturePad.toDataURL(); 
      this.emitValue(signatureDataURL);
    }
  }

  clearSignature() {
    if (this.signaturePad) {
      this.signaturePad.clear();
      this.emitValue(false);
    }
  }

  ngOnInit() {
    if (this.field && this.field.is_location) {
      this.field.name = "get.current.location";
    } else if (this.field && this.field.is_signature) {
      this.field.name = "signature";
    }
    if (this.value == false) {
      this.value = undefined
    }
    if(this.domain) {
      for (let i = 0; i < this.domain.length; i++) {
        if(this.domain[i] && this.domain[i][2] == 'true' || this.domain[i][2] == 'True') {
          this.domain[i] = [
            this.domain[i][0], this.domain[i][1], true
          ]
        } else if(this.domain[i] && this.domain[i][2] == 'false' || this.domain[i][2] == 'False') {
          this.domain[i] = [
            this.domain[i][0], this.domain[i][1], false
          ]
        }
      }
    }
    if (this.inputFieldContainer) {
      this.inputFieldContainer.nativeElement.querySelectorAll('input, select, textarea').forEach((element: HTMLElement) => {
        element.blur();
      });
    }

    this.datetimeControl.valueChanges.subscribe(value => {
      this.onDateTimeChange(value);
    });
  }

  preventTabChange(event: Event) {
    const keyboardEvent = event as KeyboardEvent;
  
    const inputValue = this.value;
    if (keyboardEvent.key === 'Tab' && (!inputValue || inputValue == undefined || inputValue.trim() === '') && this.validations.required) {
      if (this.field.ttype == 'float') {
        this.emitValue('0')
      } else {
        keyboardEvent.preventDefault();
        this.validateInput()
      }
    }
  }
  async presentToast(title: string, message: string) {
    this.snackBar.open(message, title, {
      duration: 5000,
      verticalPosition: 'top'
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['value'] && changes['value'].currentValue && (typeof changes['value'].currentValue) == 'object' && 'display_name' in changes['value'].currentValue) {
      this.displayName = changes['value'].currentValue.display_name as string;
      if (this.parentField) {
        this.searchForParent(changes['value'].currentValue);
      }
    } else if (changes['value'] && changes['value'].currentValue) {
      if (this.field && this.field.ttype != 'boolean' && this.field.ttype != 'float' && this.field.ttype != 'integer' && this.value == false) {
        this.value = undefined;
      }
    }

    if (this.field && this.field.ttype != 'boolean' && this.field.ttype != 'float' && this.field.ttype != 'integer' && changes['value'] && changes['value'].currentValue == false) {
      this.value = undefined;
    }


    if (changes['warning_message']) {
      this.warning_message = changes['warning_message'].currentValue
    }

  }

  emitParentValue(parentValue: any) {
    this.selectedParentValue.emit(parentValue);
    this.setParentLoading.emit(false);
  }

  searchForParent(childValue: any) {
    if (childValue[this.parentField.field_map]) {
      setTimeout(() => {
        this.setParentLoading.emit(true);
      }, 1);
      
      return of(this.odooRpc.searchRead(this.parentField.model_name, [['id', '=', childValue[this.parentField.field_map][0]]], false, 0, 0, "").then(data => {
        try {
          let parentValue = data.body.result.result.result[0];
          if (parentValue)
            this.emitParentValue(parentValue);
          else
            this.setParentLoading.emit(false);
        } catch {
          this.setParentLoading.emit(false);
        }
      }));
    }
    return false;
  }

  getDomain() {
    if (this.parentField && this.parentValue)
      return [[this.parentField.field_map, '=', this.parentValue]].concat(this.domain || []);
    else return this.domain;
  }

  openSelector() {
    this.openModalValueSelector(this.field.ttype);
  }

  emitValueSelected(selectedValueInput: any) {
    if(selectedValueInput) {
      this.emitValue(selectedValueInput);
      this.updateMappingFields.emit(selectedValueInput, this.field);
    }
  }

  emitSelector(isOpen: any) {
    if(isOpen) {
      this.openSelector()
    }
  }
  openModalValueSelector(ttype: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.width = '80%';
    dialogConfig.height = '80%';
    dialogConfig.data = {
      modelName: this.field.model_name,
      selectionItems: this.selectionItems,
      domain: this.getDomain(),
      selectedValue: this.value,
      searchDomain: this.searchDomain ? this.searchDomain : [['name', 'ilike', 'value']],
      limitPerSearch: this.limitPerSearch || 0,
      parentField: this.parentField,
      selectionType: ttype,
      placeholder: this.field.placeholder,
      imageFieldName: this.imageFieldName,
      isAutoFillMappingFieldsEnabled: this.isAutoFillMappingFieldsEnabled,
      mappingFields: this.mappingFields
    };

    const dialogRef = this.dialog.open(DynamicSelectionComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(output => {
      if (output) {
        this.emitValue(output);
        if (this.isAutoFillMappingFieldsEnabled) {
          this.updateMappingFields.emit(output, this.field);
        }
        if (ttype === 'many2many' || ttype === 'one2many') {
          setTimeout(() => {
            this.checkOverflow();
          }, 100);
        }
      }
    });
  }

  openSelection() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.width = '80%';
    dialogConfig.height = '80%';
    dialogConfig.data = {
      selectionItems: this.selectionItems,
      title: this.field.placeholder,
      selectedValue: this.value
    };

    const dialogRef = this.dialog.open(DynamicSelectionComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(output => {
      if (output) {
        this.emitValue(output);
      }
    });
  }

  emitValue(value: any) {

    if (typeof value === "object" && 'display_name' in value) {
      this.displayName = value.display_name;
      this.value = value;
    } else {
      if (Array.isArray(value) && value.length > 0 && value[0].multiScannedElemtns && this.value) {
        const sequencesToFilter: Set<string> = new Set(value[0].sequences);
        this.value = this.value.filter((item: { sequence: string; }) => sequencesToFilter.has(item.sequence));
      } else {
        this.value = value;
      }
    }
 
    if (Array.isArray(value) && value.length > 0) {
      let inputBarcode: string[] = [];
      for (let item of value) {
        if (item.sequence) {
          inputBarcode.push(item.sequence);
        }
      }
      this.barcodeList = this.barcodeList.concat(inputBarcode);
      this.noOfElements = this.barcodeList.length;
    }

    this.selectedValue.emit(value);
  }

  onBlur() {
    this.computeEmmiter.emit()
    this.validationEmiter.emit()
  }

  emitFunction(functionName: string) {
    this.buttonPress.emit(functionName);
  }

  validateInput() {
    this.validationEmiter.emit()
  }

  onChangeValue(event: any) {
    let value = event
    if (this.field &&this.field.ttype === 'boolean') {
      value = event.checked;
    }else
      value = event.target.value;
      if (this.field.ttype === 'float' || this.field.ttype === 'integer') {
        value = value.replace(',', '.');
        value = value.replace('٫', '.');
        value = value.replace(/[^\d٠١٢٣٤٥٦٧٨٩.-]+/g, '');
        if (!/^-\d*\.?\d*$/.test(value)) {
          value = value.replace(/^-/, '');
        }
        const dotCount = value.split('.').length - 1;
        if (dotCount > (this.field.ttype === 'integer' ? 0 : 1)) {
          const parts = value.split('.');
          value = parts[0] + '.' + parts.slice(1).join('');
        }
        if (value === '.' || value === '-.') {
          value = '';
        }
      }
       else if (this.field.ttype === 'boolean') {
      value = event.checked;
    } else if (this.field.ttype === 'datetime' || this.field.ttype === 'date') {
      // Format the date
      value = this.datePipe.transform(value, 'yyyy-MM-dd HH:mm:ss');
    }

    if (this.field && this.field.input_type === 'tel') {
      const cleanValue = value.replace(/[^\d\u0660-\u0669+]/g, '');
      if (value !== cleanValue) {
        value = cleanValue;
      }
    }

    if (this.field && this.field.ttype != 'boolean' && this.value == false) {
      this.value = undefined;
    }

    if (this.matInputEl && this.matInputEl.value) {
      this.matInputEl.value = value;
    }


    this.emitValue(value);
  }

  checkOpenSelection() {
    if (!this.connected_field_to_selection_modal) {
      return;
    }

    const dialogConfig = new MatDialogConfig();
    dialogConfig.width = '80%';
    dialogConfig.height = '80%';
    dialogConfig.data = {
      modelName: this.connected_field_to_selection_modal[0].relation,
      selectionItems: this.selectionItems,
      selectedValue: this.value,
      searchDomain: this.search_domain_inside_selection_modal ? this.search_domain_inside_selection_modal : [['name', 'ilike', 'value']],
      limitPerSearch: this.limitPerSearch || 0,
      parentField: this.parentField,
      selectionType: this.connected_field_to_selection_modal[0].ttype,
      placeholder: this.connected_field_to_selection_modal[0].name,
      imageFieldName: this.imageFieldName,
      isAutoFillMappingFieldsEnabled: this.isAutoFillMappingFieldsEnabled,
      mappingFields: this.mappingFields
    };



    const dialogRef = this.dialog.open(DynamicSelectionComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(output => {
      if (output) {
        this.updateMappingFields.emit(output, this.field);
      }
    });
  }


  simulateClick() {
    this.render.selectRootElement('#inputFieldContainer')
  }

  setDateValue() {
    this.dateModal?.present();
  }

  resetValue(event: Event) {
    event.stopPropagation();
    this.emitValue(undefined);
  }

  removeValue(value: any, event: MouseEvent): void {
    this.value = this.value.filter((val: any) => val.id !== value.id);
    setTimeout(() => {
      this.checkOverflow();
    }, 100);
    this.emitValue(this.value);
  }

  checkOverflow() {
    let tagsScrollElement = document.getElementById('tagsScroll');
    let elementIds: string[] = [];
    for (let val of this.value) {
      elementIds.push(val.id.toString());
    }
    let noOfElements = 0;
    let totalOffset = 0;
    for (let elementId of elementIds) {
      let element = document.getElementById(elementId);
      if (element && element.offsetWidth) {
        totalOffset += element.offsetWidth;
      }

      if (document.documentElement.dir === 'ltr' && tagsScrollElement && totalOffset > tagsScrollElement.scrollLeft + tagsScrollElement.clientWidth) {
        noOfElements += 1;
      } else if (document.documentElement.dir === 'rtl' && tagsScrollElement && totalOffset < tagsScrollElement.scrollLeft + tagsScrollElement.clientWidth) {
        noOfElements += 1;
      }
    }
    this.noOfElements = noOfElements;
  }

  onDateTimeChange(value: any) {
    const formattedValue = this.datePipe.transform(value, 'yyyy-MM-dd HH:mm:ss');
    this.emitValue(formattedValue);
  }
}
