.form-container {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
  }

  .left-inputs, .right-inputs {
    width: 48%;
    display: flex;
    flex-direction: column;
    padding: 15px;
    gap: 7px;
  }

  .input-element {
    padding-top: 15px;
  }

  .form-content {
    display: flex;
  }

  .table-container {
    padding-top: 25px;
  }

  /* app.component.css or the appropriate global stylesheet */
:host {
  display: flex;
  flex-direction: column;
  min-height: 30vh;
  padding-top: 10px;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 2vh;
}

.form-footer {
  position: sticky;
  bottom: 0;
  width: 100%;
  background-color: #f8f9fa;
  padding: 0 0 34px 0;
  text-align: center;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
}

.table-container {
  flex: 1;
  overflow-y: auto;
  height: 300px; /* Adjust height as needed */
  background: #E9E9E9;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table, th, td {
  border: 1px solid #ddd;
}

.error-message-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 99vh;
}

.small-input input, .small-input select, .small-input textarea {
  width: 100% !important;
  height: 30px !important; /* Adjust the height as needed */
  padding: 5px !important; /* Adjust padding as needed */
  font-size: 12px !important; /* Adjust font size as needed */
}

.small-input .input-label {
  color: #333333;
  font-size: 10pt;
}

@media (max-width: 500px) {
  .form-content {
    flex-direction: column;
  }

  .left-inputs,
  .right-inputs {
    max-width: 100%;
    flex: 0 0 100%;
    width: 90% !important;
  }
}

.form-tables {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  gap: 13px;
}

.table-inner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  gap: 13px;
}

.table-title {
  color: #7C7BAD;
}

.table-title-container {
  width: 100%;
}

/* Attachment section styles */
.form-attachments {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  gap: 13px;
  margin-top: 20px;
}

.attachment-inner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  gap: 13px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.attachment-title {
  color: #7C7BAD;
}

.attachment-title-container {
  width: 100%;
}