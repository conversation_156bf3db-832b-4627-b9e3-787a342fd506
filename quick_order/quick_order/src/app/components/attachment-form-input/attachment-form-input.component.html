<div class="attachment-form-input">
  <div class="file-upload-container"
       [class.drag-over]="isDragOver"
       (dragover)="onDragOver($event)"
       (dragleave)="onDragLeave($event)"
       (drop)="onDrop($event)"
       (click)="fileInput.click()">
    <div class="drop-zone-content">
      <div class="icon-container">
        <mat-icon class="drop-icon">{{ isDragOver ? 'file_download' : 'cloud_upload' }}</mat-icon>
      </div>
      <div class="drop-text">
        {{ isDragOver ? ('DROP_FILES_HERE' | translate) : ('SELECT_OR_DROP_FILES' | translate) }}
      </div>
      <div class="drop-subtext" *ngIf="!isDragOver">
        {{ 'CLICK_OR_DRAG_FILES' | translate }}
      </div>
      <input
        #fileInput
        type="file"
        [multiple]="field && (field.ttype === 'one2many' || field.ttype === 'many2many')"
        [accept]="'*/*'"
        (change)="onFileSelected($event)"
        style="display: none"
      />
    </div>

    <div class="debug-info" *ngIf="selectedFiles.length > 0">
      {{ selectedFiles.length }} {{'FILES_SELECTED' | translate }}
    </div>

    <div class="selected-files" *ngIf="selectedFiles.length > 0">
      <div class="file-item" *ngFor="let file of selectedFiles; let i = index">
        <div class="file-info">
          <mat-icon>insert_drive_file</mat-icon>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size">({{ file.size / 1024 | number: '1.0-0' }} KB)</span>
        </div>
        <button mat-icon-button color="warn" (click)="removeFile(i)">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>
  </div>
</div>
