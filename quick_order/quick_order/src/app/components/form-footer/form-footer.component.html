<div *ngIf="navigateButtons || (formFooterInputs && formFooterInputs.length > 0)" class="footer">
  <div class="grid" style="width: inherit; padding: 0;display: flex;flex-direction: row;gap: 15px;justify-content: space-between;">
    <div *ngIf="orders && orders.length > 0" style="    display: flex;
      align-items: center;
      justify-content: center;
      padding-inline-start: 15px;">
      <div class="col-12">
          <a  class="button-style-count" style="background-color: #707070;">
            <span style="color: white;">{{ orders.length }} - {{'ORDER' | translate}}</span>
          </a>
      </div>
    </div>

    <div *ngIf="formFooterInputs && formFooterInputs.length > 0" class="row" style="display: flex; align-items: center; padding-block: 8px;    gap: 50px;justify-content: center;    margin-inline-start: 20px;">
      <ng-container *ngFor="let input of formFooterInputs">
        <div *ngIf="input.field && input.position === 'inside_footer'" [ngClass]="'col-' + insideFooterCount">
          <div class="row">
            <div class="col-12" style="font-weight: bold;">
              <div style="display: flex; align-items: center;">
                <img  *ngIf="checkInIconMap(input.field_icon)?.exist" [src]="checkInIconMap(input.field_icon)?.path || ''" style="width: 25px;"/>
                <span *ngIf="input.value" style="font-size: 20px; padding: 0 5px; margin: 0;">{{ input.value }}</span>
                <span *ngIf="!input.value && (input.field.ttype == 'float' || input.field.ttype == 'integer')" style="font-size: 20px; padding: 0 5px; margin: 0;">{{ 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>

    <div class="row" style="display: flex; align-items: center; padding-block: 8px;justify-content: center;">
      <ng-container *ngFor="let input of formFooterInputs">
        <div style="    width: fit-content;;
        height: 6vh;
        padding-inline-end: 11px;    display: flex;white-space: nowrap;
         align-items: center;" *ngIf="input.is_button && input.position === 'in_footer'" class="col-12">
            <button class="footer-button" mat-raised-button [color]="input.success ? 'accent' : input.fail ? 'warn' : 'primary'" [disabled]="input.loading" (click)="emitFunction(input)" class="button-style d-flex">
              <img  *ngIf="checkInIconMap(input.button_icon)?.exist" [src]="checkInIconMap(input.button_icon)?.path || ''" style="width: 13px;margin-inline-start: 8px;margin-inline-end: 8px;"/>
              <span style="display: flex;align-items: center;justify-self: center;" *ngIf="input.button_text" [ngStyle]="{'font-size': input.button_icon ? '16px' : '19px'}">{{ input.button_text | translate }}</span>
            </button>
        </div>
      </ng-container>
    </div>

    <div *ngIf="navigateButtons" class="row" style="display: flex; align-items: center; padding-block: 8px;">
      <div class="col" [ngStyle]="{'padding-inline-end': showNext && showBack ? '8px' : '0'}" [ngClass]="(showNext && showBack) ? 'col-6' : 'col-12'">
        <button mat-raised-button color="warn" class="button-style" *ngIf="showBack" (click)="emitPrevStep()">{{ 'BACK' | translate }}</button>
      </div>
      <div class="col" [ngStyle]="{'padding-inline-start': showBack ? '8px' : '0'}" [ngClass]="showBack ? 'col-6' : 'col-12'">
        <button mat-raised-button color="primary" class="button-style" *ngIf="showNext" (click)="emitNextStep()">{{ 'NEXT' | translate }}</button>
      </div>
    </div>
  </div>
</div>
