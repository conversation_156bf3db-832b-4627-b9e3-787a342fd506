import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { OdooJsonRPC } from '../../services/odooJsonRPC';

@Component({
  selector: 'app-pdf-dialog',
  templateUrl: './pdf-dialog.component.html',
  styleUrls: ['./pdf-dialog.component.scss']
})
export class PdfDialogComponent implements OnInit {
  STATUS: string = 'waiting';
  infoMessage: string = this.translate.instant('YOUR_REPORT_IS_WAITING_TO_BE_PROCESSED');
  buttonMessage: string = this.translate.instant('GO_TO_REPORTS');
  jq_id: string = '';
  interval: number = 5000;
  displayName: string = '';
  context: any;
  result: string = '';
  reportDate:any

  public dialogState = new BehaviorSubject<boolean>(true);

  constructor(
    private dialogRef: MatDialogRef<PdfDialogComponent>,
    private snackBar: MatSnackBar,
    private translate: TranslateService,
    private odooRpc: OdooJsonRPC,
    private http: HttpClient,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    if (data) {
      this.jq_id = data.jq_id || '';
      this.displayName = data.displayName || '';
      this.context = data.context || {};
    }
  }

  ngOnInit() {
    this.startReportChecker();
  }

  private startReportChecker(): void {
    const reportChecker = new ReportChecker(
      this.jq_id, this.interval, this.odooRpc, this, this.translate, this.dialogState
    );

    reportChecker.start().then((result) => {
      this.result = result;
      this.openPDF(result, this.displayName);
    }).catch((error) => {
      const message = error instanceof Error ? error.message : 'An unknown error occurred';
      console.error('An error has occurred while printing your report:', message);
      this.showSnackbar('An error has occurred while printing your report', message);
    });
  }

  close(): void {
    this.dialogState.next(false);
    this.dialogRef.close();
  }

  async openPDF(reportData?: string, displayName?: string): Promise<void> {
    if (reportData) {
      this.reportDate = reportData
    }
    if (displayName) {
      this.displayName = displayName
    }
    const fileName = `${displayName}.pdf`;
    const blob = this.base64ToBlob(this.reportDate, 'application/pdf');
    const url = window.URL.createObjectURL(blob);
    window.open(url);
  }

  private base64ToBlob(base64: string, contentType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: contentType });
  }

  private showSnackbar(header: string, message: string): void {
    this.snackBar.open(`${header}: ${message}`, '', {
      duration: 6000,
      verticalPosition: 'top',
    });
  }

  getStatusIcon(): string {
    if (this.STATUS === 'done') {
      this.infoMessage = 'YOUR_REPORT_IS_READY_TO_DOWNLOAD!';
      return 'rb_delivery/static/src/quick_order/browser/assets/icon/report_done_icon.svg';
    } else if (this.STATUS === 'started' || this.STATUS === 'enqueued') {
      this.infoMessage = 'YOUR_REPORT_IS_BEING_PROCESSED!';
      return 'rb_delivery/static/src/quick_order/browser/assets/icon/report_working_icon.svg';
    } else {
      this.infoMessage = 'YOUR_REPORT_IS_WAITING_TO_BE_PROCESSED!';
      return 'rb_delivery/static/src/quick_order/browser/assets/icon/report_waiting_icon.svg';
    }
  }
}

class ReportChecker {
  private intervalId: any = null;
  public reportData: string = '';
  private dialogStateSubscription: Subscription;

  constructor(
    private jqId: string, 
    private interval: number, 
    private odooRpc: OdooJsonRPC,
    private component: PdfDialogComponent,
    private translate: TranslateService,
    private dialogState: BehaviorSubject<boolean>
  ) {
    this.dialogStateSubscription = this.dialogState.subscribe(isOpen => {
      if (!isOpen) this.stop();
    });
  }

  start(): Promise<string> {
    const maxTime = 1000 * 60 * 5;
    let currTime = 0;
    return new Promise((resolve, reject) => {
      if (!this.intervalId) {
        this.intervalId = setInterval(() => {
          this.odooRpc.call('rb_delivery.report_job_queue', 'get_mobile_pdf_result', [this.jqId])
            .then(reportData => {
              if (reportData?.body?.result?.result?.success) {
                const state: string = reportData.body.result.result.result;
                if (state.length > 25) {
                  this.component.STATUS = 'done';
                  this.reportData = state;
                  this.stop();
                  this.component.buttonMessage = this.translate.instant('OPEN_REPORTS');
                  resolve(this.reportData);
                } else if ([
                  'Job not found', 'Printing failed', 'Bad Gateway'
                ].includes(state)) {
                  this.component.STATUS = state;
                  this.stop();
                  reject(new Error(state));
                } else if (state === 'done') {
                  this.stop();
                  reject(new Error(this.translate.instant(
                    'DONE_WITHOUT_PDF_REPORT_PLEASE_CHECK_THE_REPORTS_PAGE_IN_THE_SETTINGS_MENU'
                  )));
                } else {
                  this.component.STATUS = state;
                  this.component.buttonMessage = this.translate.instant('GO_TO_REPORTS');
                }
              } else {
                this.stop();
                reject(new Error(reportData.body.result.message));
              }
            });
          currTime += this.interval;
          if (currTime > maxTime) {
            this.stop();
            reject(new Error(this.translate.instant('TIMEOUT_WHILE_WAITING_FOR_REPORT')));
          }
        }, this.interval);
      }
    });
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}
