// static/src/js/custom_dialog.js
odoo.define('rb_delivery.custom_dialog', function (require) {
    "use strict";

    const Widget = require('web.Widget');
    const core = require('web.core');
    const qweb = core.qweb;
    var dialogs = require('web.view_dialogs');

    const CustomDialog = Widget.extend({
        template: 'dynamic_quick_order_template',
        init: function (parent, options) {
            this._super(parent, options);
            this.options = options || {};
        },
        start: function () {
            this._renderDialog();

            this._boundHandleMessage = this._handleMessage.bind(this);
            window.addEventListener('message', this._boundHandleMessage);
        },
        _handleMessage: function (event) {
            const data = event.data;
            const payload = data.payload;

            if (data.topic === 'create_record') {

                var self = this;
                return new dialogs.SelectCreateDialog(this, _.extend({}, this.nodeOptions, {
                    res_model: payload.model,
                    domain: [],
                    context: {},
                    title: 'Create' + payload.string,
                    initial_ids: undefined,
                    initial_view: payload.view_type,
                    disable_multiple_selection: true,
                    no_create: false,
                    on_selected: function (records) {
                        debugger
                        console.log("oh this function looks cool")
                        console.log("is there a source ?", event.source)
                        if (event.source) {
                            event.source.postMessage({
                                topic: 'record_created',
                                payload: {
                                    id: records[0].id,
                                }
                            }, event.origin);
                        }
                    }
                })).open();
            }
        },
        
        destroy: function () {
            if (this._boundHandleMessage) {
                window.removeEventListener('message', this._boundHandleMessage);
            }
            return this._super.apply(this, arguments);
        },
        _renderDialog: function () {
            const self = this;
            const $dialog = $('<div/>', {
                class: 'custom-dialog',
                html: qweb.render(this.template, this.options)
            });

            $dialog.appendTo('body');

            // Basic styling and structure for the dialog
            $dialog.css({
                position: 'fixed',
                top: '50%',
                left: '50%',
                background: '#fff',
                border: '1px solid #ccc',
                'z-index': 1002
            });

            // Overlay to darken the background
            const $overlay = $('<div/>', {
                class: 'custom-dialog-overlay'
            }).css({
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'rgba(0, 0, 0, 0.5)',
                'z-index': 1001
            }).appendTo('body');

            // Close dialog on overlay click
            $overlay.on('click', function () {
                self._closeDialog($dialog, $overlay);
            });

            // Close dialog on "X" button click
            $dialog.find('.close-btn').on('click', function () {
                self._closeDialog($dialog, $overlay);
            });
        },
        _closeDialog: function ($dialog, $overlay) {
            $dialog.remove();
            $overlay.remove();
            this.trigger_up('dialog_closed', {widget: this});
        }
    });

    return CustomDialog;
});