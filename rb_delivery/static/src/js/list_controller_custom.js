odoo.define('rb_delivery.list_controller_custom_to_remove_create_button', function (require) {
    "use strict";
    var session = require('web.session');
    var ListController = require('web.ListController');
    var FormController = require('web.FormController');
    ListController.include({
        start: function () {
            this._super.apply(this, arguments);
            if (this.modelName === "rb_delivery.order"){
                if('user_info' in session && session['user_info'] && 'group_id' in session['user_info'] && session['user_info']['group_id'] && session['user_info']['group_id'].length > 0){
                    let group_id = session['user_info']['group_id'][0]
                    if('role_code' in session['user_info'] && session['user_info']['role_code'] && !['rb_delivery.role_accounting','rb_delivery.role_manager','rb_delivery.role_super_manager'].includes(session['user_info']['role_code'])){
                        $(".oe_action_button_create_payout").attr("style", "display: none !important;");
                    }
                    if('roles_to_hide_create_order_button' in session && session['roles_to_hide_create_order_button'] && session['roles_to_hide_create_order_button'].includes(group_id)){
                        $(".o_list_button_add").attr("style", "display: none !important;");
                    }
                    if('roles_to_hide_public_link_button' in session && session['roles_to_hide_public_link_button'] && session['roles_to_hide_public_link_button'].includes(group_id)){
                        $(".oe_action_button_public_order_link").attr("style", "display: none !important;");      
                    }
                    if('roles_to_hide_quick_order_button' in session && session['roles_to_hide_quick_order_button'] && session['roles_to_hide_quick_order_button'].includes(group_id)){
                        $(".oe_action_button_quick_order").attr("style", "display: none !important;");        
                    }
                }
                
                if (session && 'show_payout_order_button' in session && !session['show_payout_order_button']){
                    $(".oe_action_button_create_payout").attr("style", "display: none !important;");
                }
                if (session && 'show_create_order_button' in session && !session['show_create_order_button']){
                    $(".o_list_button_add").attr("style", "display: none !important;");
                }
        }
        }
    });
    FormController.include({
        start: function () {
            this._super.apply(this, arguments);
            if (this.modelName === "rb_delivery.order"){
            if ('user_info' in session && session['user_info'] && 'roles_to_hide_create_order_button' in session && session['roles_to_hide_create_order_button']) {
                let group_id = session['user_info']['group_id'][0]
                if (session['roles_to_hide_create_order_button'].includes(group_id)) {
                    $(".o_form_button_create").attr("style", "display: none !important;");
                }
            }
            if (session && 'show_create_order_button' in session && !session['show_create_order_button']){
                $(".o_form_button_create").attr("style", "display: none !important;");
            }
        }
    },

    _onButtonClicked: function (event) {
        if (event && event.data && event.data.record && event.data.record.model &&  event.data.attrs && event.data.attrs.name &&
            event.data.record.model == "rb_delivery.money_collection_download_all_attachments" && event.data.attrs.name === "download_attachments" && 
            event.data.record.context && event.data.record.context.active_ids) {
                var self = this;
                this._rpc({
                    model: 'rb_delivery.money_collection_download_all_attachments',
                    method: 'download_attachments',
                    args: [[],event.data.record.context.active_ids],
                }).then(function (result) {
                    window.open(result.url, '_blank');
                    self.do_action({type: 'ir.actions.act_window_close'});
                });
                return;
        }
        return this._super.apply(this, arguments);
    },
    });
});