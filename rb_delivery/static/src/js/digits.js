odoo.define('digits.Menu', function (require) {
    'use strict';
    var globalself=this
    var digits = sessionStorage.getItem('digits');

    var BasicRenderer = require('web.BasicRenderer');
    var session = require('web.session');
    BasicRenderer.include({
        /**
        * @override
        */
        _renderView: function () {
            var super_render = this._super.apply(this, arguments);
            globalself = this
            if(!digits){
                if (session && 'global_dicimal_value' in session && session['global_dicimal_value']){
                    digits=session['global_dicimal_value']
                    if(!!Number(digits) || Number(digits)==0){
                        sessionStorage.setItem('digits', digits);
                        globalself.modify_digits()
                    }
                }
                
            }else{
                setTimeout(()=>{
                    globalself.modify_digits()
                },1000)
                
            }

            return super_render;

        },
        modify_digits:function() {
                if($(".o_field_float").length>0){
                    for(let i=0; i<= $(".o_field_float").length; i++){
                        let num
                        let text
                        if(!$(".o_field_float").eq(i).hasClass( "o_input" )){
                            text = $(".o_field_float").eq(i).text().replaceAll(',', '');
                            num=Number(text).toFixed(Number(digits))
                            var decimal_parts = num.toString().split(".");
                            decimal_parts[0] = decimal_parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                            num = decimal_parts.join(".");
                            $(".o_field_float").eq(i).html(num)
                        }

                    }

                }
                if($(".o_list_number").length>0){
                    let listNumbers = $(".o_list_number");
                    listNumbers.each(function(index) {
                        let num;
                        let text = $(this).text();

                        if (text.includes('.')) {
                            text = text.replaceAll(',', '');
                            num = Number(text).toFixed(Number(digits));

                            var decimal_parts = num.toString().split(".");
                            decimal_parts[0] = decimal_parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                            num = decimal_parts.join(".");

                            $(this).html(num);
                        }
                    });

                }

        },
    });
});






