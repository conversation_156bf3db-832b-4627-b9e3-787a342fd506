odoo.define('rb_delivery.list_controller_custom', function (require) {
	"use strict";

    var core = require('web.core');
    var _t = core._t;
    var ListController = require('web.ListController');
    var QWeb = core.qweb;
    var session = require('web.session');
    var rpc = require('web.rpc');
    var framework = require('web.framework');
    var crash_manager = require('web.crash_manager');
    var _t = core._t;
    var live = false
    var interval;
    var globalSelf;
    var model_name;
    var WebClient = require('web.WebClient');
    var FieldDropdown = require('muk_web_views_list_dynamic.FieldDropdown');
    var Dialog = require('web.Dialog');
    WebClient.include({
        init: function(parent, client_options){
            globalSelf = this;
            this._super(parent, client_options);
        }});
    ListController.include({
        custom_events: _.extend({}, ListController.prototype.custom_events, {
            update_fields: '_updateFields',
        }),
        init: function (parent, model, renderer, params) {
            this._super.apply(this, arguments);
            if($('.oe_action_drop_list').length == 0){
                var default_value =_t("Default")
                var sequence_value=_t("Sequence")
                var reference_value=_t("Reference")
                var all_value=_t("All")
                var reference_sequence_value=_t("Barcodes")
                var partner_reference_value=_t("Partner Reference")
                if($("div.o_cp_searchview" ).attr('style') && !$("div.o_cp_searchview" ).attr('style').includes('display: none')){
                $("<select class='oe_action_drop_list' style='width:auto ;margin-left:5px ;display:inline !important; height:30px; border-color:#dee2e6; border-radius:5px' name='default_filter' id='default_filter'><option value='default'>"+default_value+"</option> <option value='sequence'>"+sequence_value+"</option><option value='reference'>"+reference_value+"</option><option value='partner_reference'>"+partner_reference_value+"</option><option value='barcodes'>"+reference_sequence_value+"</option><option value='all'>"+all_value+"</option></select>").
                    insertAfter("div.o_cp_searchview")
                    $("div.o_cp_searchview" ).attr("style", "width: 35% ;");
}
                }
                else{
                    if($("div.o_cp_searchview" ).attr('style') && !$("div.o_cp_searchview" ).attr('style').includes('display: none')){
                    $('.oe_action_drop_list').attr("style", "width:auto ;margin-left:5px ;display:inline !important; height:30px; border-color:#dee2e6; border-radius:5px")
                    $("div.o_cp_searchview" ).attr("style", "width: 35% ");}
                }
            live = false
            clearInterval(interval)
            this.viewClass = params.viewClass;
        },
        draft_import: function () {
            if (!this.$buttons) {
                return;
            }
            var self = this;

            this.$buttons.on('click', '.o_button_draft_import', function () {
                var state = self.model.get(self.handle, {raw: true});
                var context = state.getContext()
                context['draft_import'] = true
                self.do_action({
                    type: 'ir.actions.client',
                    tag: 'import',
                    params: {
                        model: self.modelName,
                        context: context,
                    }
                });
            });
        },

        start: function () {
            this._super.apply(this, arguments);
            //Editable
            var self =this

		self.fields_dropdown = self._createFieldsDropdown();

        var isAdmin = session.is_admin || session.uid == 1 || session.uid == 1;
        let manager_role_codes = ['rb_delivery.role_super_manager','rb_delivery.role_manager','rb_delivery.role_picking_up_manager','rb_delivery.role_distribution_manager']
        var isManagerOrSuperManager = session.user_info && session.user_info.role_code && manager_role_codes.includes(session.user_info.role_code)
        var isAllowQuickEdit = session.user_info && session.user_info.show_editable_button
        var isAllowShowFields = session.user_info && session.user_info.show_fields_button

		if(isAdmin || isManagerOrSuperManager || isAllowQuickEdit || isAllowShowFields){
            var x = setInterval(function() {
				$(document).ready(function() {
                    if (self.$buttons){
                        if (isAdmin || isManagerOrSuperManager || isAllowQuickEdit) {
                            self.$mode_switch = $(QWeb.render('muk_web_utils.switch', {
                                id: 'mk-list-switch-' + self.controllerID,
                                label: _t("Editable"),
                            }));
                            self.$buttons.find('.mk_list_button_switch').html(self.$mode_switch);
                            $('.mk_list_button_switch').insertAfter('div.o_favorites_menu')
                            self.$mode_switch.on('change', 'input[type="checkbox"]', function() {
                                self._onSwitchMode.apply(self, arguments);
                                $('.oe_action_live_mode').toggle(!this.checked);
                            });
                        }
                    }
                    clearInterval(x)
                })
				},150);
            var fields_interval = setInterval(function() {
                $(document).ready(function() {
                    if (self.$buttons){
                        self.$list_customize = self.$buttons.find('.mk_list_button_customize');
                        self.fields_dropdown.appendTo(self.$list_customize);
                    }

                    clearInterval(fields_interval)
                })
            },5000);
		}
        //Live
            model_name = this.modelName
            if(this.modelName == 'rb_delivery.order' || this.modelName == 'rb_delivery.order_draft'){
                $('<div class="oe_action_live_mode" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager" ></div>').
                insertAfter('div.o_favorites_menu')
                this.$live_mode = $(QWeb.render('muk_web_utils.switch', {
                    id: 'oe-action-live-mode' + this.controllerID,
                    label: _t("Live")}));
                $('.oe_action_live_mode').html(this.$live_mode);
                this.$live_mode.on('change', 'input[type="checkbox"]', function() {
                    self._onLiveMode.apply(self, arguments);
                    $('.mk_list_button_switch').toggle(!this.checked);
                });
            }
            if(this.modelName === 'rb_delivery.order' && this.hasSidebar)
            {
                if(this.hasSidebar){
                    if($('.oe_action_drop_list').length == 0){
                        var default_value =_t("Default")
                        var sequence_value=_t("Sequence")
                        var reference_value=_t("Reference")
                        var all_value=_t("All")
                        var reference_sequence_value=_t("Barcodes")
                        var partner_reference_value=_t("Partner Reference")
                        $("<select class='oe_action_drop_list' style='width:auto ;margin-left:5px ;display:inline !important; height:30px; border-color:#dee2e6; border-radius:5px' name='default_filter' id='default_filter'><option value='default'>"+default_value+"</option> <option value='sequence'>"+sequence_value+"</option><option value='reference'>"+reference_value+"</option><option value='partner_reference'>"+partner_reference_value+"</option><option value='barcodes'>"+reference_sequence_value+"</option><option value='all'>"+all_value+"</option></select>").
                            insertAfter("div.o_cp_searchview")
                            $("div.o_cp_searchview" ).attr("style", "width: 35% ");
                    }
                    else{
                        if($("div.o_cp_searchview" ).attr('style') && !$("div.o_cp_searchview" ).attr('style').includes('display: none')){
                        $(".oe_action_drop_list").attr("style", "width:auto ;margin-left:5px ;display:inline !important; height:30px; border-color:#dee2e6; border-radius:5px")
                        $("div.o_cp_searchview" ).attr("style", "width: 35%")
                        ;}
                    }
                }
                else{

                    $('.oe_action_drop_list').attr("style", "display:none !important;")
                    $('.oe_action_drop_list').removeClass("red-border")
                    $("div.o_cp_searchview" ).attr("style", "width: 0px !important;");
                    $('.box').attr("style", "display:none ;")
                    $('.arrow-red').attr("style", "display:none ;")
                }
            }
            else{
                $('.oe_action_drop_list').attr("style", "display:none !important;")
                $('.box').attr("style", "display:none ;")
                $('.oe_action_drop_list').removeClass("red-border")
                $('.arrow-red').attr("style", "display:none ;")

                }

                },

                renderButtons: function($node) {
                    this._super.apply(this, arguments);
                    var self = this;
                    if (this.$buttons) {
                        if (session && 'is_public_link_button_visible' in session && !session['is_public_link_button_visible']){
                            self.$buttons.find('.oe_action_button_public_order_link').hide();
                        }
                        if (!session.is_admin) {
                            var role_code = session.user_info.role_code;
                            var model_name = "rb_delivery.quick_access_buttons";

                            this._rpc({
                                model: model_name,
                                method: 'get_button_options_by_role',
                                args: [role_code, false],
                            }).then(function(buttonOptions) {
                                // Reset visibility for all buttons initially to handle dynamic changes
                                self.$buttons.find('.oe_action_quick_access_button_1, .oe_action_quick_access_button_2, .oe_action_quick_access_button_3').removeClass('is-visible').attr("style", "");

                                if (buttonOptions && buttonOptions.length > 0) {
                                    buttonOptions.forEach(function(option, index) {
                                        let iconName = option.is_print_button ? "fa fa-print" : "fa fa-refresh";
                                        let nameParts = option.name.split(":");
                                        let buttonDetail = nameParts.length > 1 ? nameParts[1].trim() : nameParts[0].trim();
                                        buttonDetail=_t(buttonDetail)
                                        let buttonText = `<i class="${iconName}"></i>&nbsp;&nbsp;${buttonDetail}`;
                                        let buttonSelector = `.oe_action_quick_access_button_${index + 1}`;

                                        // Update button text, add visibility class, and bind click event
                                        self.$buttons.find(buttonSelector)
                                            .html(buttonText)
                                            .addClass('is-visible')
                                            .off('click')
                                            .click(function() {
                                                self.executeQuickAccessAction(option);
                                            });
                                    });
                                } else {
                                    // If no buttons should be shown, ensure they remain hidden
                                    for (let j = 1; j <= 3; j++) {
                                        self.$buttons.find(`.oe_action_quick_access_button_${j}`).removeClass('is-visible').attr("style", "display: none !important;");
                                    }
                                }
                            });
                        }
                        if(!session.ability_to_export_data){
                            this.$buttons.find('.mk_list_button_export').hide()
                        }
                        else{
                            this.$buttons.on('click', '.mk_list_button_export', this._onExportView.bind(this));
                        }
                        this.$buttons.find('.oe_action_button_change_state').addClass('is-visible').click(this.proxy('change_state_action'));
                        this.$buttons.find('.oe_action_button_change_money_collection_state').click(this.proxy('change_money_collection_state_action'));
                        this.$buttons.find('.oe_action_button_returned_collection_state').click(this.proxy('change_returned_collection_state_action'));
                        this.$buttons.find('.oe_action_button_change_agent_returned_collection_state').click(this.proxy('change_agent_returned_collection_state_action'));
                        this.$buttons.find('.oe_action_button_change_agent_collection_state').click(this.proxy('change_agent_collection_state_action'));
                        this.$buttons.find('.oe_action_button_change_runsheet_collection_state').click(this.proxy('change_runsheet_collection_state_action'));
                        this.$buttons.find('.oe_action_button_show_orders').click(this.proxy('show_orders_action'));
                        this.$buttons.find('.oe_action_button_sample_excel_template').click(this.proxy('sample_excel_action_def'));
                        this.$buttons.find('.o_button_draft_import').click(this.proxy('draft_import'));
                        this.$buttons.find('.oe_action_button_create_missing_tags').click(this.proxy('create_missing_tags'));
                        this.$buttons.find('.oe_action_button_public_order_link').click(this.proxy('public_order_link_action'));
                        this.$buttons.find('.oe_action_filter_date').click(this.proxy('filter'));
                        this.$buttons.find('.oe_action_clear_date').click(this.proxy('clear_date'));
                        this.$buttons.find('.open_filter_dialog').click(this.proxy('_onOpenFilterDialog'));
                        this.$buttons.find('.reset_filter').click(this.proxy('_reset_filter'));
                        this.$buttons.find('.oe_action_button_create_payout').click(this.proxy('create_payout'));


                    }
                },
        _reset_filter:function(){
            $(".from_date").val('').change()
            $(".to_date").val('').change()
            self.savedFromDate = null;
            self.savedToDate = null;
            this.searchView.do_search()
        },
        _onOpenFilterDialog: function() {
            var self = this;
            var $content = $('<div>', {
                html: QWeb.render('filter_button_template', { widget: self })
            });

            var dialog = new Dialog(this, {
                title: 'Date',
                size: 'large',
                $content: $content,
                buttons: []
            });

            dialog.opened().then(function() {
                $(".modal-title").css("font-size", "36px");
                $(".modal-footer").css("display", "none");
                $('.modal-title').addClass('my_custom_title_class');

                if (self.savedFromDate) {
                    $content.find('.from_date').val(self.savedFromDate);
                }
                if (self.savedToDate) {
                    $content.find('.to_date').val(self.savedToDate);
                }

                // When the Save button is clicked
                $content.find('.oe_action_filter_date').on('click', function() {
                    // Store the input values to instance variables
                    self.savedFromDate = $content.find('.from_date').val();
                    self.savedToDate = $content.find('.to_date').val();
                    $(".to_date").val(self.savedToDate);
                    $(".from_date").val(self.savedFromDate);
                    self.searchView.do_search()

                    dialog.close();
                });

                // When the Clear button is clicked
                $content.find('.oe_action_clear_date').on('click', function() {
                    // Clear the input values
                    $content.find('.from_date').val('');
                    $content.find('.to_date').val('');
                    self.savedFromDate = null;
                    self.savedToDate = null;
                    sessionStorage.setItem('filter_by_date_cleared', true);

                });
            });

            dialog.open();
        },


            _onSelectionChanged: function (event) {
                this.selectedRecords = event.data.selection;
                this.selectedRecordsCount = this.selectedRecords.length;

                var element = '';
                if (this.selectedRecordsCount > 0) {
                    element = '<span class="selected_rows_element text-primary">' + this.selectedRecordsCount + ' #</span>';
                }

                var parent = $('nav.o_cp_pager');

                if (parent.length > 0) {
                    var selectedRowElement = $('.o_cp_pager .selected_rows');

                    if (selectedRowElement.length > 0) {
                        selectedRowElement.html(element);
                    } else if (this.selectedRecordsCount > 0) {
                        $('<span class="selected_rows" style="display:inline;padding-left:5px;padding-right:5px;margin-right:3px;margin-left:3px; padding-top:20px;">' + element + '</span>')
                            .appendTo('.o_cp_pager');
                    }
                }

                this._toggleSidebar();
            },
            _onExportView: function() {
                var selected_ids = []
                var selected_records = this.getSelectedRecords()
                for(var i=0; i<=selected_records.length-1;i++){
                    selected_ids.push(selected_records[i].data.id)
                }
                var renderer = this.renderer;
                var record = this.model.get(this.handle);
                var fields = renderer.columns.filter(function (field) {
                    return field.tag == "field";
                });
                var fieldData = _.map(fields, function (field) {
                    var name = field.attrs.name;
                    var description = field.attrs.widget ?
                        renderer.state.fieldsInfo.list[name].Widget.prototype.description :
                        field.attrs.string || renderer.state.fields[name].string;
                    return {name: name, label: description || name}
                });
                var data = {
                    import_compat: false,
                    model: record.model,
                    fields: fieldData,
                    ids: selected_ids || [],
                    domain: record.getDomain(),
                    context: record.getContext(),
                }

                framework.blockUI();
                session.get_file({
                    url: '/web/export/xls',
                    data: {data: JSON.stringify(data)},
                    complete: framework.unblockUI,
                    error: crash_manager.rpc_error.bind(crash_manager)
                });

            },

            create_payout:function(){
                var self =this
                

                var action = {
                    'type': 'ir.actions.act_window',
                    'name': _t('Create payout'),
                    'res_model': 'rb_delivery.add_payout_order',
                    'views': [[false, 'form']],
                    'view_type': 'form',
                    'view_mode': 'tree,form',
                    'target': 'new',
                    'context':{}
                };

                return this.do_action(action,{on_close:()=>{self.reload()} });


               },   
            
            change_state_action:function(){
                var self =this
                var user = session.uid;
                var records = _.map(self.selectedRecords, function (id) {
                    return self.model.localData[id];
                });
                var ids = _.pluck(records, 'res_id');

                if(ids.length<1) { alert("Please select Items / الرجاء اختيار عناصر");return }


                var self = this;

                var context = {"active_ids":ids}

                var action = {
                    'type': 'ir.actions.act_window',
                    'name': _t('Select State'),
                    'res_model': 'rb_delivery.select_state',
                    'views': [[false, 'form']],
                    'view_type': 'form',
                    'view_mode': 'tree,form',
                    'target': 'new',
                    'context':context
                };

                return this.do_action(action,{on_close:()=>{self.reload()} });


               },
               public_order_link_action:function(){
                var self =this

                var self = this;

                var action = {
                    'type': 'ir.actions.act_window',
                    'name': _t('Public Order Form Link'),
                    'res_model': 'rb_delivery.public_order_link',
                    'views': [[false, 'form']],
                    'view_type': 'form',
                    'view_mode': 'tree,form',
                    'target': 'new'
                };

                return this.do_action(action,{on_close:()=>{self.reload()} });


               },
               change_state_for_collections:function(model){
                var self =this
                var user = session.uid;
                var records = _.map(self.selectedRecords, function (id) {
                    return self.model.localData[id];
                });
                var ids = _.pluck(records, 'res_id');

                if(ids.length<1) { alert("Please select Items / الرجاء اختيار عناصر");return }


                var self = this;

                var context = {"active_ids":ids}

                var action = {
                    'type': 'ir.actions.act_window',
                    'name': _t('Select State'),
                    'res_model': model,
                    'views': [[false, 'form']],
                    'view_type': 'form',
                    'view_mode': 'tree,form',
                    'target': 'new',
                    'context':context
                };

                return this.do_action(action,{on_close:()=>{self.reload()} });

               },

            //change money collection state
               change_money_collection_state_action:function(){
                var model ="rb_delivery.select_money_collection_state"
                this.change_state_for_collections(model)
               },
            //returned collection
               change_returned_collection_state_action:function(){
                var model='rb_delivery.select_returned_collection_state'
                this.change_state_for_collections(model)
               },
            //agent returned collection
               change_agent_returned_collection_state_action:function(){
                var model='rb_delivery.select_agent_returned_money_collection_state'
                this.change_state_for_collections(model)
               },
            //agent collection
               change_agent_collection_state_action:function(){
                var model='rb_delivery.select_agent_money_collection_state'
                this.change_state_for_collections(model)
               },
            //run sheet collection
               change_runsheet_collection_state_action:function(){
                var model='rb_delivery.select_runsheet_state'
                this.change_state_for_collections(model)
               },


               show_orders_action:function(){
                    var self =this
                    var user = session.uid;
                    var records = _.map(self.selectedRecords, function (id) {
                        return self.model.localData[id];
                    });
                    var ids = _.pluck(records, 'res_id');
                    if(ids.length<1) { alert("Please select Items / الرجاء اختيار عناصر") ;return}

                    if(confirm("Are you sure to continue? ")){

                    var self = this;
                    var model_name = self.modelName;
                    var args1 = [[['id','in',ids]]]
                    if(model_name == 'rb_delivery.order_logs'){
                        self._rpc({
                            model: model_name,
                            method: 'search_read',
                            args: args1,
                            fields: ['order_id']
                            }).then(function(order_logs){
                                var order_ids = []
                                if(order_logs.length>0){
                                    for(var i=0;i<order_logs.length;i++){
                                        order_ids.push(order_logs[i].order_id[0])
                                    }
                                    if(order_ids.length>0){
                                        self.do_action({
                                            type: 'ir.actions.act_window',
                                            name: 'Orders',
                                            res_model: 'rb_delivery.order',
                                            view_type: 'form',
                                            view_mode: 'list,form',
                                            views: [[false, 'list'], [false, 'form']],
                                            target: 'current',
                                            domain: [['id', 'in', order_ids]]
                                        });
                                    }
                                    else{
                                        alert(_t("There are no orders in selected collections")) ;return
                                    }
                                }
                            })
                    }
                    else{
                        self._rpc({
                        model: model_name,
                        method: 'search_read',
                        args: args1,
                        fields: ['order_ids']
                        }).then(function(collection_orders){
                            var order_ids = []
                            if(collection_orders.length>0){
                                for(var i=0;i<collection_orders.length;i++){
                                    var collection_order_ids = collection_orders[i].order_ids
                                    for(var j=0; j<collection_order_ids.length;j++){
                                        order_ids.push(collection_order_ids[j])
                                    }
                                }
                                if(order_ids.length>0){
                                    self.do_action({
                                        type: 'ir.actions.act_window',
                                        name: 'Orders',
                                        res_model: 'rb_delivery.order',
                                        view_type: 'form',
                                        view_mode: 'list,form',
                                        views: [[false, 'list'], [false, 'form']],
                                        target: 'current',
                                        domain: [['id', 'in', order_ids]]
                                    });
                                }
                                else{
                                    alert(_t("There are no orders in selected collections")) ;return
                                }
                            }
                        })
                    }

                }
           },


               print_a5_action:function(){
                var self =this
                var user = session.uid;
                var records = _.map(self.selectedRecords, function (id) {
                    return self.model.localData[id];
                });
                var ids = _.pluck(records, 'res_id');
                if(ids.length<1) { alert("Please select Items / الرجاء اختيار عناصر") ;return}

                if(confirm("Are you sure to continue? ")){

                var self = this;
                var reportname = 'rb_delivery.order_detail?docids=' +
                                  ids +
                                  '&report_type=qweb-pdf&model_name=rb_delivery.order';
                var action = {
                    'type': 'ir.actions.report',
                    'report_type': 'qweb-pdf',
                    'report_name': reportname,
                    'report_file': 'rb_delivery.order_detail',
                };
                return this.do_action(action,{on_close:()=>{self.reload()} });

                }
               },

               executeQuickAccessAction: function (buttonOption) {
                var self = this;
                var user = session.uid;
                var records = _.map(self.selectedRecords, function (id) {
                    return self.model.localData[id];
                });
                var ids = _.pluck(records, 'res_id');
                if(ids.length < 1) { alert("Please select Items / الرجاء اختيار عناصر"); return; }

                // Handling for print button actions
                if (buttonOption.is_print_button) {
                    if(confirm("Are you sure to continue?")) {
                        var action = {
                            'type': 'ir.actions.report',
                            'report_type': 'qweb-pdf',
                            'report_name': buttonOption.report_file,
                            'activeIds': ids,
                            'report_file': buttonOption.report_file,
                        };
                        return this.do_action(action, {on_close: () => { self.reload(); }});
                    }
                }
                // Handling for change status button actions
                else if (buttonOption.is_change_status_button) {

                    if (buttonOption.role_access_status === false) {
                        var context = {"active_ids": ids} // Include the target status in the context if needed
                    }
                    else{
                        var context = {"active_ids": ids, "default_state": buttonOption.status}; // Include the target status in the context if needed

                    }


                    var action = {
                        'type': 'ir.actions.act_window',
                        'name': _t('Select State'), // You might want to customize this title based on the action or status
                        'res_model': 'rb_delivery.select_state', // Ensure this is the correct model for your status change wizard
                        'views': [[false, 'form']],
                        'view_type': 'form',
                        'view_mode': 'form', // Changed to form since it's a wizard
                        'target': 'new',
                        'context': context,
                    };
                    return this.do_action(action);
                }
            },
            print_a4_action: function (state) {
                var self =this
                var user = session.uid;
                var records = _.map(self.selectedRecords, function (id) {
                    return self.model.localData[id];
                });
                var ids = _.pluck(records, 'res_id');
                if(ids.length<1) { alert("Please select Items / الرجاء اختيار عناصر") ;return }

                if(confirm("Are you sure to continue? ")){

                var self = this;
                var reportname = 'rb_delivery.order_detail_a4?docids=' +
                                  ids +
                                  '&report_type=qweb-pdf&model_name=rb_delivery.order';
                var action = {
                    'type': 'ir.actions.report',
                    'report_type': 'qweb-pdf',
                    'report_name': reportname,
                    'report_file': 'rb_delivery.order_detail_a4',
                };
                return this.do_action(action,{on_close:()=>{self.reload()} });

                }
            },
        _onLiveMode: function(event) {
            if(globalSelf != undefined)
            {
                var self = this
                if(!live){
                    live = true
                    interval = setInterval(function() {
                        var widget = globalSelf.action_manager;
                        var controller = widget.getCurrentController();
                        if(controller.widget.viewType == 'list' && self.selectedRecords.length === 0 && (model_name == 'rb_delivery.order' || model_name == 'rb_delivery.order_draft')){
                        controller.widget.reload();
                        }
                        if(model_name != 'rb_delivery.order' && model_name != 'rb_delivery.order_draft'){
                            live = false
                            clearInterval(interval)
                        }
                    },5000);
                }
                else{
                    live = false
                    if(interval != undefined){
                        clearInterval(interval)
                    }
                }
            }
        },
        sample_excel_action_def: function () {
            var url = '/invoicing/excel_report/' + this.modelName;

            var win = window.open(url, '_blank');

            if (!win || win.closed || typeof win.closed === 'undefined') {
                this.do_warn(_t("Download Blocked"),_t("Your browser blocked the download. Please enable popups or allow downloads."));
            }


            },
                filter:function(){
                    this.searchView.do_search()
               },

               clear_date:function(){
                 $(".from_date").val('').change()
                 $(".to_date").val('').change()
                 this.searchView.do_search()
            },
            _getViewStorageKey: function () {
                return this.viewClass._getViewStorageKey();
            },
            _setViewStorageData: function (data) {
                this.call('local_storage', 'setItem', this._getViewStorageKey(), data);
            },
            _createFieldsDropdown: function () {
                var state = this.model.get(this.handle);
                return new FieldDropdown(this, state.fields, state.fieldsInfo[this.viewType],
                        this.renderer.arch.children, this.viewClass);
            },
            _updateFields: function (event) {
                event.stopPropagation();
                var state = this.model.get(this.handle);
                state.fieldsInfo[this.viewType] = event.data.info;
                var updated_fields = []
                var uniqueIdsFields = new Set();
                var combined_children = [...this.renderer.arch.children, ...event.data.arch];
                for (let i = 0; i < combined_children.length; i++) {
                    let child = combined_children[i];
                    if (!uniqueIdsFields.has(child.attrs.name)) {
                        uniqueIdsFields.add(child.attrs.name);
                        if (child.tag == 'button') {
                            event.data.arch.push(child);
                        } else if (!child.attrs.invisible) {
                            updated_fields.push(child.attrs.name);
                        }
                    }
                }
                this.renderer.arch.children = event.data.arch;
                this._setViewStorageData(this.renderer.arch);
                if(this.modelName == 'rb_delivery.order' && this.searchView && this.searchView.action && ((this.searchView.action.xml_id && this.searchView.action.xml_id != 'olivery_live_map.action_olivery_live_map_order_map') || !this.searchView.action.xml_id) && session.uid != 1 && session.uid != 2)
                {rpc.query({
                        model: 'rb_delivery.user',
                        method: 'write_fields',
                    args: [[],updated_fields]
                }).then(function (data){})}
                this.update({}, {reload: true})
            },
            _updateButtons: function (mode) {
                this._super.apply(this, arguments);
                if (this.$mode_switch) {
                    this.$mode_switch.find('input[type="checkbox"]').prop('checked', !!this.editable);
                }
            },
            _onSwitchMode: function(event) {
                var editable = $(event.currentTarget).is(':checked');
                if(editable) {
                    $(".o_list_button_add").attr("style", "display: none !important;");
                    this.editable = 'top';
                    this.mode = "readonly";
                    this.renderer.mode = this.mode;
                    this.renderer.editable = this.editable;
                } else {
                    $(".o_list_button_add").attr("style", "display: inline-block !important;");
                    this.editable = false;
                    this.mode = "readonly";
                    this.renderer.mode = this.mode;
                    this.renderer.editable = false;
                }
                this.update({}, {reload: true}).then(this._updateButtons.bind(this, 'readonly'));
            },
            create_missing_tags:function(){
                var self = this
                rpc.query({
                    model: 'rb_delivery.address_tags',
                    method: 'create_missing_address_tags',
                    args: []
                }).then(function (data){
                    self.update({}, {reload: true})
                })



            },
    });
})






