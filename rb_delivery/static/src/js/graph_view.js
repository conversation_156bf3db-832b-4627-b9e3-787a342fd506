odoo.define('rb_delivery.graph_view_custom', function (require) {
	"use strict";
    var GraphView = require('web.GraphView');

    GraphView.include({
        init: function (viewInfo, params) {
            this._super.apply(this, arguments);
            if (this.loadParams && this.loadParams.domain && this.loadParams.domain.length>0){
                for(let i=0; i<= this.loadParams.domain.length-1; i++){
                    let domain_item = this.loadParams.domain[i]
                    if(domain_item.includes('business_id')){
                        this.loadParams.groupBys = ['order_status','business_id'];
                    }
                    else if(domain_item.includes('driver_id')){
                        this.loadParams.groupBys = ['order_status','driver_id'];
                    }
                }
            }
        },
    });
})