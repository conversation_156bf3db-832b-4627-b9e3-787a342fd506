odoo.define('rb_delivery.filters', function (require) {

    "use strict";

    var core = require('web.core');
    var _t = core._t;
    var rpc = require('web.rpc');
    var SearchView = require('web.SearchView');
    var filter_domain = ''
    var partial_search=false
    var shownBox = false
    var session = require('web.session');
    SearchView.include({
        toggle_buttons: function (is_visible) {
            this.visible_filters = true;
            if (this.$buttons) {
                this.$buttons.toggle(this.visible_filters);
            }
        },

        start: function () {
            if (session && 'default_search' in session && session['default_search']){
                var data = session['default_search']
                if('search_default' in data && data['search_default']){
                    filter_domain = data['search_default']
                }
                if('partial_search' in data && data['partial_search']){
                    partial_search = data['partial_search']
                }
            }
            return this._super.apply(this, arguments);

        },
        add_filter_domain:function(domain,_query,operator){
            if (domain == 'sequence') {
                _query.attributes.field.attrs.filter_domain = "[('sequence','"+operator+"',self)]"
            }
            else if (domain == 'reference') {
                _query.attributes.field.attrs.filter_domain = "[('reference_id','"+operator+"',self)]"
            }
            else if (domain == 'barcodes') {
                _query.attributes.field.attrs.filter_domain = "['|','|','|','|',('follower_ref_id','"+operator+"',self),('reference_id','"+operator+"',self),('sequence','"+operator+"',self),('partner_reference_id','"+operator+"',self),('follow_up_order_sequences','"+operator+"',self)]"
            }
            else if (domain == 'all') {
                _query.attributes.field.attrs.filter_domain = "['|','|','|','|',('sequence','ilike',self),('reference_id','ilike',self),'|','|','|',('state_id','ilike',self),('customer_name','ilike',self),('customer_mobile','ilike',self),('previous_customer_mobile_number','ilike',self),'|','|','|',('assign_to_business','ilike',self),('commercial_name','ilike',self),('user_sequence','ilike',self),('business_mobile_number','ilike',self),'|','|','|','|','|',('assign_to_agent','ilike',self),('customer_area','ilike',self),('customer_sub_area','ilike',self),('customer_address','ilike',self),('follower_ref_id','ilike',self),('follow_up_order_sequences','ilike',self)]"
            }
            else if (domain == 'partner_reference') {
                _query.attributes.field.attrs.filter_domain = "[('partner_reference_id','"+operator+"',self)]"
            }
        },

        remove_warn_box: function(searchBox,arrow,box,warnBox){
            shownBox = false
            if(arrow && arrow.length > 0 && arrow[0]){
                arrow[0].style.display = "none";
                searchBox.classList.remove("red-border");
            }
            if(box && box.length >0 && box[0]){
                box[0].style.display = "none"

            }
            if(warnBox && warnBox.length >0 && warnBox[0]){
                warnBox[0].style.display = "none"

            }
        },

        add_warn_box: function(defaultFilter,searchBox,arrow,warnArrow,box,warnBox){
            //Add class for red border
            searchBox.classList.add("red-border");
            shownBox = true
            //Add warning box
            if(box.length == 0){
                box = document.createElement('div');
                box.className = 'box';
                var boxText = document.createElement('div')
                boxText.className = 'boxText';
                boxText.textContent = _t("You are using partial search and exceeded the limit of 20 searches which will cause your search experience to be slower, so to continue your search please select 'Barcodes' from the menu on the side.");
                box.appendChild(boxText);
            }
            else{
                box = box[0]
                box.style.display = "flex";
            }
            if(warnBox.length == 0){
                warnBox = document.createElement('div');
                warnBox.className = 'warn-box';
                var warn_boxText = document.createElement('div')
                warn_boxText.className = 'warn-box-text';
                warn_boxText.textContent = _t("Choose 'Barcodes' from here.");
                warnBox.appendChild(warn_boxText);
            }
            else{
                warnBox = warnBox[0]
                warnBox.style.display = "flex";
            }

            //Add arrow for the box
            if (arrow.length == 0){
                arrow = document.createElement('div');
                arrow.className = 'arrow-red';
                searchBox.after(box);
                searchBox.after(arrow);

            }
            else{
                arrow[0].style.display = "block";
            }
            if (warnArrow.length == 0){
                warnArrow = document.createElement('div');
                warnArrow.className = 'arrow';
                defaultFilter.before(warnArrow)
                defaultFilter.before(warnBox)

            }
            else{
                warnArrow[0].style.display = "block";
            }

            //Add close button
            var closeButton = document.getElementsByClassName('close-btn');
            var warnCloseButton = document.getElementsByClassName('warn-close-btn');
            var warningSign = document.getElementsByClassName('warn-sign');
            var greenWarningSign = document.getElementsByClassName('green-warn-sign');
            if (greenWarningSign.length == 0){
                greenWarningSign = document.createElement('img');
                greenWarningSign.src = 'rb_delivery/static/src/img/green-warn.png';
                greenWarningSign.className = 'green-warn-sign';
                warnBox.insertBefore(greenWarningSign, warnBox.firstChild);
            }
            if (warningSign.length == 0){
                warningSign = document.createElement('img');
                warningSign.src = 'rb_delivery/static/src/img/warn_icon.png';
                warningSign.className = 'warn-sign';
                box.insertBefore(warningSign, box.firstChild);
            }
            if (closeButton.length == 0){
                closeButton = document.createElement('button');
                closeButton.textContent = 'x';
                closeButton.className = 'close-btn';
                box.appendChild(closeButton);
                var self = this
                closeButton.addEventListener('click', function() {
                    self.remove_warn_box(searchBox,[arrow],[box],[warnBox])
                });
            }
            if (warnCloseButton.length == 0){
                warnCloseButton = document.createElement('button');
                warnCloseButton.textContent = 'x';
                warnCloseButton.className = 'warn-close-btn';
                warnBox.appendChild(warnCloseButton);
                var self = this
                warnCloseButton.addEventListener('click', function() {
                    self.remove_warn_box(searchBox,[arrow],[box],[warnBox])
                });
            }

        },
        do_search: function (_query, options) {
            var query_length = 0
            if(this.query && this.query.length){
                query_length = this.query.length
            }
            var searchBox = document.getElementsByClassName('o_searchview');
            var defaultFilter = document.getElementById('default_filter');


            if(searchBox.length>0){
                searchBox = searchBox[0]
            }
            var arrow = document.getElementsByClassName('arrow-red')
            var warnArrow = document.getElementsByClassName('arrow')
            var box = document.getElementsByClassName('box')
            var warnBox = document.getElementsByClassName('warn-box')
            if(!shownBox || query_length==0 || (this.action && this.action.res_model && this.action.res_model !='rb_delivery.order')){
                this.remove_warn_box(searchBox,arrow,box,warnBox)
            }
            if (defaultFilter){
                var self = this;
                defaultFilter.addEventListener("change", function() {
                    const selectedValue = defaultFilter.value;
                    if(selectedValue && selectedValue == 'barcodes'){
                        self.remove_warn_box(searchBox,arrow,box,warnBox)
                    }
                });
            }

            var from_date = ''
            var to_date = ''
            var modelName = ''
            var filter_value = ''
            if (this.action.env && this.action.env.modelName) {
                modelName = this.action.env.modelName
            }
            if (modelName == 'rb_delivery.order' || modelName == 'rb_delivery.multi_print_orders_money_collector' || modelName=='rb_delivery.agent_money_collection' || modelName=='rb_delivery.runsheet' || modelName=='rb_delivery.returned_money_collection' || modelName=='rb_delivery.agent_returned_collection') {
                from_date = $(".from_date").val()
                to_date = $(".to_date").val()
                var domain = []

                if (_query && _query.attributes && _query.attributes.field && _query.attributes.field.attrs && _query.attributes.field.attrs.name) {
                    if (_query.attributes.field.attrs.name == "default") {
                        filter_value = $('#default_filter').val()
                        var operator = 'ilike'
                        if(!partial_search){
                            operator = '='
                        }
                        if (filter_value == 'default' && partial_search && _query.values && _query.values.length > 0 && options.length>20) {
                            this.add_warn_box(defaultFilter,searchBox,arrow,warnArrow,box,warnBox)
                            return
                        }
                        else{
                            this.remove_warn_box(searchBox,arrow,box,warnBox)
                        }
                        if(filter_value == 'default'){
                            this.add_filter_domain(filter_domain,_query,operator)
                        }
                        else{
                            this.add_filter_domain(filter_value,_query,'=')
                        }
                    }
                }

                if (this.action && this.action.domain) {
                    domain = this.action.domain;
                }

                if (to_date != '' && from_date != '' && to_date != undefined && from_date != undefined) {
                    from_date = new Date(from_date)
                    to_date = new Date(to_date)
                    if (from_date > to_date) {
                        alert(_t("From date can't be less than to date.")); return
                    }
                    else {
                        var filter_as = $('#filter_as').val()
                        if (filter_as == undefined) {
                            filter_as = 'create_date'
                        }
                        var date = ('0' + from_date.getDate()).slice(-2);
                        var month = ('0' + (from_date.getMonth() + 1)).slice(-2);
                        var year = from_date.getFullYear();
                        var time = year + '-' + month + '-' + date + ' ' + '00:00:00';
                        var timeZone = 'Asia/Hebron'
                        if (this.action.context.tz) {
                            timeZone = this.action.context.tz
                        }
                        time = moment.utc(time, 'YYYY-MM-DD HH:mm:ss').local(timeZone);
                        var date_from = [filter_as, '>=', time]
                        var date = ('0' + to_date.getDate()).slice(-2);
                        var month = ('0' + (to_date.getMonth() + 1)).slice(-2);
                        var year = to_date.getFullYear();
                        var time = year + '-' + month + '-' + date + ' ' + '23:59:59';
                        time = moment.utc(time, 'YYYY-MM-DD HH:mm:ss').local(timeZone);
                        var date_to = [filter_as, '<', time];
                        var saved_date_from = JSON.parse(localStorage.getItem('saved_date_from'));
                        var saved_date_to = JSON.parse(localStorage.getItem('saved_date_to'));
                        domain = domain.filter(item => JSON.stringify(item) !== JSON.stringify(saved_date_from) && JSON.stringify(item) !== JSON.stringify(saved_date_to));
                        domain.push(date_from);
                        domain.push(date_to);
                        localStorage.setItem('saved_date_from', JSON.stringify(date_from));
                        localStorage.setItem('saved_date_to', JSON.stringify(date_to));
                        this.action.domain = domain;
                    }

                }
            }
            this._super.apply(this, arguments);
            let  from_date_value=$(".from_date").val()
            let to_date_vale=$(".to_date").val()
            var clearButton = sessionStorage.getItem('filter_by_date_cleared');
             if(from_date_value==false && to_date_vale==false && clearButton === "true" && domain.length>1){
                domain.pop()
                domain.pop()
                sessionStorage.setItem('filter_by_date_cleared', false);
                this.do_search()

            }

        }
    })
})
