odoo.define('rb_delivery.Dialog', function (require) {
    "use strict";

    var Dialog = require('web.Dialog');
    var ajax = require('web.ajax');
    var _t = require('web.core')._t;
    Dialog.include({
        init: function (parent, options) {
            this._super.apply(this, arguments);
            if (options && 'title' in options && options['title'] != undefined && options['title'].includes('Odoo') || this.$content && this.$content[0] && this.$content[0].innerHTML && this.$content[0].innerHTML.includes('<button class="btn btn-primary float-right ml8 o_clipboard_button"')){
                var Copy_error = _t('Copy error');
                var clearCache_btn = _t('Clear Cache');
                const copyButton = {
                    text: Copy_error,
                    classes: 'btn-secondary', // Use your desired class for styling
                    click: function () {
                        navigator.clipboard.writeText(error).then(() => {
                            const button = document.querySelector('.olivery-dialog-button-secondary'); // Adjust this selector as needed
                            if (button) {
                                const originalText = button.textContent; // Save the original text
                                button.textContent = Copied;

                                setTimeout(() => {
                                    button.textContent = originalText;
                                }, 2000);
                            }
                        }).catch(err => {
                            alert('something went wrong');
                        });
                    }
                };
                const clearCacheButton = {
                    text: clearCache_btn,
                    classes: 'btn-secondary olivery-dialog-button-primary',
                    click: function () {
                        ajax.jsonRpc('/web/session/destroy', 'call', {}).then(function () {
                            document.cookie.split(";").forEach(function(c) {
                                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                            });
                            localStorage.clear();
                            sessionStorage.clear();
                            window.location = '/web';
                        }).fail(function () {
                            localStorage.clear();
                            sessionStorage.clear();
                            window.location.reload();
                        });;
                    }
                };
                if ( options && 'subtitle' in options && options['subtitle'] != undefined && options['subtitle'].includes('[Error]') || options['subtitle'].includes('[Warning]') || options['subtitle'].includes('[Success]')){
                    options['title'] = options['title'].replace("Odoo","Olivery")
                    options['subtitle'] = options['subtitle'].substring(2);
                    var Copied = _t('Copied!');
                    var Quick_Tip = _t('Quick Tip');
                    var title = options['subtitle'].match(/\[\[(.*?)\]\]/)[1];
                    options['subtitle'] = options['subtitle'].replace(/\{\[\[(.*?)\]\]\}/, "")
                    var hint = options['$content'][0].innerHTML.replace("\n", "")
                    var setContent = false;
                    var error = 'Error title: ' + title + "\n error: " + options['subtitle'].replace('[Warning]', '') + "\n What to do next:" +hint
                    if (options['subtitle'].includes('[Error]')){
                        if (options['buttons']) {
                            options['buttons'].splice(1, 0, copyButton);
                            options['buttons'].splice(2, 0, clearCacheButton);
                        }
                        options['title'] = '<div class="olivery-dialog-header"><img src="rb_delivery/static/src/img/errorAsset.svg" alt="Error icon" class="img-title-top" width="24px" height="24px"/><span class="title-top-dialog">&#9;'+title+'</span></div> <span class="olivery-dialog-message-error">'+options['subtitle'].replace('[Error]', '')+' </span>'
                        setContent = true;
                    }
                    else if(options['subtitle'].includes('[Warning]')){
                        if (options['buttons']) {
                            options['buttons'].splice(1, 0, copyButton);
                        }
                        options['title'] = '<div class="olivery-dialog-header"><img src="rb_delivery/static/src/img/warningAsset.svg" alt="Warning icon" class="img-title-top" width="24px" height="24px"/><span class="title-top-dialog">&#9;'+title+'</span></div> <span class="olivery-dialog-message-warning">'+options['subtitle'].replace('[Warning]', '')+' </span>'
                        setContent = true;
                    }
                    else if(options['subtitle'].includes('[Success]')){
                        if (options['buttons']) {
                            options['buttons'].splice(1, 0, copyButton);
                        }
                        options['title'] = '<div class="olivery-dialog-header"><img src="rb_delivery/static/src/img/successAsset.svg" alt="Success icon" class="img-title-top" width="24px" height="24px"/><span class="title-top-dialog">&#9;'+title+'</span></div> <span class="olivery-dialog-message-success">'+options['subtitle'].replace('[Success]', '')+' </span>'
                        options['$content'] = ""
                    }
                    if(setContent) {
                        options['$content'] = '<div class="olivery-hint-title"><img src="rb_delivery/static/src/img/tipAsset.svg" alt="hint logo" class="olivery-hint-logo width="24px" height="24px"" /> <span class="olivery-hint-text"> '+Quick_Tip+' </span> </div><div class="olivery-hint-desc"> <span class="olivery-hint-desc-text">' + options['$content'][0].innerHTML + '</span></div>'
                    }
                    options['subtitle'] = ""
                    this.title = options['title']
                    this.$content = options['$content']
                    const keys = Object.keys(options);
                    for (const key of keys) {
                        if (key != undefined && key.includes('buttons')){
                            for (var i = 0; i < options['buttons'].length; i++){
                                if(i == 0) {
                                    options['buttons'][i]['classes'] = 'olivery-dialog-button-primary'
                                } else {
                                    options['buttons'][i]['classes'] = 'olivery-dialog-button-secondary'
                                }
                            }
                            break;
                        }
                    }
                    this.subtitle = options['subtitle'];
                    this.buttons = options['buttons']
                } else if(options && 'buttons' in options && options['buttons'] && this.$content && this.$content[0] && this.$content[0].innerHTML && this.$content[0].innerHTML.includes('<button') == true) {
                    options['buttons'].splice(1, 0, copyButton);
                    options['buttons'].splice(2, 0, clearCacheButton);

                    const modalContent = this.$content[0];
                    const footerSection = document.createElement('div');
                    footerSection.classList.add('modal-clear-cache-section');
                    let helpText = _t('To solve the issue please try clearing the cache by clicking on the clear cache button bellow')
                    footerSection.innerHTML = `
                        <div class="clear-cache-info">
                            <span style="font-size: 18px;color: red;font-family: 'cairo' !important;font-weight: bold;">
                                ${helpText}
                            </span>
                        </div>`;
                    modalContent.appendChild(footerSection);

                    const keys = Object.keys(options);
                    for (const key of keys) {
                        if (key.includes('buttons')){
                            for (var i = 0; i < options['buttons'].length; i++){
                                if(i == 0) {
                                    options['buttons'][i]['classes'] = 'olivery-dialog-button-primary'
                                } else {
                                    options['buttons'][i]['classes'] = 'olivery-dialog-button-secondary'
                                }
                            }
                            break;
                        }
                    }
                    options['title'] = options['title'].replace("Odoo","Olivery")
                    this.title = options['title']
                } else {
                    options['title'] = options['title'].replace("Odoo","Olivery")
                    this.title = options['title']
                }
            }
        },
    });
    


    });