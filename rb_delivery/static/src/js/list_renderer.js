odoo.define('rb_delivery.listrenderer', function (require) {
    "use strict";

    var session = require('web.session');

    function get_month(month){
        let monthData = get_month_number(month)
        if (session && 'use_month_names_in_group_by' in session && session['use_month_names_in_group_by'] && monthData && monthData.length > 1){
            let lang = session.user_context.lang;
            lang = lang.replace('_', '-')
            if (lang == 'ar-SY') {
                lang = 'ar-EG'
            }
            let monthName = new Date(2023, monthData[0] - 1).toLocaleString(lang, { month: 'long' });

            return month.replace(monthData[1], monthName)

        } else if (monthData && monthData.length > 1 && monthData[0]) {

            return month.replace(monthData[1], monthData[0])

        }

    }

    function get_month_number(month) {
        var months = [['1','January','Jan','يناير','كانون الثاني'],['2','February','Feb','فبراير','شباط'],['3','March','Mar','مارس','آذار'],['4','April','Apr','أبريل','نيسان'],['5','May','May','مايو','أيار'],['6','June','Jun','يونيو','حزيران'],['7','July','Jul','يوليو','تموز'],['8','August','Aug','أغسطس','آب'],['9','September','Sep','سبتمبر','أيلول'],['10','October','Oct','أكتوبر','تشرين الأول'],['11','November','Nov','نوفمبر','تشرين الثاني'],['12','December','Dec','ديسمبر','كانون الأول']]
        for(let i=0;i<months.length;i++){
            var matches = month.replace(/[0-9]/g, '');
            matches = matches.trim()
            if(matches == months[i][1]||matches == months[i][2]||matches == months[i][3]||matches == months[i][4]){
                return [months[i][0], matches]
            }
        }
        return NaN
    }

    function all_groups_btn(self, currentGroup) {
        return $('<button/>', {
            class: 'all-group-rows-btn',
            text: 'All',
            click: function (ev) {
                currentGroup.limit = currentGroup.count;
                currentGroup.offset = 0;
                self._render().then(()=>{
                    const $pagerValueElement = $(".o_pager_value");
                    const $groupPagerElement = $(".o_group_pager"); 
                    $pagerValueElement.click();
                    $pagerValueElement.fadeOut();
                    $groupPagerElement.css('display', 'none');
                   
                })
                
            }
        });
    }
    
    var core = require('web.core');
    var ListRenderer = require('web.ListRenderer');

    ListRenderer.include({
        
        _renderGroupRow: function (group, groupLevel) {
            if(this.state && group && group && group.context && group.context['group_by'] && typeof group.value == 'string'){
                if(group.context['group_by']){
                    if(group.value!==false){
                        var value = get_month(group.value)
                        if(value)
                        {group.value = value }   
                    }
                }
            }

            const self = this;
            const currentGroup = group;
            const $groupRow = this._super.apply(this, arguments);
            
            const $button = all_groups_btn(self, currentGroup)
           
            if(group && group.isOpen && group.count>group.limit) {
                $button.css({
                    display:"block"
                })
                const $div = $('<div class="olivery_group_by_arrows" style="display: flex;width: 150%;align-items:center;position: sticky;left:120px;padding-top:5px"></div>');
                const $lastTd = $groupRow.find('td:last');
                const lastTdContent = $lastTd.contents().detach();
                $div.append(lastTdContent).append($button);
                $groupRow.find('.o_group_name').append($div);

                const $firstTd = $groupRow.find('td:first');
                $firstTd.addClass('first-td-in-tree');
            }


            
            
            
            return $groupRow;       
        },
        _renderBodyCell: function (record, node, colIndex, options) {
            if('tag' in node && node['tag'] && node['tag'] !== '' && node['tag'] !== undefined ){
                var $td = this._super.apply(this, arguments);
                if(node && node.attrs && node.attrs.name && (node.attrs.name === 'advanced_tags' || node.attrs.name === 'is_in_collection' || node.attrs.name === 'is_agent_collection' || node.attrs.name === 'is_in_branch_financial_collection')){
                    if($td && $td[0] && $td[0].innerText){
                        $td[0].innerHTML = $td[0].innerText
                    }
                    $td.addClass('smallTd');
                }
                if(node && node.attrs && node.attrs.class && node.attrs.class === 'smallTd') {
                    if($td && $td[0] && $td[0].innerText){
                        $td[0].innerHTML = $td[0].innerText
                    }
                    $td.addClass('smallTd');
                } else if (node && node.attrs && node.attrs.class && node.attrs.class === 'largeTd') {
                    if($td && $td[0] && $td[0].innerText){
                        $td[0].innerHTML = $td[0].innerText
                    }
                    $td.addClass('largeTd');
                }
                return $td
            }
            else{
                options.renderInvisible = false
                node.attrs.invisible = true
                node.attrs.modifiers.invisible = true
                node.attrs.modifiers.column_invisible = true
                if(node && node.attrs && node.attrs.class && node.attrs.class === 'smallTd') {
                    $td.addClass('smallTd');
                } else if (node && node.attrs && node.attrs.class && node.attrs.class === 'largeTd') {
                    $td.addClass('largeTd');
                }
                return $td
            }
        },
    });
});