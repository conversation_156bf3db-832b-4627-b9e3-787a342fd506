odoo.define('rb_delivery.prevent_open_rec', function (require) {
    "use strict";

    var session = require('web.session');
    var Thread = require('mail.widget.Thread');


    Thread.include({
        _onClickRedirect: function (ev) {
            var $target = $(ev.target);
            var model = $target.data('oe-model');
            
            if (!session.is_admin && (model === 'res.users' || model === 'res.partner')) {
                ev.preventDefault();
                ev.stopPropagation();
                return false;
            }
            
            return this._super.apply(this, arguments);
        }
    });
});


