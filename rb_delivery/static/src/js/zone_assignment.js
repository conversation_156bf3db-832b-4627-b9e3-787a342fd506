odoo.define('rb_delivery.load_zoen', function (require) {
    "use strict";

    var KanbanController = require('web.KanbanController');
    var core = require('web.core');
    var _t = core._t;

    KanbanController.include({
        on_attach_callback: function () {
            this._super(...arguments);
        
            const self = this;
        
            if (this.modelName === 'rb_delivery.zone_assignment') {

                const observer = new MutationObserver(function () {
                    const $quickAdd = self.$('.o_kanban_quick_add');
                    const $settings = self.$('.o_kanban_config');
        
                    if ($quickAdd.length) {
                        $quickAdd[0].style.setProperty('display', 'none', 'important');
                    }
        
                    if ($settings.length) {
                        $settings[0].style.setProperty('display', 'none', 'important');
                    }
                });
        

                const targetNode = this.$el[0];
                observer.observe(targetNode, {
                    childList: true,
                    subtree: true
                });
        
                this._rpc({
                    model: 'rb_delivery.zone_assignment',
                    method: 'generate_zone_load_items',
                    args: [],
                }).then(() => {
                    this.reload();
                });
            }
        },
        
        renderButtons: function ($node) {
            this._super($node);
        
            if (this.modelName === 'rb_delivery.zone_assignment') {
                const self = this;
                if (!this.$buttons || this.$buttons.length === 0) {
                    this.$buttons = $('<div class="o_kanban_buttons btn-group"></div>');
                    this.$el.find('.o_kanban_view').before(this.$buttons);
                }
        
                const $generateBtn = $('<button type="button" class="btn btn-primary mr-2">' + _t("Refresh") + '</button>');
                $generateBtn.on('click', function () {
                    self._rpc({
                        model: 'rb_delivery.zone_assignment',
                        method: 'generate_zone_load_items',
                        args: [],
                    }).then(() => self.reload());
                });
        
        
                this.$buttons.append($generateBtn);
                this.$buttons.find('.o-kanban-button-new').remove();
            }
        },

    });

    var KanbanModel = require('web.KanbanModel');
    KanbanModel.include({
        save: function (recordID, changes, options) {
            const self = this;
            return this._super.apply(this, arguments).then(function (result) {
                if (result) {
                    return self._rpc({
                        model: 'rb_delivery.zone_assignment',
                        method: 'push_zone_changes_to_real_models',
                        args: [],
                    }).then(() => {
                        self.trigger_up('reload');
                        console.log("Zone sync complete after save");
                    });
                }
                return result;
            });
        }

    });
});
