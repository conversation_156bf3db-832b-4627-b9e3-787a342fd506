.o_menu_brand{
    margin:0px !important;
    padding:0px !important;
    height:70%  !important;

}

.dropdown-toggle{
    padding-right: 5px !important
}

.dropdown-toggle.o-no-caret.o_menu_header_lvl_1{
    padding-right: 10px !important
}

header .navbar-brand.logo img{
    width: 70px !important;
    height: 70px !important;
    min-width: 0px !important;
}
.fa-exclamation{
    font-size: 18px;
}
.o_main_navbar .dropdown-menu.show {
    max-height: 90vh !important;
    min-width: 10rem;
    overflow: auto !important;
    position: absolute !important;
    margin-top: 0 !important;
}
.o_data_cell.note
{
    color:#cc3300 !important;
    height: 60px !important;

}
header .navbar-brand.logo img {
    width: 100px !important;
    height: 100px !important;
    min-width: 0px !important;}

.fa-fw{
    color:red;
}
a.dropdown-toggle.o-no-caret{
    padding-left: 4px !important;
    padding-right: 4px !important;}


    .o_rtl .fa.fa-align-right, .o_rtl .fa.fa-align-left, .o_rtl .fa.fa-chevron-right, .o_rtl .fa.fa-chevron-left, .o_rtl .fa.fa-arrow-right, .o_rtl .fa.fa-arrow-left, .o_rtl .fa.fa-hand-o-right, .o_rtl .fa.fa-hand-o-left, .o_rtl .fa.fa-arrow-circle-right, .o_rtl .fa.fa-arrow-circle-left, .o_rtl .fa.fa-rotate-right, .o_rtl .fa.fa-rotate-left, .o_rtl .fa.fa-angle-double-right, .o_rtl .fa.fa-angle-double-left, .o_rtl .fa.fa-angle-right, .o_rtl .fa.fa-angle-left, .o_rtl .fa.fa-quote-right, .o_rtl .fa.fa-quote-left, .o_rtl .fa.fa-chevron-circle-right, .o_rtl .fa.fa-chevron-circle-left, .o_rtl .fa.fa-long-arrow-right, .o_rtl .fa.fa-long-arrow-left, .o_rtl .fa.fa-toggle-right, .o_rtl .fa.fa-toggle-left, .o_rtl .fa.fa-caret-square-o-right, .o_rtl .fa.fa-arrow-circle-o-left, .o_rtl .fa.fa-arrow-circle-o-right, .o_rtl .fa.fa-caret-square-o-left{
        transform: none !important;
    }

    ul.o_mail_thread_message_tracking li span:nth-child(2){
        display: inline-flex ;
    }
    ul.o_mail_thread_message_tracking{
    list-style-type: none;}


    .o_form_view .o_group .o_field_widget > .btn {
        -webkit-box-flex: 0;
        -webkit-flex: 0 0 auto;
        flex: 0 0 auto;
        padding: 0 5px !important;
    }
    @keyframes up-down {
        0%   { transform: translateY(0); }
        50%  { transform: translateY(-10px); }
        100% { transform: translateY(0); }
    }
    .warn-box {
        width: 10vw;
        height: 100px;
        background-color: white;
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;
        margin-top:50px;
        right:3%;
        z-index: 2;
        line-height: 1.5;
        border-radius: 5px;
        border: 1px solid white;
        font-size: 12px;
        box-shadow: 0px 3px 10px #00000033;
        top: 90px;
        animation: up-down 2s infinite ease-in-out;
    }

    .green-warn-sign{
        position: absolute;
        margin-bottom:55px
    }
    .warn-box-text{
        margin-top: 10px;
    }

    .arrow{
        position:relative;
        margin-top: 40px;
        left:6%;
        transform: translateX(-50%);
        width:0;
        height: 0;
        border-color: white transparent transparent;
        border-width: 10px 5px 0;
        border-style: solid;
        rotate:180deg
    }
    .arrow-red{
        position:absolute;
        transform: translateX(-50%);
        width:0;
        height: 0;
        border-color: #ffcc00 transparent transparent;
        border-width: 10px 5px 0;
        border-style: solid;
        rotate:180deg;
        margin-top: -10px;
    }
    .red-border{
        border: #ff4d4d 4px solid !important;
    }

    .box {
        height: 55px;
        background-color: #ffcc00;
        position: absolute;
        display: flex;
        align-items: center;
        padding: 10px;
        z-index: 2;
        line-height: 1.5;
        border-radius: 5px;
        border: 1px solid #ffcc00;
        font-size: 14px;
        width:75vw;
    }
    .warn-close-btn{
        position: absolute;
        bottom: 10px;
        right: 10px;
        cursor: pointer;
        font-size: 15px;
        background: none;
        border: none;
        outline: none;
        border-radius: 18px;
        height: 30px;
        width: 30px;
        color:black;
        background-color: transparent;
        font-weight: bold;
        right:0px;
        top:0px;
    }

    .close-btn {
        position: absolute;
        bottom: 10px;
        right: 10px;
        cursor: pointer;
        font-size: 15px;
        background: none;
        border: none;
        outline: none;
        border-radius: 18px;
        height: 30px;
        width: 30px;
        color:black;
        background-color: transparent;
        font-weight: bold;
    }
    .warn-sign{
        padding:10px;
        height:35px
    }


    .olivery_group_by_arrows {
        display: flex;
        width: 150%;
    }

    .clear-cache-info {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 17px;
    }