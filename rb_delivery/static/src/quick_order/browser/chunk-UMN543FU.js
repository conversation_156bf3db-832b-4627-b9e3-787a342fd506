var am=Object.create;var qi=Object.defineProperty,um=Object.defineProperties,lm=Object.getOwnPropertyDescriptor,cm=Object.getOwnPropertyDescriptors,dm=Object.getOwnPropertyNames,Ar=Object.getOwnPropertySymbols,fm=Object.getPrototypeOf,Gi=Object.prototype.hasOwnProperty,Kl=Object.prototype.propertyIsEnumerable;var Ql=(t,e,n)=>e in t?qi(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,ge=(t,e)=>{for(var n in e||={})Gi.call(e,n)&&Ql(t,n,e[n]);if(Ar)for(var n of Ar(e))Kl.call(e,n)&&Ql(t,n,e[n]);return t},It=(t,e)=>um(t,cm(e));var DC=(t=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(t,{get:(e,n)=>(typeof require<"u"?require:e)[n]}):t)(function(t){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+t+'" is not supported')});var Yl=(t,e)=>{var n={};for(var r in t)Gi.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&Ar)for(var r of Ar(t))e.indexOf(r)<0&&Kl.call(t,r)&&(n[r]=t[r]);return n};var EC=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var hm=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of dm(e))!Gi.call(t,o)&&o!==n&&qi(t,o,{get:()=>e[o],enumerable:!(r=lm(e,o))||r.enumerable});return t};var wC=(t,e,n)=>(n=t!=null?am(fm(t)):{},hm(e||!t||!t.__esModule?qi(n,"default",{value:t,enumerable:!0}):n,t));var pm=(t,e,n)=>new Promise((r,o)=>{var i=u=>{try{a(n.next(u))}catch(l){o(l)}},s=u=>{try{a(n.throw(u))}catch(l){o(l)}},a=u=>u.done?r(u.value):Promise.resolve(u.value).then(i,s);a((n=n.apply(t,e)).next())});function Zl(t,e){return Object.is(t,e)}var oe=null,qn=!1,Or=1,Qe=Symbol("SIGNAL");function P(t){let e=oe;return oe=t,e}function mm(){return qn}var Wn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Yi(t){if(qn)throw new Error("");if(oe===null)return;oe.consumerOnSignalRead(t);let e=oe.nextProducerIndex++;if(fn(oe),e<oe.producerNode.length&&oe.producerNode[e]!==t&&Gn(oe)){let n=oe.producerNode[e];Lr(n,oe.producerIndexOfThis[e])}oe.producerNode[e]!==t&&(oe.producerNode[e]=t,oe.producerIndexOfThis[e]=Gn(oe)?nc(t,oe,e):0),oe.producerLastReadVersion[e]=t.version}function gm(){Or++}function Jl(t){if(!(Gn(t)&&!t.dirty)&&!(!t.dirty&&t.lastCleanEpoch===Or)){if(!t.producerMustRecompute(t)&&!kr(t)){t.dirty=!1,t.lastCleanEpoch=Or;return}t.producerRecomputeValue(t),t.dirty=!1,t.lastCleanEpoch=Or}}function Xl(t){if(t.liveConsumerNode===void 0)return;let e=qn;qn=!0;try{for(let n of t.liveConsumerNode)n.dirty||tc(n)}finally{qn=e}}function ec(){return oe?.consumerAllowSignalWrites!==!1}function tc(t){t.dirty=!0,Xl(t),t.consumerMarkedDirty?.(t)}function Fr(t){return t&&(t.nextProducerIndex=0),P(t)}function Rr(t,e){if(P(e),!(!t||t.producerNode===void 0||t.producerIndexOfThis===void 0||t.producerLastReadVersion===void 0)){if(Gn(t))for(let n=t.nextProducerIndex;n<t.producerNode.length;n++)Lr(t.producerNode[n],t.producerIndexOfThis[n]);for(;t.producerNode.length>t.nextProducerIndex;)t.producerNode.pop(),t.producerLastReadVersion.pop(),t.producerIndexOfThis.pop()}}function kr(t){fn(t);for(let e=0;e<t.producerNode.length;e++){let n=t.producerNode[e],r=t.producerLastReadVersion[e];if(r!==n.version||(Jl(n),r!==n.version))return!0}return!1}function Zi(t){if(fn(t),Gn(t))for(let e=0;e<t.producerNode.length;e++)Lr(t.producerNode[e],t.producerIndexOfThis[e]);t.producerNode.length=t.producerLastReadVersion.length=t.producerIndexOfThis.length=0,t.liveConsumerNode&&(t.liveConsumerNode.length=t.liveConsumerIndexOfThis.length=0)}function nc(t,e,n){if(rc(t),fn(t),t.liveConsumerNode.length===0)for(let r=0;r<t.producerNode.length;r++)t.producerIndexOfThis[r]=nc(t.producerNode[r],t,r);return t.liveConsumerIndexOfThis.push(n),t.liveConsumerNode.push(e)-1}function Lr(t,e){if(rc(t),fn(t),t.liveConsumerNode.length===1)for(let r=0;r<t.producerNode.length;r++)Lr(t.producerNode[r],t.producerIndexOfThis[r]);let n=t.liveConsumerNode.length-1;if(t.liveConsumerNode[e]=t.liveConsumerNode[n],t.liveConsumerIndexOfThis[e]=t.liveConsumerIndexOfThis[n],t.liveConsumerNode.length--,t.liveConsumerIndexOfThis.length--,e<t.liveConsumerNode.length){let r=t.liveConsumerIndexOfThis[e],o=t.liveConsumerNode[e];fn(o),o.producerIndexOfThis[r]=e}}function Gn(t){return t.consumerIsAlwaysLive||(t?.liveConsumerNode?.length??0)>0}function fn(t){t.producerNode??=[],t.producerIndexOfThis??=[],t.producerLastReadVersion??=[]}function rc(t){t.liveConsumerNode??=[],t.liveConsumerIndexOfThis??=[]}function oc(t){let e=Object.create(ym);e.computation=t;let n=()=>{if(Jl(e),Yi(e),e.value===Pr)throw e.error;return e.value};return n[Qe]=e,n}var Wi=Symbol("UNSET"),Qi=Symbol("COMPUTING"),Pr=Symbol("ERRORED"),ym=It(ge({},Wn),{value:Wi,dirty:!0,error:null,equal:Zl,producerMustRecompute(t){return t.value===Wi||t.value===Qi},producerRecomputeValue(t){if(t.value===Qi)throw new Error("Detected cycle in computations.");let e=t.value;t.value=Qi;let n=Fr(t),r;try{r=t.computation()}catch(o){r=Pr,t.error=o}finally{Rr(t,n)}if(e!==Wi&&e!==Pr&&r!==Pr&&t.equal(e,r)){t.value=e;return}t.value=r,t.version++}});function vm(){throw new Error}var ic=vm;function sc(){ic()}function ac(t){ic=t}var Dm=null;function uc(t){let e=Object.create(cc);e.value=t;let n=()=>(Yi(e),e.value);return n[Qe]=e,n}function Ji(t,e){ec()||sc(),t.equal(t.value,e)||(t.value=e,Em(t))}function lc(t,e){ec()||sc(),Ji(t,e(t.value))}var cc=It(ge({},Wn),{equal:Zl,value:void 0});function Em(t){t.version++,gm(),Xl(t),Dm?.()}function dc(t,e,n){let r=Object.create(wm);n&&(r.consumerAllowSignalWrites=!0),r.fn=t,r.schedule=e;let o=u=>{r.cleanupFn=u};function i(u){return u.fn===null&&u.schedule===null}function s(u){i(u)||(Zi(u),u.cleanupFn(),u.fn=null,u.schedule=null,u.cleanupFn=Ki)}let a=()=>{if(r.fn===null)return;if(mm())throw new Error("Schedulers cannot synchronously execute watches while scheduling.");if(r.dirty=!1,r.hasRun&&!kr(r))return;r.hasRun=!0;let u=Fr(r);try{r.cleanupFn(),r.cleanupFn=Ki,r.fn(o)}finally{Rr(r,u)}};return r.ref={notify:()=>tc(r),run:a,cleanup:()=>r.cleanupFn(),destroy:()=>s(r),[Qe]:r},r.ref}var Ki=()=>{},wm=It(ge({},Wn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:t=>{t.schedule!==null&&t.schedule(t.ref)},hasRun:!1,cleanupFn:Ki});function I(t){return typeof t=="function"}function hn(t){let n=t(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var jr=hn(t=>function(n){t(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function $t(t,e){if(t){let n=t.indexOf(e);0<=n&&t.splice(n,1)}}var ee=class t{constructor(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let e;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(I(r))try{r()}catch(i){e=i instanceof jr?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{fc(i)}catch(s){e=e??[],s instanceof jr?e=[...e,...s.errors]:e.push(s)}}if(e)throw new jr(e)}}add(e){var n;if(e&&e!==this)if(this.closed)fc(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(e)}}_hasParent(e){let{_parentage:n}=this;return n===e||Array.isArray(n)&&n.includes(e)}_addParent(e){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(e),n):n?[n,e]:e}_removeParent(e){let{_parentage:n}=this;n===e?this._parentage=null:Array.isArray(n)&&$t(n,e)}remove(e){let{_finalizers:n}=this;n&&$t(n,e),e instanceof t&&e._removeParent(this)}};ee.EMPTY=(()=>{let t=new ee;return t.closed=!0,t})();var Xi=ee.EMPTY;function Vr(t){return t instanceof ee||t&&"closed"in t&&I(t.remove)&&I(t.add)&&I(t.unsubscribe)}function fc(t){I(t)?t():t.unsubscribe()}var Le={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var pn={setTimeout(t,e,...n){let{delegate:r}=pn;return r?.setTimeout?r.setTimeout(t,e,...n):setTimeout(t,e,...n)},clearTimeout(t){let{delegate:e}=pn;return(e?.clearTimeout||clearTimeout)(t)},delegate:void 0};function Br(t){pn.setTimeout(()=>{let{onUnhandledError:e}=Le;if(e)e(t);else throw t})}function ot(){}var hc=es("C",void 0,void 0);function pc(t){return es("E",void 0,t)}function mc(t){return es("N",t,void 0)}function es(t,e,n){return{kind:t,value:e,error:n}}var Ht=null;function mn(t){if(Le.useDeprecatedSynchronousErrorHandling){let e=!Ht;if(e&&(Ht={errorThrown:!1,error:null}),t(),e){let{errorThrown:n,error:r}=Ht;if(Ht=null,n)throw r}}else t()}function gc(t){Le.useDeprecatedSynchronousErrorHandling&&Ht&&(Ht.errorThrown=!0,Ht.error=t)}var Ut=class extends ee{constructor(e){super(),this.isStopped=!1,e?(this.destination=e,Vr(e)&&e.add(this)):this.destination=_m}static create(e,n,r){return new it(e,n,r)}next(e){this.isStopped?ns(mc(e),this):this._next(e)}error(e){this.isStopped?ns(pc(e),this):(this.isStopped=!0,this._error(e))}complete(){this.isStopped?ns(hc,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(e){this.destination.next(e)}_error(e){try{this.destination.error(e)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},bm=Function.prototype.bind;function ts(t,e){return bm.call(t,e)}var rs=class{constructor(e){this.partialObserver=e}next(e){let{partialObserver:n}=this;if(n.next)try{n.next(e)}catch(r){$r(r)}}error(e){let{partialObserver:n}=this;if(n.error)try{n.error(e)}catch(r){$r(r)}else $r(e)}complete(){let{partialObserver:e}=this;if(e.complete)try{e.complete()}catch(n){$r(n)}}},it=class extends Ut{constructor(e,n,r){super();let o;if(I(e)||!e)o={next:e??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Le.useDeprecatedNextContext?(i=Object.create(e),i.unsubscribe=()=>this.unsubscribe(),o={next:e.next&&ts(e.next,i),error:e.error&&ts(e.error,i),complete:e.complete&&ts(e.complete,i)}):o=e}this.destination=new rs(o)}};function $r(t){Le.useDeprecatedSynchronousErrorHandling?gc(t):Br(t)}function Im(t){throw t}function ns(t,e){let{onStoppedNotification:n}=Le;n&&pn.setTimeout(()=>n(t,e))}var _m={closed:!0,next:ot,error:Im,complete:ot};var gn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ie(t){return t}function Cm(...t){return os(t)}function os(t){return t.length===0?ie:t.length===1?t[0]:function(n){return t.reduce((r,o)=>o(r),n)}}var O=(()=>{class t{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new t;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Mm(n)?n:new it(n,r,o);return mn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=yc(r),new r((o,i)=>{let s=new it({next:a=>{try{n(a)}catch(u){i(u),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[gn](){return this}pipe(...n){return os(n)(this)}toPromise(n){return n=yc(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return t.create=e=>new t(e),t})();function yc(t){var e;return(e=t??Le.Promise)!==null&&e!==void 0?e:Promise}function Sm(t){return t&&I(t.next)&&I(t.error)&&I(t.complete)}function Mm(t){return t&&t instanceof Ut||Sm(t)&&Vr(t)}function is(t){return I(t?.lift)}function w(t){return e=>{if(is(e))return e.lift(function(n){try{return t(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function E(t,e,n,r,o){return new Qn(t,e,n,r,o)}var Qn=class extends Ut{constructor(e,n,r,o,i,s){super(e),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(u){e.error(u)}}:super._next,this._error=o?function(a){try{o(a)}catch(u){e.error(u)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){e.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((e=this.onFinalize)===null||e===void 0||e.call(this))}}};function ss(){return w((t,e)=>{let n=null;t._refCount++;let r=E(e,void 0,void 0,void 0,()=>{if(!t||t._refCount<=0||0<--t._refCount){n=null;return}let o=t._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),e.unsubscribe()});t.subscribe(r),r.closed||(n=t.connect())})}var as=class extends O{constructor(e,n){super(),this.source=e,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,is(e)&&(this.lift=e.lift)}_subscribe(e){return this.getSubject().subscribe(e)}getSubject(){let e=this._subject;return(!e||e.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:e}=this;this._subject=this._connection=null,e?.unsubscribe()}connect(){let e=this._connection;if(!e){e=this._connection=new ee;let n=this.getSubject();e.add(this.source.subscribe(E(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),e.closed&&(this._connection=null,e=ee.EMPTY)}return e}refCount(){return ss()(this)}};var vc=hn(t=>function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ye=(()=>{class t extends O{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Hr(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new vc}next(n){mn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){mn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){mn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Xi:(this.currentObservers=null,i.push(n),new ee(()=>{this.currentObservers=null,$t(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new O;return n.source=this,n}}return t.create=(e,n)=>new Hr(e,n),t})(),Hr=class extends ye{constructor(e,n){super(),this.destination=e,this.source=n}next(e){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,e)}error(e){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,e)}complete(){var e,n;(n=(e=this.destination)===null||e===void 0?void 0:e.complete)===null||n===void 0||n.call(e)}_subscribe(e){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(e))!==null&&r!==void 0?r:Xi}};var Kn=class extends ye{constructor(e){super(),this._value=e}get value(){return this.getValue()}_subscribe(e){let n=super._subscribe(e);return!n.closed&&e.next(this._value),n}getValue(){let{hasError:e,thrownError:n,_value:r}=this;if(e)throw n;return this._throwIfClosed(),r}next(e){super.next(this._value=e)}};var Yn={now(){return(Yn.delegate||Date).now()},delegate:void 0};var Ur=class extends ye{constructor(e=1/0,n=1/0,r=Yn){super(),this._bufferSize=e,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,e),this._windowTime=Math.max(1,n)}next(e){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(e),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(e)}_subscribe(e){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(e),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!e.closed;s+=r?1:2)e.next(i[s]);return this._checkFinalizedStatuses(e),n}_trimBuffer(){let{_bufferSize:e,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*e;if(e<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let u=1;u<r.length&&r[u]<=s;u+=2)a=u;a&&r.splice(0,a+1)}}};var zr=class extends ee{constructor(e,n){super()}schedule(e,n=0){return this}};var Zn={setInterval(t,e,...n){let{delegate:r}=Zn;return r?.setInterval?r.setInterval(t,e,...n):setInterval(t,e,...n)},clearInterval(t){let{delegate:e}=Zn;return(e?.clearInterval||clearInterval)(t)},delegate:void 0};var _t=class extends zr{constructor(e,n){super(e,n),this.scheduler=e,this.work=n,this.pending=!1}schedule(e,n=0){var r;if(this.closed)return this;this.state=e;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(e,n,r=0){return Zn.setInterval(e.flush.bind(e,this),r)}recycleAsyncId(e,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Zn.clearInterval(n)}execute(e,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(e,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(e,n){let r=!1,o;try{this.work(e)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:e,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,$t(r,this),e!=null&&(this.id=this.recycleAsyncId(n,e,null)),this.delay=null,super.unsubscribe()}}};var Tm=1,us,ls={};function Dc(t){return t in ls?(delete ls[t],!0):!1}var Ec={setImmediate(t){let e=Tm++;return ls[e]=!0,us||(us=Promise.resolve()),us.then(()=>Dc(e)&&t()),e},clearImmediate(t){Dc(t)}};var{setImmediate:xm,clearImmediate:Nm}=Ec,Jn={setImmediate(...t){let{delegate:e}=Jn;return(e?.setImmediate||xm)(...t)},clearImmediate(t){let{delegate:e}=Jn;return(e?.clearImmediate||Nm)(t)},delegate:void 0};var qr=class extends _t{constructor(e,n){super(e,n),this.scheduler=e,this.work=n}requestAsyncId(e,n,r=0){return r!==null&&r>0?super.requestAsyncId(e,n,r):(e.actions.push(this),e._scheduled||(e._scheduled=Jn.setImmediate(e.flush.bind(e,void 0))))}recycleAsyncId(e,n,r=0){var o;if(r!=null?r>0:this.delay>0)return super.recycleAsyncId(e,n,r);let{actions:i}=e;n!=null&&((o=i[i.length-1])===null||o===void 0?void 0:o.id)!==n&&(Jn.clearImmediate(n),e._scheduled===n&&(e._scheduled=void 0))}};var yn=class t{constructor(e,n=t.now){this.schedulerActionCtor=e,this.now=n}schedule(e,n=0,r){return new this.schedulerActionCtor(this,e).schedule(r,n)}};yn.now=Yn.now;var Ct=class extends yn{constructor(e,n=yn.now){super(e,n),this.actions=[],this._active=!1}flush(e){let{actions:n}=this;if(this._active){n.push(e);return}let r;this._active=!0;do if(r=e.execute(e.state,e.delay))break;while(e=n.shift());if(this._active=!1,r){for(;e=n.shift();)e.unsubscribe();throw r}}};var Gr=class extends Ct{flush(e){this._active=!0;let n=this._scheduled;this._scheduled=void 0;let{actions:r}=this,o;e=e||r.shift();do if(o=e.execute(e.state,e.delay))break;while((e=r[0])&&e.id===n&&r.shift());if(this._active=!1,o){for(;(e=r[0])&&e.id===n&&r.shift();)e.unsubscribe();throw o}}};var Am=new Gr(qr);var St=new Ct(_t),cs=St;var Wr=class extends _t{constructor(e,n){super(e,n),this.scheduler=e,this.work=n}schedule(e,n=0){return n>0?super.schedule(e,n):(this.delay=n,this.state=e,this.scheduler.flush(this),this)}execute(e,n){return n>0||this.closed?super.execute(e,n):this._execute(e,n)}requestAsyncId(e,n,r=0){return r!=null&&r>0||r==null&&this.delay>0?super.requestAsyncId(e,n,r):(e.flush(this),0)}};var Qr=class extends Ct{};var Om=new Qr(Wr);var st=new O(t=>t.complete());function Kr(t){return t&&I(t.schedule)}function ds(t){return t[t.length-1]}function vn(t){return I(ds(t))?t.pop():void 0}function Ke(t){return Kr(ds(t))?t.pop():void 0}function wc(t,e){return typeof ds(t)=="number"?t.pop():e}function Ic(t,e,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(c){try{l(r.next(c))}catch(d){s(d)}}function u(c){try{l(r.throw(c))}catch(d){s(d)}}function l(c){c.done?i(c.value):o(c.value).then(a,u)}l((r=r.apply(t,e||[])).next())})}function bc(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function zt(t){return this instanceof zt?(this.v=t,this):new zt(t)}function _c(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(t,e||[]),o,i=[];return o={},s("next"),s("throw"),s("return"),o[Symbol.asyncIterator]=function(){return this},o;function s(f){r[f]&&(o[f]=function(h){return new Promise(function(p,m){i.push([f,h,p,m])>1||a(f,h)})})}function a(f,h){try{u(r[f](h))}catch(p){d(i[0][3],p)}}function u(f){f.value instanceof zt?Promise.resolve(f.value.v).then(l,c):d(i[0][2],f)}function l(f){a("next",f)}function c(f){a("throw",f)}function d(f,h){f(h),i.shift(),i.length&&a(i[0][0],i[0][1])}}function Cc(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof bc=="function"?bc(t):t[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=t[i]&&function(s){return new Promise(function(a,u){s=t[i](s),o(a,u,s.done,s.value)})}}function o(i,s,a,u){Promise.resolve(u).then(function(l){i({value:l,done:a})},s)}}var Dn=t=>t&&typeof t.length=="number"&&typeof t!="function";function Yr(t){return I(t?.then)}function Zr(t){return I(t[gn])}function Jr(t){return Symbol.asyncIterator&&I(t?.[Symbol.asyncIterator])}function Xr(t){return new TypeError(`You provided ${t!==null&&typeof t=="object"?"an invalid object":`'${t}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Pm(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var eo=Pm();function to(t){return I(t?.[eo])}function no(t){return _c(this,arguments,function*(){let n=t.getReader();try{for(;;){let{value:r,done:o}=yield zt(n.read());if(o)return yield zt(void 0);yield yield zt(r)}}finally{n.releaseLock()}})}function ro(t){return I(t?.getReader)}function A(t){if(t instanceof O)return t;if(t!=null){if(Zr(t))return Fm(t);if(Dn(t))return Rm(t);if(Yr(t))return km(t);if(Jr(t))return Sc(t);if(to(t))return Lm(t);if(ro(t))return jm(t)}throw Xr(t)}function Fm(t){return new O(e=>{let n=t[gn]();if(I(n.subscribe))return n.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Rm(t){return new O(e=>{for(let n=0;n<t.length&&!e.closed;n++)e.next(t[n]);e.complete()})}function km(t){return new O(e=>{t.then(n=>{e.closed||(e.next(n),e.complete())},n=>e.error(n)).then(null,Br)})}function Lm(t){return new O(e=>{for(let n of t)if(e.next(n),e.closed)return;e.complete()})}function Sc(t){return new O(e=>{Vm(t,e).catch(n=>e.error(n))})}function jm(t){return Sc(no(t))}function Vm(t,e){var n,r,o,i;return Ic(this,void 0,void 0,function*(){try{for(n=Cc(t);r=yield n.next(),!r.done;){let s=r.value;if(e.next(s),e.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}e.complete()})}function ve(t,e,n,r=0,o=!1){let i=e.schedule(function(){n(),o?t.add(this.schedule(null,r)):this.unsubscribe()},r);if(t.add(i),!o)return i}function Xn(t,e=0){return w((n,r)=>{n.subscribe(E(r,o=>ve(r,t,()=>r.next(o),e),()=>ve(r,t,()=>r.complete(),e),o=>ve(r,t,()=>r.error(o),e)))})}function oo(t,e=0){return w((n,r)=>{r.add(t.schedule(()=>n.subscribe(r),e))})}function Mc(t,e){return A(t).pipe(oo(e),Xn(e))}function Tc(t,e){return A(t).pipe(oo(e),Xn(e))}function xc(t,e){return new O(n=>{let r=0;return e.schedule(function(){r===t.length?n.complete():(n.next(t[r++]),n.closed||this.schedule())})})}function Nc(t,e){return new O(n=>{let r;return ve(n,e,()=>{r=t[eo](),ve(n,e,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>I(r?.return)&&r.return()})}function io(t,e){if(!t)throw new Error("Iterable cannot be null");return new O(n=>{ve(n,e,()=>{let r=t[Symbol.asyncIterator]();ve(n,e,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Ac(t,e){return io(no(t),e)}function fs(t,e){if(t!=null){if(Zr(t))return Mc(t,e);if(Dn(t))return xc(t,e);if(Yr(t))return Tc(t,e);if(Jr(t))return io(t,e);if(to(t))return Nc(t,e);if(ro(t))return Ac(t,e)}throw Xr(t)}function Ye(t,e){return e?fs(t,e):A(t)}function hs(...t){let e=Ke(t);return Ye(t,e)}function ps(t,e){let n=I(t)?t:()=>t,r=o=>o.error(n());return new O(e?o=>e.schedule(r,0,o):r)}var Mt=class t{constructor(e,n,r){this.kind=e,this.value=n,this.error=r,this.hasValue=e==="N"}observe(e){return ms(this,e)}do(e,n,r){let{kind:o,value:i,error:s}=this;return o==="N"?e?.(i):o==="E"?n?.(s):r?.()}accept(e,n,r){var o;return I((o=e)===null||o===void 0?void 0:o.next)?this.observe(e):this.do(e,n,r)}toObservable(){let{kind:e,value:n,error:r}=this,o=e==="N"?hs(n):e==="E"?ps(()=>r):e==="C"?st:0;if(!o)throw new TypeError(`Unexpected notification kind ${e}`);return o}static createNext(e){return new t("N",e)}static createError(e){return new t("E",void 0,e)}static createComplete(){return t.completeNotification}};Mt.completeNotification=new Mt("C");function ms(t,e){var n,r,o;let{kind:i,value:s,error:a}=t;if(typeof i!="string")throw new TypeError('Invalid notification, missing "kind"');i==="N"?(n=e.next)===null||n===void 0||n.call(e,s):i==="E"?(r=e.error)===null||r===void 0||r.call(e,a):(o=e.complete)===null||o===void 0||o.call(e)}function Bm(t){return!!t&&(t instanceof O||I(t.lift)&&I(t.subscribe))}var at=hn(t=>function(){t(this),this.name="EmptyError",this.message="no elements in sequence"});function $m(t,e){let n=typeof e=="object";return new Promise((r,o)=>{let i=!1,s;t.subscribe({next:a=>{s=a,i=!0},error:o,complete:()=>{i?r(s):n?r(e.defaultValue):o(new at)}})})}function Oc(t){return t instanceof Date&&!isNaN(t)}function Ie(t,e){return w((n,r)=>{let o=0;n.subscribe(E(r,i=>{r.next(t.call(e,i,o++))}))})}var{isArray:Hm}=Array;function Um(t,e){return Hm(e)?t(...e):t(e)}function En(t){return Ie(e=>Um(t,e))}var{isArray:zm}=Array,{getPrototypeOf:qm,prototype:Gm,keys:Wm}=Object;function so(t){if(t.length===1){let e=t[0];if(zm(e))return{args:e,keys:null};if(Qm(e)){let n=Wm(e);return{args:n.map(r=>e[r]),keys:n}}}return{args:t,keys:null}}function Qm(t){return t&&typeof t=="object"&&qm(t)===Gm}function ao(t,e){return t.reduce((n,r,o)=>(n[r]=e[o],n),{})}function Km(...t){let e=Ke(t),n=vn(t),{args:r,keys:o}=so(t);if(r.length===0)return Ye([],e);let i=new O(Ym(r,e,o?s=>ao(o,s):ie));return n?i.pipe(En(n)):i}function Ym(t,e,n=ie){return r=>{Pc(e,()=>{let{length:o}=t,i=new Array(o),s=o,a=o;for(let u=0;u<o;u++)Pc(e,()=>{let l=Ye(t[u],e),c=!1;l.subscribe(E(r,d=>{i[u]=d,c||(c=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Pc(t,e,n){t?ve(n,t,e):e()}function Fc(t,e,n,r,o,i,s,a){let u=[],l=0,c=0,d=!1,f=()=>{d&&!u.length&&!l&&e.complete()},h=m=>l<r?p(m):u.push(m),p=m=>{i&&e.next(m),l++;let y=!1;A(n(m,c++)).subscribe(E(e,D=>{o?.(D),i?h(D):e.next(D)},()=>{y=!0},void 0,()=>{if(y)try{for(l--;u.length&&l<r;){let D=u.shift();s?ve(e,s,()=>p(D)):p(D)}f()}catch(D){e.error(D)}}))};return t.subscribe(E(e,h,()=>{d=!0,f()})),()=>{a?.()}}function je(t,e,n=1/0){return I(e)?je((r,o)=>Ie((i,s)=>e(r,i,o,s))(A(t(r,o))),n):(typeof e=="number"&&(n=e),w((r,o)=>Fc(r,o,t,n)))}function uo(t=1/0){return je(ie,t)}function Rc(){return uo(1)}function wn(...t){return Rc()(Ye(t,Ke(t)))}function Zm(t){return new O(e=>{A(t()).subscribe(e)})}function Jm(...t){let e=vn(t),{args:n,keys:r}=so(t),o=new O(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),u=s,l=s;for(let c=0;c<s;c++){let d=!1;A(n[c]).subscribe(E(i,f=>{d||(d=!0,l--),a[c]=f},()=>u--,void 0,()=>{(!u||!d)&&(l||i.next(r?ao(r,a):a),i.complete())}))}});return e?o.pipe(En(e)):o}var Xm=["addListener","removeListener"],eg=["addEventListener","removeEventListener"],tg=["on","off"];function gs(t,e,n,r){if(I(n)&&(r=n,n=void 0),r)return gs(t,e,n).pipe(En(r));let[o,i]=og(t)?eg.map(s=>a=>t[s](e,a,n)):ng(t)?Xm.map(kc(t,e)):rg(t)?tg.map(kc(t,e)):[];if(!o&&Dn(t))return je(s=>gs(s,e,n))(A(t));if(!o)throw new TypeError("Invalid event target");return new O(s=>{let a=(...u)=>s.next(1<u.length?u:u[0]);return o(a),()=>i(a)})}function kc(t,e){return n=>r=>t[n](e,r)}function ng(t){return I(t.addListener)&&I(t.removeListener)}function rg(t){return I(t.on)&&I(t.off)}function og(t){return I(t.addEventListener)&&I(t.removeEventListener)}function lo(t=0,e,n=cs){let r=-1;return e!=null&&(Kr(e)?n=e:r=e),new O(o=>{let i=Oc(t)?+t-n.now():t;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function ig(...t){let e=Ke(t),n=wc(t,1/0),r=t;return r.length?r.length===1?A(r[0]):uo(n)(Ye(r,e)):st}function qt(t,e){return w((n,r)=>{let o=0;n.subscribe(E(r,i=>t.call(e,i,o++)&&r.next(i)))})}function Lc(t){return w((e,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let l=o;o=null,n.next(l)}s&&n.complete()},u=()=>{i=null,s&&n.complete()};e.subscribe(E(n,l=>{r=!0,o=l,i||A(t(l)).subscribe(i=E(n,a,u))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function sg(t,e=St){return Lc(()=>lo(t,e))}function jc(t){return w((e,n)=>{let r=null,o=!1,i;r=e.subscribe(E(n,void 0,void 0,s=>{i=A(t(s,jc(t)(e))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Vc(t,e,n,r,o){return(i,s)=>{let a=n,u=e,l=0;i.subscribe(E(s,c=>{let d=l++;u=a?t(u,c,d):(a=!0,c),r&&s.next(u)},o&&(()=>{a&&s.next(u),s.complete()})))}}function ag(t,e){return I(e)?je(t,e,1):je(t,1)}function Bc(t,e=St){return w((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let l=i;i=null,r.next(l)}};function u(){let l=s+t,c=e.now();if(c<l){o=this.schedule(void 0,l-c),r.add(o);return}a()}n.subscribe(E(r,l=>{i=l,s=e.now(),o||(o=e.schedule(u,t),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function er(t){return w((e,n)=>{let r=!1;e.subscribe(E(n,o=>{r=!0,n.next(o)},()=>{r||n.next(t),n.complete()}))})}function bn(t){return t<=0?()=>st:w((e,n)=>{let r=0;e.subscribe(E(n,o=>{++r<=t&&(n.next(o),t<=r&&n.complete())}))})}function ys(){return w((t,e)=>{t.subscribe(E(e,ot))})}function vs(t){return Ie(()=>t)}function Ds(t,e){return e?n=>wn(e.pipe(bn(1),ys()),n.pipe(Ds(t))):je((n,r)=>A(t(n,r)).pipe(bn(1),vs(n)))}function ug(t,e=St){let n=lo(t,e);return Ds(()=>n)}function lg(){return w((t,e)=>{t.subscribe(E(e,n=>ms(n,e)))})}function cg(t,e=ie){return t=t??dg,w((n,r)=>{let o,i=!0;n.subscribe(E(r,s=>{let a=e(s);(i||!t(o,a))&&(i=!1,o=a,r.next(s))}))})}function dg(t,e){return t===e}function co(t=fg){return w((e,n)=>{let r=!1;e.subscribe(E(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(t())))})}function fg(){return new at}function $c(t,e){return e?n=>n.pipe($c((r,o)=>A(t(r,o)).pipe(Ie((i,s)=>e(r,i,o,s))))):w((n,r)=>{let o=0,i=null,s=!1;n.subscribe(E(r,a=>{i||(i=E(r,void 0,()=>{i=null,s&&r.complete()}),A(t(a,o++)).subscribe(i))},()=>{s=!0,!i&&r.complete()}))})}function hg(t){return w((e,n)=>{try{e.subscribe(n)}finally{n.add(t)}})}function Hc(t,e){let n=arguments.length>=2;return r=>r.pipe(t?qt((o,i)=>t(o,i,r)):ie,bn(1),n?er(e):co(()=>new at))}function pg(t,e,n,r){return w((o,i)=>{let s;!e||typeof e=="function"?s=e:{duration:n,element:s,connector:r}=e;let a=new Map,u=p=>{a.forEach(p),p(i)},l=p=>u(m=>m.error(p)),c=0,d=!1,f=new Qn(i,p=>{try{let m=t(p),y=a.get(m);if(!y){a.set(m,y=r?r():new ye);let D=h(m,y);if(i.next(D),n){let T=E(y,()=>{y.complete(),T?.unsubscribe()},void 0,void 0,()=>a.delete(m));f.add(A(n(D)).subscribe(T))}}y.next(s?s(p):p)}catch(m){l(m)}},()=>u(p=>p.complete()),l,()=>a.clear(),()=>(d=!0,c===0));o.subscribe(f);function h(p,m){let y=new O(D=>{c++;let T=m.subscribe(D);return()=>{T.unsubscribe(),--c===0&&d&&f.unsubscribe()}});return y.key=p,y}})}function Es(t){return t<=0?()=>st:w((e,n)=>{let r=[];e.subscribe(E(n,o=>{r.push(o),t<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function mg(t,e){let n=arguments.length>=2;return r=>r.pipe(t?qt((o,i)=>t(o,i,r)):ie,Es(1),n?er(e):co(()=>new at))}function gg(){return w((t,e)=>{t.subscribe(E(e,n=>{e.next(Mt.createNext(n))},()=>{e.next(Mt.createComplete()),e.complete()},n=>{e.next(Mt.createError(n)),e.complete()}))})}function yg(...t){let e=t.length;if(e===0)throw new Error("list of properties cannot be empty.");return Ie(n=>{let r=n;for(let o=0;o<e;o++){let i=r?.[t[o]];if(typeof i<"u")r=i;else return}return r})}function vg(t,e){return w(Vc(t,e,arguments.length>=2,!0))}function bs(t={}){let{connector:e=()=>new ye,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=t;return i=>{let s,a,u,l=0,c=!1,d=!1,f=()=>{a?.unsubscribe(),a=void 0},h=()=>{f(),s=u=void 0,c=d=!1},p=()=>{let m=s;h(),m?.unsubscribe()};return w((m,y)=>{l++,!d&&!c&&f();let D=u=u??e();y.add(()=>{l--,l===0&&!d&&!c&&(a=ws(p,o))}),D.subscribe(y),!s&&l>0&&(s=new it({next:T=>D.next(T),error:T=>{d=!0,f(),a=ws(h,n,T),D.error(T)},complete:()=>{c=!0,f(),a=ws(h,r),D.complete()}}),A(m).subscribe(s))})(i)}}function ws(t,e,...n){if(e===!0){t();return}if(e===!1)return;let r=new it({next:()=>{r.unsubscribe(),t()}});return A(e(...n)).subscribe(r)}function Dg(t,e,n){let r,o=!1;return t&&typeof t=="object"?{bufferSize:r=1/0,windowTime:e=1/0,refCount:o=!1,scheduler:n}=t:r=t??1/0,bs({connector:()=>new Ur(r,e,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function Eg(t){return qt((e,n)=>t<=n)}function wg(...t){let e=Ke(t);return w((n,r)=>{(e?wn(t,n,e):wn(t,n)).subscribe(r)})}function bg(t,e){return w((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(E(r,u=>{o?.unsubscribe();let l=0,c=i++;A(t(u,c)).subscribe(o=E(r,d=>r.next(e?e(u,d,c,l++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Ig(t){return w((e,n)=>{A(t).subscribe(E(n,()=>n.complete(),ot)),!n.closed&&e.subscribe(n)})}function _g(t,e,n){let r=I(t)||e||n?{next:t,error:e,complete:n}:t;return r?w((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(E(i,u=>{var l;(l=r.next)===null||l===void 0||l.call(r,u),i.next(u)},()=>{var u;a=!1,(u=r.complete)===null||u===void 0||u.call(r),i.complete()},u=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,u),i.error(u)},()=>{var u,l;a&&((u=r.unsubscribe)===null||u===void 0||u.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):ie}function Cg(...t){let e=vn(t);return w((n,r)=>{let o=t.length,i=new Array(o),s=t.map(()=>!1),a=!1;for(let u=0;u<o;u++)A(t[u]).subscribe(E(r,l=>{i[u]=l,!a&&!s[u]&&(s[u]=!0,(a=s.every(ie))&&(s=null))},ot));n.subscribe(E(r,u=>{if(a){let l=[u,...i];r.next(e?e(...l):l)}}))})}var Nd="https://g.co/ng/security#xss",g=class extends Error{constructor(e,n){super(Ad(e,n)),this.code=e}};function Ad(t,e){return`${`NG0${Math.abs(t)}`}${e?": "+e:""}`}function yr(t){return{toString:t}.toString()}var fo="__parameters__";function Sg(t){return function(...n){if(t){let r=t(...n);for(let o in r)this[o]=r[o]}}}function Xa(t,e,n){return yr(()=>{let r=Sg(e);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(u,l,c){let d=u.hasOwnProperty(fo)?u[fo]:Object.defineProperty(u,fo,{value:[]})[fo];for(;d.length<=c;)d.push(null);return(d[c]=d[c]||[]).push(s),u}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=t,o.annotationCls=o,o})}var $e=globalThis;function $(t){for(let e in t)if(t[e]===$)return e;throw Error("Could not find renamed property on target object.")}function Mg(t,e){for(let n in e)e.hasOwnProperty(n)&&!t.hasOwnProperty(n)&&(t[n]=e[n])}function pe(t){if(typeof t=="string")return t;if(Array.isArray(t))return"["+t.map(pe).join(", ")+"]";if(t==null)return""+t;if(t.overriddenName)return`${t.overriddenName}`;if(t.name)return`${t.name}`;let e=t.toString();if(e==null)return""+e;let n=e.indexOf(`
`);return n===-1?e:e.substring(0,n)}function js(t,e){return t==null||t===""?e===null?"":e:e==null||e===""?t:t+" "+e}var Tg=$({__forward_ref__:$});function Od(t){return t.__forward_ref__=Od,t.toString=function(){return pe(this())},t}function he(t){return Pd(t)?t():t}function Pd(t){return typeof t=="function"&&t.hasOwnProperty(Tg)&&t.__forward_ref__===Od}function H(t){return{token:t.token,providedIn:t.providedIn||null,factory:t.factory,value:void 0}}function eu(t){return{providers:t.providers||[],imports:t.imports||[]}}function Qo(t){return Uc(t,Fd)||Uc(t,Rd)}function oP(t){return Qo(t)!==null}function Uc(t,e){return t.hasOwnProperty(e)?t[e]:null}function xg(t){let e=t&&(t[Fd]||t[Rd]);return e||null}function zc(t){return t&&(t.hasOwnProperty(qc)||t.hasOwnProperty(Ng))?t[qc]:null}var Fd=$({\u0275prov:$}),qc=$({\u0275inj:$}),Rd=$({ngInjectableDef:$}),Ng=$({ngInjectorDef:$}),B=class{constructor(e,n){this._desc=e,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=H({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function kd(t){return t&&!!t.\u0275providers}var Ag=$({\u0275cmp:$}),Og=$({\u0275dir:$}),Pg=$({\u0275pipe:$}),Fg=$({\u0275mod:$}),Co=$({\u0275fac:$}),tr=$({__NG_ELEMENT_ID__:$}),Gc=$({__NG_ENV_ID__:$});function Tn(t){return typeof t=="string"?t:t==null?"":String(t)}function Rg(t){return typeof t=="function"?t.name||t.toString():typeof t=="object"&&t!=null&&typeof t.type=="function"?t.type.name||t.type.toString():Tn(t)}function kg(t,e){let n=e?`. Dependency path: ${e.join(" > ")} > ${t}`:"";throw new g(-200,t)}function tu(t,e){throw new g(-201,!1)}var F=function(t){return t[t.Default=0]="Default",t[t.Host=1]="Host",t[t.Self=2]="Self",t[t.SkipSelf=4]="SkipSelf",t[t.Optional=8]="Optional",t}(F||{}),Vs;function Ld(){return Vs}function De(t){let e=Vs;return Vs=t,e}function jd(t,e,n){let r=Qo(t);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&F.Optional)return null;if(e!==void 0)return e;tu(t,"Injector")}var Lg={},rr=Lg,Bs="__NG_DI_FLAG__",So="ngTempTokenPath",jg="ngTokenPath",Vg=/\n/gm,Bg="\u0275",Wc="__source",Sn;function $g(){return Sn}function Tt(t){let e=Sn;return Sn=t,e}function Hg(t,e=F.Default){if(Sn===void 0)throw new g(-203,!1);return Sn===null?jd(t,void 0,e):Sn.get(t,e&F.Optional?null:void 0,e)}function te(t,e=F.Default){return(Ld()||Hg)(he(t),e)}function Q(t,e=F.Default){return te(t,Ko(e))}function Ko(t){return typeof t>"u"||typeof t=="number"?t:0|(t.optional&&8)|(t.host&&1)|(t.self&&2)|(t.skipSelf&&4)}function $s(t){let e=[];for(let n=0;n<t.length;n++){let r=he(t[n]);if(Array.isArray(r)){if(r.length===0)throw new g(900,!1);let o,i=F.Default;for(let s=0;s<r.length;s++){let a=r[s],u=Ug(a);typeof u=="number"?u===-1?o=a.token:i|=u:o=a}e.push(te(o,i))}else e.push(te(r))}return e}function nu(t,e){return t[Bs]=e,t.prototype[Bs]=e,t}function Ug(t){return t[Bs]}function zg(t,e,n,r){let o=t[So];throw e[Wc]&&o.unshift(e[Wc]),t.message=qg(`
`+t.message,o,n,r),t[jg]=o,t[So]=null,t}function qg(t,e,n,r=null){t=t&&t.charAt(0)===`
`&&t.charAt(1)==Bg?t.slice(2):t;let o=pe(e);if(Array.isArray(e))o=e.map(pe).join(" -> ");else if(typeof e=="object"){let i=[];for(let s in e)if(e.hasOwnProperty(s)){let a=e[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):pe(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${t.replace(Vg,`
  `)}`}var iP=nu(Xa("Inject",t=>({token:t})),-1),Vd=nu(Xa("Optional"),8);var Bd=nu(Xa("SkipSelf"),4);function Kt(t,e){let n=t.hasOwnProperty(Co);return n?t[Co]:null}function Gg(t,e,n){if(t.length!==e.length)return!1;for(let r=0;r<t.length;r++){let o=t[r],i=e[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Wg(t){return t.flat(Number.POSITIVE_INFINITY)}function ru(t,e){t.forEach(n=>Array.isArray(n)?ru(n,e):e(n))}function $d(t,e,n){e>=t.length?t.push(n):t.splice(e,0,n)}function Mo(t,e){return e>=t.length-1?t.pop():t.splice(e,1)[0]}function Qg(t,e){let n=[];for(let r=0;r<t;r++)n.push(e);return n}function Kg(t,e,n,r){let o=t.length;if(o==e)t.push(n,r);else if(o===1)t.push(r,t[0]),t[0]=n;else{for(o--,t.push(t[o-1],t[o]);o>e;){let i=o-2;t[o]=t[i],o--}t[e]=n,t[e+1]=r}}function Yo(t,e,n){let r=vr(t,e);return r>=0?t[r|1]=n:(r=~r,Kg(t,r,e,n)),r}function Is(t,e){let n=vr(t,e);if(n>=0)return t[n|1]}function vr(t,e){return Yg(t,e,1)}function Yg(t,e,n){let r=0,o=t.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=t[i<<n];if(e===s)return i<<n;s>e?o=i:r=i+1}return~(o<<n)}var xn={},Ee=[],To=new B(""),Hd=new B("",-1),Ud=new B(""),xo=class{get(e,n=rr){if(n===rr){let r=new Error(`NullInjectorError: No provider for ${pe(e)}!`);throw r.name="NullInjectorError",r}return n}},zd=function(t){return t[t.OnPush=0]="OnPush",t[t.Default=1]="Default",t}(zd||{}),or=function(t){return t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom",t}(or||{}),Nt=function(t){return t[t.None=0]="None",t[t.SignalBased=1]="SignalBased",t[t.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",t}(Nt||{});function Zg(t,e,n){let r=t.length;for(;;){let o=t.indexOf(e,n);if(o===-1)return o;if(o===0||t.charCodeAt(o-1)<=32){let i=e.length;if(o+i===r||t.charCodeAt(o+i)<=32)return o}n=o+1}}function Hs(t,e,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];t.setAttribute(e,s,a,i)}else{let i=o,s=n[++r];Jg(i)?t.setProperty(e,i,s):t.setAttribute(e,i,s),r++}}return r}function qd(t){return t===3||t===4||t===6}function Jg(t){return t.charCodeAt(0)===64}function ir(t,e){if(!(e===null||e.length===0))if(t===null||t.length===0)t=e.slice();else{let n=-1;for(let r=0;r<e.length;r++){let o=e[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Qc(t,n,o,null,e[++r]):Qc(t,n,o,null,null))}}return t}function Qc(t,e,n,r,o){let i=0,s=t.length;if(e===-1)s=-1;else for(;i<t.length;){let a=t[i++];if(typeof a=="number"){if(a===e){s=-1;break}else if(a>e){s=i-1;break}}}for(;i<t.length;){let a=t[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(t[i+1]=o);return}else if(r===t[i+1]){t[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(t.splice(s,0,e),i=s+1),t.splice(i++,0,n),r!==null&&t.splice(i++,0,r),o!==null&&t.splice(i++,0,o)}var Gd="ng-template";function Xg(t,e,n,r){let o=0;if(r){for(;o<e.length&&typeof e[o]=="string";o+=2)if(e[o]==="class"&&Zg(e[o+1].toLowerCase(),n,0)!==-1)return!0}else if(ou(t))return!1;if(o=e.indexOf(1,o),o>-1){let i;for(;++o<e.length&&typeof(i=e[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function ou(t){return t.type===4&&t.value!==Gd}function ey(t,e,n){let r=t.type===4&&!n?Gd:t.value;return e===r}function ty(t,e,n){let r=4,o=t.attrs,i=o!==null?oy(o):0,s=!1;for(let a=0;a<e.length;a++){let u=e[a];if(typeof u=="number"){if(!s&&!Ve(r)&&!Ve(u))return!1;if(s&&Ve(u))continue;s=!1,r=u|r&1;continue}if(!s)if(r&4){if(r=2|r&1,u!==""&&!ey(t,u,n)||u===""&&e.length===1){if(Ve(r))return!1;s=!0}}else if(r&8){if(o===null||!Xg(t,o,u,n)){if(Ve(r))return!1;s=!0}}else{let l=e[++a],c=ny(u,o,ou(t),n);if(c===-1){if(Ve(r))return!1;s=!0;continue}if(l!==""){let d;if(c>i?d="":d=o[c+1].toLowerCase(),r&2&&l!==d){if(Ve(r))return!1;s=!0}}}}return Ve(r)||s}function Ve(t){return(t&1)===0}function ny(t,e,n,r){if(e===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<e.length;){let s=e[o];if(s===t)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=e[++o];for(;typeof a=="string";)a=e[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return iy(e,t)}function Wd(t,e,n=!1){for(let r=0;r<e.length;r++)if(ty(t,e[r],n))return!0;return!1}function ry(t){let e=t.attrs;if(e!=null){let n=e.indexOf(5);if(!(n&1))return e[n+1]}return null}function oy(t){for(let e=0;e<t.length;e++){let n=t[e];if(qd(n))return e}return t.length}function iy(t,e){let n=t.indexOf(4);if(n>-1)for(n++;n<t.length;){let r=t[n];if(typeof r=="number")return-1;if(r===e)return n;n++}return-1}function sy(t,e){e:for(let n=0;n<e.length;n++){let r=e[n];if(t.length===r.length){for(let o=0;o<t.length;o++)if(t[o]!==r[o])continue e;return!0}}return!1}function Kc(t,e){return t?":not("+e.trim()+")":e}function ay(t){let e=t[0],n=1,r=2,o="",i=!1;for(;n<t.length;){let s=t[n];if(typeof s=="string")if(r&2){let a=t[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ve(s)&&(e+=Kc(i,o),o=""),r=s,i=i||!Ve(r);n++}return o!==""&&(e+=Kc(i,o)),e}function uy(t){return t.map(ay).join(",")}function ly(t){let e=[],n=[],r=1,o=2;for(;r<t.length;){let i=t[r];if(typeof i=="string")o===2?i!==""&&e.push(i,t[++r]):o===8&&n.push(i);else{if(!Ve(o))break;o=i}r++}return{attrs:e,classes:n}}function sP(t){return yr(()=>{let e=Zd(t),n=It(ge({},e),{decls:t.decls,vars:t.vars,template:t.template,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,onPush:t.changeDetection===zd.OnPush,directiveDefs:null,pipeDefs:null,dependencies:e.standalone&&t.dependencies||null,getStandaloneInjector:null,signals:t.signals??!1,data:t.data||{},encapsulation:t.encapsulation||or.Emulated,styles:t.styles||Ee,_:null,schemas:t.schemas||null,tView:null,id:""});Jd(n);let r=t.dependencies;return n.directiveDefs=Zc(r,!1),n.pipeDefs=Zc(r,!0),n.id=hy(n),n})}function cy(t){return Yt(t)||Qd(t)}function dy(t){return t!==null}function iu(t){return yr(()=>({type:t.type,bootstrap:t.bootstrap||Ee,declarations:t.declarations||Ee,imports:t.imports||Ee,exports:t.exports||Ee,transitiveCompileScopes:null,schemas:t.schemas||null,id:t.id||null}))}function Yc(t,e){if(t==null)return xn;let n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r],i,s,a=Nt.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),e?(n[i]=a!==Nt.None?[r,a]:r,e[i]=s):n[i]=r}return n}function ft(t){return yr(()=>{let e=Zd(t);return Jd(e),e})}function su(t){return{type:t.type,name:t.name,factory:null,pure:t.pure!==!1,standalone:t.standalone===!0,onDestroy:t.type.prototype.ngOnDestroy||null}}function Yt(t){return t[Ag]||null}function Qd(t){return t[Og]||null}function Kd(t){return t[Pg]||null}function fy(t){let e=Yt(t)||Qd(t)||Kd(t);return e!==null?e.standalone:!1}function Yd(t,e){let n=t[Fg]||null;if(!n&&e===!0)throw new Error(`Type ${pe(t)} does not have '\u0275mod' property.`);return n}function Zd(t){let e={};return{type:t.type,providersResolver:null,factory:null,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:e,inputTransforms:null,inputConfig:t.inputs||xn,exportAs:t.exportAs||null,standalone:t.standalone===!0,signals:t.signals===!0,selectors:t.selectors||Ee,viewQuery:t.viewQuery||null,features:t.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Yc(t.inputs,e),outputs:Yc(t.outputs),debugInfo:null}}function Jd(t){t.features?.forEach(e=>e(t))}function Zc(t,e){if(!t)return null;let n=e?Kd:cy;return()=>(typeof t=="function"?t():t).map(r=>n(r)).filter(dy)}function hy(t){let e=0,n=[t.selectors,t.ngContentSelectors,t.hostVars,t.hostAttrs,t.consts,t.vars,t.decls,t.encapsulation,t.standalone,t.signals,t.exportAs,JSON.stringify(t.inputs),JSON.stringify(t.outputs),Object.getOwnPropertyNames(t.type.prototype),!!t.contentQueries,!!t.viewQuery].join("|");for(let o of n)e=Math.imul(31,e)+o.charCodeAt(0)<<0;return e+=**********,"c"+e}function aP(t){return{\u0275providers:t}}function py(...t){return{\u0275providers:Xd(!0,t),\u0275fromNgModule:!0}}function Xd(t,...e){let n=[],r=new Set,o,i=s=>{n.push(s)};return ru(e,s=>{let a=s;Us(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&ef(o,i),n}function ef(t,e){for(let n=0;n<t.length;n++){let{ngModule:r,providers:o}=t[n];au(o,i=>{e(i,r)})}}function Us(t,e,n,r){if(t=he(t),!t)return!1;let o=null,i=zc(t),s=!i&&Yt(t);if(!i&&!s){let u=t.ngModule;if(i=zc(u),i)o=u;else return!1}else{if(s&&!s.standalone)return!1;o=t}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let u=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of u)Us(l,e,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{ru(i.imports,c=>{Us(c,e,n,r)&&(l||=[],l.push(c))})}finally{}l!==void 0&&ef(l,e)}if(!a){let l=Kt(o)||(()=>new o);e({provide:o,useFactory:l,deps:Ee},o),e({provide:Ud,useValue:o,multi:!0},o),e({provide:To,useValue:()=>te(o),multi:!0},o)}let u=i.providers;if(u!=null&&!a){let l=t;au(u,c=>{e(c,l)})}}else return!1;return o!==t&&t.providers!==void 0}function au(t,e){for(let n of t)kd(n)&&(n=n.\u0275providers),Array.isArray(n)?au(n,e):e(n)}var my=$({provide:String,useValue:$});function tf(t){return t!==null&&typeof t=="object"&&my in t}function gy(t){return!!(t&&t.useExisting)}function yy(t){return!!(t&&t.useFactory)}function Nn(t){return typeof t=="function"}function vy(t){return!!t.useClass}var nf=new B(""),vo={},Dy={},_s;function uu(){return _s===void 0&&(_s=new xo),_s}var At=class{},sr=class extends At{get destroyed(){return this._destroyed}constructor(e,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,qs(e,s=>this.processProvider(s)),this.records.set(Hd,In(void 0,this)),o.has("environment")&&this.records.set(At,In(void 0,this));let i=this.records.get(nf);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Ud,Ee,F.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let e=P(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),P(e)}}onDestroy(e){return this.assertNotDestroyed(),this._onDestroyHooks.push(e),()=>this.removeOnDestroy(e)}runInContext(e){this.assertNotDestroyed();let n=Tt(this),r=De(void 0),o;try{return e()}finally{Tt(n),De(r)}}get(e,n=rr,r=F.Default){if(this.assertNotDestroyed(),e.hasOwnProperty(Gc))return e[Gc](this);r=Ko(r);let o,i=Tt(this),s=De(void 0);try{if(!(r&F.SkipSelf)){let u=this.records.get(e);if(u===void 0){let l=_y(e)&&Qo(e);l&&this.injectableDefInScope(l)?u=In(zs(e),vo):u=null,this.records.set(e,u)}if(u!=null)return this.hydrate(e,u)}let a=r&F.Self?uu():this.parent;return n=r&F.Optional&&n===rr?null:n,a.get(e,n)}catch(a){if(a.name==="NullInjectorError"){if((a[So]=a[So]||[]).unshift(pe(e)),i)throw a;return zg(a,e,"R3InjectorError",this.source)}else throw a}finally{De(s),Tt(i)}}resolveInjectorInitializers(){let e=P(null),n=Tt(this),r=De(void 0),o;try{let i=this.get(To,Ee,F.Self);for(let s of i)s()}finally{Tt(n),De(r),P(e)}}toString(){let e=[],n=this.records;for(let r of n.keys())e.push(pe(r));return`R3Injector[${e.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new g(205,!1)}processProvider(e){e=he(e);let n=Nn(e)?e:he(e&&e.provide),r=wy(e);if(!Nn(e)&&e.multi===!0){let o=this.records.get(n);o||(o=In(void 0,vo,!0),o.factory=()=>$s(o.multi),this.records.set(n,o)),n=e,o.multi.push(e)}this.records.set(n,r)}hydrate(e,n){let r=P(null);try{return n.value===vo&&(n.value=Dy,n.value=n.factory()),typeof n.value=="object"&&n.value&&Iy(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{P(r)}}injectableDefInScope(e){if(!e.providedIn)return!1;let n=he(e.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(e){let n=this._onDestroyHooks.indexOf(e);n!==-1&&this._onDestroyHooks.splice(n,1)}};function zs(t){let e=Qo(t),n=e!==null?e.factory:Kt(t);if(n!==null)return n;if(t instanceof B)throw new g(204,!1);if(t instanceof Function)return Ey(t);throw new g(204,!1)}function Ey(t){if(t.length>0)throw new g(204,!1);let n=xg(t);return n!==null?()=>n.factory(t):()=>new t}function wy(t){if(tf(t))return In(void 0,t.useValue);{let e=rf(t);return In(e,vo)}}function rf(t,e,n){let r;if(Nn(t)){let o=he(t);return Kt(o)||zs(o)}else if(tf(t))r=()=>he(t.useValue);else if(yy(t))r=()=>t.useFactory(...$s(t.deps||[]));else if(gy(t))r=()=>te(he(t.useExisting));else{let o=he(t&&(t.useClass||t.provide));if(by(t))r=()=>new o(...$s(t.deps));else return Kt(o)||zs(o)}return r}function In(t,e,n=!1){return{factory:t,value:e,multi:n?[]:void 0}}function by(t){return!!t.deps}function Iy(t){return t!==null&&typeof t=="object"&&typeof t.ngOnDestroy=="function"}function _y(t){return typeof t=="function"||typeof t=="object"&&t instanceof B}function qs(t,e){for(let n of t)Array.isArray(n)?qs(n,e):n&&kd(n)?qs(n.\u0275providers,e):e(n)}function uP(t,e){t instanceof sr&&t.assertNotDestroyed();let n,r=Tt(t),o=De(void 0);try{return e()}finally{Tt(r),De(o)}}function of(){return Ld()!==void 0||$g()!=null}function Cy(t){if(!of())throw new g(-203,!1)}function Sy(t){let e=$e.ng;if(e&&e.\u0275compilerFacade)return e.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function My(t){return typeof t=="function"}var et=0,x=1,S=2,se=3,Ue=4,_e=5,An=6,ar=7,de=8,On=9,ze=10,G=11,ur=12,Jc=13,kn=14,Pe=15,Dr=16,_n=17,ut=18,Zo=19,sf=20,xt=21,Do=22,Zt=23,ae=25,lu=1;var Jt=7,No=8,Pn=9,fe=10,cu=function(t){return t[t.None=0]="None",t[t.HasTransplantedViews=2]="HasTransplantedViews",t}(cu||{});function Wt(t){return Array.isArray(t)&&typeof t[lu]=="object"}function ht(t){return Array.isArray(t)&&t[lu]===!0}function du(t){return(t.flags&4)!==0}function Jo(t){return t.componentOffset>-1}function Xo(t){return(t.flags&1)===1}function lt(t){return!!t.template}function Ty(t){return(t[S]&512)!==0}var Gs=class{constructor(e,n,r){this.previousValue=e,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function af(t,e,n,r){e!==null?e.applyValueToInputSignal(e,r):t[n]=r}function fu(){return uf}function uf(t){return t.type.prototype.ngOnChanges&&(t.setInput=Ny),xy}fu.ngInherit=!0;function xy(){let t=cf(this),e=t?.current;if(e){let n=t.previous;if(n===xn)t.previous=e;else for(let r in e)n[r]=e[r];t.current=null,this.ngOnChanges(e)}}function Ny(t,e,n,r,o){let i=this.declaredInputs[r],s=cf(t)||Ay(t,{previous:xn,current:null}),a=s.current||(s.current={}),u=s.previous,l=u[i];a[i]=new Gs(l&&l.currentValue,n,u===xn),af(t,e,o,n)}var lf="__ngSimpleChanges__";function cf(t){return t[lf]||null}function Ay(t,e){return t[lf]=e}var Xc=null;var Ze=function(t,e,n){Xc?.(t,e,n)},df="svg",Oy="math",Py=!1;function Fy(){return Py}function Xe(t){for(;Array.isArray(t);)t=t[et];return t}function Ry(t){for(;Array.isArray(t);){if(typeof t[lu]=="object")return t;t=t[et]}return null}function ff(t,e){return Xe(e[t])}function Fe(t,e){return Xe(e[t.index])}function hu(t,e){return t.data[e]}function pu(t,e){return t[e]}function Ft(t,e){let n=e[t];return Wt(n)?n:n[et]}function ky(t){return(t[S]&4)===4}function mu(t){return(t[S]&128)===128}function Ly(t){return ht(t[se])}function Fn(t,e){return e==null?null:t[e]}function hf(t){t[_n]=0}function jy(t){t[S]&1024||(t[S]|=1024,mu(t)&&lr(t))}function Vy(t,e){for(;t>0;)e=e[kn],t--;return e}function gu(t){return!!(t[S]&9216||t[Zt]?.dirty)}function Ws(t){t[ze].changeDetectionScheduler?.notify(1),gu(t)?lr(t):t[S]&64&&(Fy()?(t[S]|=1024,lr(t)):t[ze].changeDetectionScheduler?.notify())}function lr(t){t[ze].changeDetectionScheduler?.notify();let e=cr(t);for(;e!==null&&!(e[S]&8192||(e[S]|=8192,!mu(e)));)e=cr(e)}function pf(t,e){if((t[S]&256)===256)throw new g(911,!1);t[xt]===null&&(t[xt]=[]),t[xt].push(e)}function By(t,e){if(t[xt]===null)return;let n=t[xt].indexOf(e);n!==-1&&t[xt].splice(n,1)}function cr(t){let e=t[se];return ht(e)?e[se]:e}var N={lFrame:wf(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function $y(){return N.lFrame.elementDepthCount}function Hy(){N.lFrame.elementDepthCount++}function Uy(){N.lFrame.elementDepthCount--}function mf(){return N.bindingsEnabled}function gf(){return N.skipHydrationRootTNode!==null}function zy(t){return N.skipHydrationRootTNode===t}function qy(){N.skipHydrationRootTNode=null}function C(){return N.lFrame.lView}function W(){return N.lFrame.tView}function lP(t){return N.lFrame.contextLView=t,t[de]}function cP(t){return N.lFrame.contextLView=null,t}function ue(){let t=yf();for(;t!==null&&t.type===64;)t=t.parent;return t}function yf(){return N.lFrame.currentTNode}function Gy(){let t=N.lFrame,e=t.currentTNode;return t.isParent?e:e.parent}function sn(t,e){let n=N.lFrame;n.currentTNode=t,n.isParent=e}function yu(){return N.lFrame.isParent}function vu(){N.lFrame.isParent=!1}function Wy(){return N.lFrame.contextLView}function Ln(){let t=N.lFrame,e=t.bindingRootIndex;return e===-1&&(e=t.bindingRootIndex=t.tView.bindingStartIndex),e}function Qy(){return N.lFrame.bindingIndex}function Ky(t){return N.lFrame.bindingIndex=t}function Rt(){return N.lFrame.bindingIndex++}function Du(t){let e=N.lFrame,n=e.bindingIndex;return e.bindingIndex=e.bindingIndex+t,n}function Yy(){return N.lFrame.inI18n}function Zy(t,e){let n=N.lFrame;n.bindingIndex=n.bindingRootIndex=t,Qs(e)}function Jy(){return N.lFrame.currentDirectiveIndex}function Qs(t){N.lFrame.currentDirectiveIndex=t}function Eu(t){let e=N.lFrame.currentDirectiveIndex;return e===-1?null:t[e]}function vf(){return N.lFrame.currentQueryIndex}function wu(t){N.lFrame.currentQueryIndex=t}function Xy(t){let e=t[x];return e.type===2?e.declTNode:e.type===1?t[_e]:null}function Df(t,e,n){if(n&F.SkipSelf){let o=e,i=t;for(;o=o.parent,o===null&&!(n&F.Host);)if(o=Xy(i),o===null||(i=i[kn],o.type&10))break;if(o===null)return!1;e=o,t=i}let r=N.lFrame=Ef();return r.currentTNode=e,r.lView=t,!0}function bu(t){let e=Ef(),n=t[x];N.lFrame=e,e.currentTNode=n.firstChild,e.lView=t,e.tView=n,e.contextLView=t,e.bindingIndex=n.bindingStartIndex,e.inI18n=!1}function Ef(){let t=N.lFrame,e=t===null?null:t.child;return e===null?wf(t):e}function wf(t){let e={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:t,child:null,inI18n:!1};return t!==null&&(t.child=e),e}function bf(){let t=N.lFrame;return N.lFrame=t.parent,t.currentTNode=null,t.lView=null,t}var If=bf;function Iu(){let t=bf();t.isParent=!0,t.tView=null,t.selectedIndex=-1,t.contextLView=null,t.elementDepthCount=0,t.currentDirectiveIndex=-1,t.currentNamespace=null,t.bindingRootIndex=-1,t.bindingIndex=-1,t.currentQueryIndex=0}function ev(t){return(N.lFrame.contextLView=Vy(t,N.lFrame.contextLView))[de]}function pt(){return N.lFrame.selectedIndex}function Xt(t){N.lFrame.selectedIndex=t}function jn(){let t=N.lFrame;return hu(t.tView,t.selectedIndex)}function dP(){N.lFrame.currentNamespace=df}function fP(){tv()}function tv(){N.lFrame.currentNamespace=null}function nv(){return N.lFrame.currentNamespace}var _f=!0;function ei(){return _f}function ti(t){_f=t}function rv(t,e,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=e.type.prototype;if(r){let s=uf(e);(n.preOrderHooks??=[]).push(t,s),(n.preOrderCheckHooks??=[]).push(t,s)}o&&(n.preOrderHooks??=[]).push(0-t,o),i&&((n.preOrderHooks??=[]).push(t,i),(n.preOrderCheckHooks??=[]).push(t,i))}function ni(t,e){for(let n=e.directiveStart,r=e.directiveEnd;n<r;n++){let i=t.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:u,ngAfterViewChecked:l,ngOnDestroy:c}=i;s&&(t.contentHooks??=[]).push(-n,s),a&&((t.contentHooks??=[]).push(n,a),(t.contentCheckHooks??=[]).push(n,a)),u&&(t.viewHooks??=[]).push(-n,u),l&&((t.viewHooks??=[]).push(n,l),(t.viewCheckHooks??=[]).push(n,l)),c!=null&&(t.destroyHooks??=[]).push(n,c)}}function Eo(t,e,n){Cf(t,e,3,n)}function wo(t,e,n,r){(t[S]&3)===n&&Cf(t,e,n,r)}function Cs(t,e){let n=t[S];(n&3)===e&&(n&=16383,n+=1,t[S]=n)}function Cf(t,e,n,r){let o=r!==void 0?t[_n]&65535:0,i=r??-1,s=e.length-1,a=0;for(let u=o;u<s;u++)if(typeof e[u+1]=="number"){if(a=e[u],r!=null&&a>=r)break}else e[u]<0&&(t[_n]+=65536),(a<i||i==-1)&&(ov(t,n,e,u),t[_n]=(t[_n]&**********)+u+2),u++}function ed(t,e){Ze(4,t,e);let n=P(null);try{e.call(t)}finally{P(n),Ze(5,t,e)}}function ov(t,e,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=t[s];o?t[S]>>14<t[_n]>>16&&(t[S]&3)===e&&(t[S]+=16384,ed(a,i)):ed(a,i)}var Mn=-1,en=class{constructor(e,n,r){this.factory=e,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function iv(t){return t instanceof en}function sv(t){return(t.flags&8)!==0}function av(t){return(t.flags&16)!==0}function Sf(t){return t!==Mn}function Ao(t){return t&32767}function uv(t){return t>>16}function Oo(t,e){let n=uv(t),r=e;for(;n>0;)r=r[kn],n--;return r}var Ks=!0;function Po(t){let e=Ks;return Ks=t,e}var lv=256,Mf=lv-1,Tf=5,cv=0,Je={};function dv(t,e,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(tr)&&(r=n[tr]),r==null&&(r=n[tr]=cv++);let o=r&Mf,i=1<<o;e.data[t+(o>>Tf)]|=i}function Fo(t,e){let n=xf(t,e);if(n!==-1)return n;let r=e[x];r.firstCreatePass&&(t.injectorIndex=e.length,Ss(r.data,t),Ss(e,null),Ss(r.blueprint,null));let o=_u(t,e),i=t.injectorIndex;if(Sf(o)){let s=Ao(o),a=Oo(o,e),u=a[x].data;for(let l=0;l<8;l++)e[i+l]=a[s+l]|u[s+l]}return e[i+8]=o,i}function Ss(t,e){t.push(0,0,0,0,0,0,0,0,e)}function xf(t,e){return t.injectorIndex===-1||t.parent&&t.parent.injectorIndex===t.injectorIndex||e[t.injectorIndex+8]===null?-1:t.injectorIndex}function _u(t,e){if(t.parent&&t.parent.injectorIndex!==-1)return t.parent.injectorIndex;let n=0,r=null,o=e;for(;o!==null;){if(r=Ff(o),r===null)return Mn;if(n++,o=o[kn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Mn}function Ys(t,e,n){dv(t,e,n)}function fv(t,e){if(e==="class")return t.classes;if(e==="style")return t.styles;let n=t.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(qd(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===e)return n[o+1];o=o+2}}}return null}function Nf(t,e,n){if(n&F.Optional||t!==void 0)return t;tu(e,"NodeInjector")}function Af(t,e,n,r){if(n&F.Optional&&r===void 0&&(r=null),!(n&(F.Self|F.Host))){let o=t[On],i=De(void 0);try{return o?o.get(e,r,n&F.Optional):jd(e,r,n&F.Optional)}finally{De(i)}}return Nf(r,e,n)}function Of(t,e,n,r=F.Default,o){if(t!==null){if(e[S]&2048&&!(r&F.Self)){let s=gv(t,e,n,r,Je);if(s!==Je)return s}let i=Pf(t,e,n,r,Je);if(i!==Je)return i}return Af(e,n,r,o)}function Pf(t,e,n,r,o){let i=pv(n);if(typeof i=="function"){if(!Df(e,t,r))return r&F.Host?Nf(o,n,r):Af(e,n,r,o);try{let s;if(s=i(r),s==null&&!(r&F.Optional))tu(n);else return s}finally{If()}}else if(typeof i=="number"){let s=null,a=xf(t,e),u=Mn,l=r&F.Host?e[Pe][_e]:null;for((a===-1||r&F.SkipSelf)&&(u=a===-1?_u(t,e):e[a+8],u===Mn||!nd(r,!1)?a=-1:(s=e[x],a=Ao(u),e=Oo(u,e)));a!==-1;){let c=e[x];if(td(i,a,c.data)){let d=hv(a,e,n,s,r,l);if(d!==Je)return d}u=e[a+8],u!==Mn&&nd(r,e[x].data[a+8]===l)&&td(i,a,e)?(s=c,a=Ao(u),e=Oo(u,e)):a=-1}}return o}function hv(t,e,n,r,o,i){let s=e[x],a=s.data[t+8],u=r==null?Jo(a)&&Ks:r!=s&&(a.type&3)!==0,l=o&F.Host&&i===a,c=bo(a,s,n,u,l);return c!==null?tn(e,s,c,a):Je}function bo(t,e,n,r,o){let i=t.providerIndexes,s=e.data,a=i&1048575,u=t.directiveStart,l=t.directiveEnd,c=i>>20,d=r?a:a+c,f=o?a+c:l;for(let h=d;h<f;h++){let p=s[h];if(h<u&&n===p||h>=u&&p.type===n)return h}if(o){let h=s[u];if(h&&lt(h)&&h.type===n)return u}return null}function tn(t,e,n,r){let o=t[n],i=e.data;if(iv(o)){let s=o;s.resolving&&kg(Rg(i[n]));let a=Po(s.canSeeViewProviders);s.resolving=!0;let u,l=s.injectImpl?De(s.injectImpl):null,c=Df(t,r,F.Default);try{o=t[n]=s.factory(void 0,i,t,r),e.firstCreatePass&&n>=r.directiveStart&&rv(n,i[n],e)}finally{l!==null&&De(l),Po(a),s.resolving=!1,If()}}return o}function pv(t){if(typeof t=="string")return t.charCodeAt(0)||0;let e=t.hasOwnProperty(tr)?t[tr]:void 0;return typeof e=="number"?e>=0?e&Mf:mv:e}function td(t,e,n){let r=1<<t;return!!(n[e+(t>>Tf)]&r)}function nd(t,e){return!(t&F.Self)&&!(t&F.Host&&e)}var Qt=class{constructor(e,n){this._tNode=e,this._lView=n}get(e,n,r){return Of(this._tNode,this._lView,e,Ko(r),n)}};function mv(){return new Qt(ue(),C())}function hP(t){return yr(()=>{let e=t.prototype.constructor,n=e[Co]||Zs(e),r=Object.prototype,o=Object.getPrototypeOf(t.prototype).constructor;for(;o&&o!==r;){let i=o[Co]||Zs(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Zs(t){return Pd(t)?()=>{let e=Zs(he(t));return e&&e()}:Kt(t)}function gv(t,e,n,r,o){let i=t,s=e;for(;i!==null&&s!==null&&s[S]&2048&&!(s[S]&512);){let a=Pf(i,s,n,r|F.Self,Je);if(a!==Je)return a;let u=i.parent;if(!u){let l=s[sf];if(l){let c=l.get(n,Je,r);if(c!==Je)return c}u=Ff(s),s=s[kn]}i=u}return o}function Ff(t){let e=t[x],n=e.type;return n===2?e.declTNode:n===1?t[_e]:null}function yv(t){return fv(ue(),t)}function rd(t,e=null,n=null,r){let o=Rf(t,e,n,r);return o.resolveInjectorInitializers(),o}function Rf(t,e=null,n=null,r,o=new Set){let i=[n||Ee,py(t)];return r=r||(typeof t=="object"?void 0:pe(t)),new sr(i,e||uu(),r||null,o)}var Vn=(()=>{let e=class e{static create(r,o){if(Array.isArray(r))return rd({name:""},o,r,"");{let i=r.name??"";return rd({name:i},r.parent,r.providers,i)}}};e.THROW_IF_NOT_FOUND=rr,e.NULL=new xo,e.\u0275prov=H({token:e,providedIn:"any",factory:()=>te(Hd)}),e.__NG_ELEMENT_ID__=-1;let t=e;return t})();var vv="ngOriginalError";function Ms(t){return t[vv]}var nn=class{constructor(){this._console=console}handleError(e){let n=this._findOriginalError(e);this._console.error("ERROR",e),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(e){let n=e&&Ms(e);for(;n&&Ms(n);)n=Ms(n);return n||null}},kf=new B("",{providedIn:"root",factory:()=>Q(nn).handleError.bind(void 0)}),Cu=(()=>{let e=class e{};e.__NG_ELEMENT_ID__=Dv,e.__NG_ENV_ID__=r=>r;let t=e;return t})(),Js=class extends Cu{constructor(e){super(),this._lView=e}onDestroy(e){return pf(this._lView,e),()=>By(this._lView,e)}};function Dv(){return new Js(C())}function Ev(){return Bn(ue(),C())}function Bn(t,e){return new kt(Fe(t,e))}var kt=(()=>{let e=class e{constructor(r){this.nativeElement=r}};e.__NG_ELEMENT_ID__=Ev;let t=e;return t})();function wv(t){return t instanceof kt?t.nativeElement:t}var Xs=class extends ye{constructor(e=!1){super(),this.destroyRef=void 0,this.__isAsync=e,of()&&(this.destroyRef=Q(Cu,{optional:!0})??void 0)}emit(e){let n=P(null);try{super.next(e)}finally{P(n)}}subscribe(e,n,r){let o=e,i=n||(()=>null),s=r;if(e&&typeof e=="object"){let u=e;o=u.next?.bind(u),i=u.error?.bind(u),s=u.complete?.bind(u)}this.__isAsync&&(i=Ts(i),o&&(o=Ts(o)),s&&(s=Ts(s)));let a=super.subscribe({next:o,error:i,complete:s});return e instanceof ee&&e.add(a),a}};function Ts(t){return e=>{setTimeout(t,void 0,e)}}var He=Xs;function bv(){return this._results[Symbol.iterator]()}var ea=class t{get changes(){return this._changes??=new He}constructor(e=!1){this._emitDistinctChangesOnly=e,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let n=t.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=bv)}get(e){return this._results[e]}map(e){return this._results.map(e)}filter(e){return this._results.filter(e)}find(e){return this._results.find(e)}reduce(e,n){return this._results.reduce(e,n)}forEach(e){this._results.forEach(e)}some(e){return this._results.some(e)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(e,n){this.dirty=!1;let r=Wg(e);(this._changesDetected=!Gg(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(e){this._onDirty=e}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}};function Lf(t){return(t.flags&128)===128}var jf=new Map,Iv=0;function _v(){return Iv++}function Cv(t){jf.set(t[Zo],t)}function Sv(t){jf.delete(t[Zo])}var od="__ngContext__";function Ot(t,e){Wt(e)?(t[od]=e[Zo],Cv(e)):t[od]=e}function Vf(t){return $f(t[ur])}function Bf(t){return $f(t[Ue])}function $f(t){for(;t!==null&&!ht(t);)t=t[Ue];return t}var ta;function pP(t){ta=t}function Mv(){if(ta!==void 0)return ta;if(typeof document<"u")return document;throw new g(210,!1)}var mP=new B("",{providedIn:"root",factory:()=>Tv}),Tv="ng",xv=new B(""),Nv=new B("",{providedIn:"platform",factory:()=>"unknown"});var gP=new B(""),yP=new B("",{providedIn:"root",factory:()=>Mv().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Av="h",Ov="b";var Pv=()=>null;function Su(t,e,n=!1){return Pv(t,e,n)}var Hf=!1,Fv=new B("",{providedIn:"root",factory:()=>Hf});var ho;function Rv(){if(ho===void 0&&(ho=null,$e.trustedTypes))try{ho=$e.trustedTypes.createPolicy("angular",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return ho}function ri(t){return Rv()?.createHTML(t)||t}var ct=class{constructor(e){this.changingThisBreaksApplicationSecurity=e}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Nd})`}},na=class extends ct{getTypeName(){return"HTML"}},ra=class extends ct{getTypeName(){return"Style"}},oa=class extends ct{getTypeName(){return"Script"}},ia=class extends ct{getTypeName(){return"URL"}},sa=class extends ct{getTypeName(){return"ResourceURL"}};function oi(t){return t instanceof ct?t.changingThisBreaksApplicationSecurity:t}function kv(t,e){let n=Lv(t);if(n!=null&&n!==e){if(n==="ResourceURL"&&e==="URL")return!0;throw new Error(`Required a safe ${e}, got a ${n} (see ${Nd})`)}return n===e}function Lv(t){return t instanceof ct&&t.getTypeName()||null}function vP(t){return new na(t)}function DP(t){return new ra(t)}function EP(t){return new oa(t)}function wP(t){return new ia(t)}function bP(t){return new sa(t)}function jv(t){let e=new ua(t);return Vv()?new aa(e):e}var aa=class{constructor(e){this.inertDocumentHelper=e}getInertBodyElement(e){e="<body><remove></remove>"+e;try{let n=new window.DOMParser().parseFromString(ri(e),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(e):(n.removeChild(n.firstChild),n)}catch{return null}}},ua=class{constructor(e){this.defaultDoc=e,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(e){let n=this.inertDocument.createElement("template");return n.innerHTML=ri(e),n}};function Vv(){try{return!!new window.DOMParser().parseFromString(ri(""),"text/html")}catch{return!1}}var Bv=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Uf(t){return t=String(t),t.match(Bv)?t:"unsafe:"+t}function mt(t){let e={};for(let n of t.split(","))e[n]=!0;return e}function Er(...t){let e={};for(let n of t)for(let r in n)n.hasOwnProperty(r)&&(e[r]=!0);return e}var zf=mt("area,br,col,hr,img,wbr"),qf=mt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Gf=mt("rp,rt"),$v=Er(Gf,qf),Hv=Er(qf,mt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Uv=Er(Gf,mt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),id=Er(zf,Hv,Uv,$v),Wf=mt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),zv=mt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),qv=mt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Gv=Er(Wf,zv,qv),Wv=mt("script,style,template"),la=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(e){let n=e.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=Yv(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Kv(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(e){let n=sd(e).toLowerCase();if(!id.hasOwnProperty(n))return this.sanitizedSomething=!0,!Wv.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=e.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Gv.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let u=i.value;Wf[a]&&(u=Uf(u)),this.buf.push(" ",s,'="',ad(u),'"')}return this.buf.push(">"),!0}endElement(e){let n=sd(e).toLowerCase();id.hasOwnProperty(n)&&!zf.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(e){this.buf.push(ad(e))}};function Qv(t,e){return(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Kv(t){let e=t.nextSibling;if(e&&t!==e.previousSibling)throw Qf(e);return e}function Yv(t){let e=t.firstChild;if(e&&Qv(t,e))throw Qf(e);return e}function sd(t){let e=t.nodeName;return typeof e=="string"?e:"FORM"}function Qf(t){return new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`)}var Zv=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Jv=/([^\#-~ |!])/g;function ad(t){return t.replace(/&/g,"&amp;").replace(Zv,function(e){let n=e.charCodeAt(0),r=e.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(Jv,function(e){return"&#"+e.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var po;function IP(t,e){let n=null;try{po=po||jv(t);let r=e?String(e):"";n=po.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=po.getInertBodyElement(r)}while(r!==i);let a=new la().sanitizeChildren(ud(n)||n);return ri(a)}finally{if(n){let r=ud(n)||n;for(;r.firstChild;)r.removeChild(r.firstChild)}}}function ud(t){return"content"in t&&Xv(t)?t.content:null}function Xv(t){return t.nodeType===Node.ELEMENT_NODE&&t.nodeName==="TEMPLATE"}var Kf=function(t){return t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL",t}(Kf||{});function _P(t){let e=eD();return e?e.sanitize(Kf.URL,t)||"":kv(t,"URL")?oi(t):Uf(Tn(t))}function eD(){let t=C();return t&&t[ze].sanitizer}var tD=/^>|^->|<!--|-->|--!>|<!-$/g,nD=/(<|>)/g,rD="\u200B$1\u200B";function oD(t){return t.replace(tD,e=>e.replace(nD,rD))}function CP(t){return t.ownerDocument.defaultView}function Yf(t){return t instanceof Function?t():t}var dr=function(t){return t[t.Important=1]="Important",t[t.DashCase=2]="DashCase",t}(dr||{}),iD;function Mu(t,e){return iD(t,e)}function Cn(t,e,n,r,o){if(r!=null){let i,s=!1;ht(r)?i=r:Wt(r)&&(s=!0,r=r[et]);let a=Xe(r);t===0&&n!==null?o==null?th(e,n,a):Ro(e,n,a,o||null,!0):t===1&&n!==null?Ro(e,n,a,o||null,!0):t===2?wD(e,a,s):t===3&&e.destroyNode(a),i!=null&&ID(e,t,i,n,o)}}function sD(t,e){return t.createText(e)}function aD(t,e,n){t.setValue(e,n)}function uD(t,e){return t.createComment(oD(e))}function Zf(t,e,n){return t.createElement(e,n)}function lD(t,e){Jf(t,e),e[et]=null,e[_e]=null}function cD(t,e,n,r,o,i){r[et]=o,r[_e]=e,ai(t,r,n,1,o,i)}function Jf(t,e){e[ze].changeDetectionScheduler?.notify(1),ai(t,e,e[G],2,null,null)}function dD(t){let e=t[ur];if(!e)return xs(t[x],t);for(;e;){let n=null;if(Wt(e))n=e[ur];else{let r=e[fe];r&&(n=r)}if(!n){for(;e&&!e[Ue]&&e!==t;)Wt(e)&&xs(e[x],e),e=e[se];e===null&&(e=t),Wt(e)&&xs(e[x],e),n=e&&e[Ue]}e=n}}function fD(t,e,n,r){let o=fe+r,i=n.length;r>0&&(n[o-1][Ue]=e),r<i-fe?(e[Ue]=n[o],$d(n,fe+r,e)):(n.push(e),e[Ue]=null),e[se]=n;let s=e[Dr];s!==null&&n!==s&&hD(s,e);let a=e[ut];a!==null&&a.insertView(t),Ws(e),e[S]|=128}function hD(t,e){let n=t[Pn],o=e[se][se][Pe];e[Pe]!==o&&(t[S]|=cu.HasTransplantedViews),n===null?t[Pn]=[e]:n.push(e)}function Xf(t,e){let n=t[Pn],r=n.indexOf(e);n.splice(r,1)}function fr(t,e){if(t.length<=fe)return;let n=fe+e,r=t[n];if(r){let o=r[Dr];o!==null&&o!==t&&Xf(o,r),e>0&&(t[n-1][Ue]=r[Ue]);let i=Mo(t,fe+e);lD(r[x],r);let s=i[ut];s!==null&&s.detachView(i[x]),r[se]=null,r[Ue]=null,r[S]&=-129}return r}function ii(t,e){if(!(e[S]&256)){let n=e[G];n.destroyNode&&ai(t,e,n,3,null,null),dD(e)}}function xs(t,e){if(e[S]&256)return;let n=P(null);try{e[S]&=-129,e[S]|=256,e[Zt]&&Zi(e[Zt]),mD(t,e),pD(t,e),e[x].type===1&&e[G].destroy();let r=e[Dr];if(r!==null&&ht(e[se])){r!==e[se]&&Xf(r,e);let o=e[ut];o!==null&&o.detachView(t)}Sv(e)}finally{P(n)}}function pD(t,e){let n=t.cleanup,r=e[ar];if(n!==null)for(let i=0;i<n.length-1;i+=2)if(typeof n[i]=="string"){let s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else{let s=r[n[i+1]];n[i].call(s)}r!==null&&(e[ar]=null);let o=e[xt];if(o!==null){e[xt]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function mD(t,e){let n;if(t!=null&&(n=t.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=e[n[r]];if(!(o instanceof en)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],u=i[s+1];Ze(4,a,u);try{u.call(a)}finally{Ze(5,a,u)}}else{Ze(4,o,i);try{i.call(o)}finally{Ze(5,o,i)}}}}}function eh(t,e,n){return gD(t,e.parent,n)}function gD(t,e,n){let r=e;for(;r!==null&&r.type&40;)e=r,r=e.parent;if(r===null)return n[et];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=t.data[r.directiveStart+o];if(i===or.None||i===or.Emulated)return null}return Fe(r,n)}}function Ro(t,e,n,r,o){t.insertBefore(e,n,r,o)}function th(t,e,n){t.appendChild(e,n)}function ld(t,e,n,r,o){r!==null?Ro(t,e,n,r,o):th(t,e,n)}function yD(t,e,n,r){t.removeChild(e,n,r)}function Tu(t,e){return t.parentNode(e)}function vD(t,e){return t.nextSibling(e)}function nh(t,e,n){return ED(t,e,n)}function DD(t,e,n){return t.type&40?Fe(t,n):null}var ED=DD,cd;function si(t,e,n,r){let o=eh(t,r,e),i=e[G],s=r.parent||e[_e],a=nh(s,r,e);if(o!=null)if(Array.isArray(n))for(let u=0;u<n.length;u++)ld(i,o,n[u],a,!1);else ld(i,o,n,a,!1);cd!==void 0&&cd(i,r,e,n,o)}function Io(t,e){if(e!==null){let n=e.type;if(n&3)return Fe(e,t);if(n&4)return ca(-1,t[e.index]);if(n&8){let r=e.child;if(r!==null)return Io(t,r);{let o=t[e.index];return ht(o)?ca(-1,o):Xe(o)}}else{if(n&32)return Mu(e,t)()||Xe(t[e.index]);{let r=rh(t,e);if(r!==null){if(Array.isArray(r))return r[0];let o=cr(t[Pe]);return Io(o,r)}else return Io(t,e.next)}}}return null}function rh(t,e){if(e!==null){let r=t[Pe][_e],o=e.projection;return r.projection[o]}return null}function ca(t,e){let n=fe+t+1;if(n<e.length){let r=e[n],o=r[x].firstChild;if(o!==null)return Io(r,o)}return e[Jt]}function wD(t,e,n){let r=Tu(t,e);r&&yD(t,r,e,n)}function xu(t,e,n,r,o,i,s){for(;n!=null;){let a=r[n.index],u=n.type;if(s&&e===0&&(a&&Ot(Xe(a),r),n.flags|=2),(n.flags&32)!==32)if(u&8)xu(t,e,n.child,r,o,i,!1),Cn(e,t,o,a,i);else if(u&32){let l=Mu(n,r),c;for(;c=l();)Cn(e,t,o,c,i);Cn(e,t,o,a,i)}else u&16?oh(t,e,r,n,o,i):Cn(e,t,o,a,i);n=s?n.projectionNext:n.next}}function ai(t,e,n,r,o,i){xu(n,r,t.firstChild,e,o,i,!1)}function bD(t,e,n){let r=e[G],o=eh(t,n,e),i=n.parent||e[_e],s=nh(i,n,e);oh(r,0,e,n,o,s)}function oh(t,e,n,r,o,i){let s=n[Pe],u=s[_e].projection[r.projection];if(Array.isArray(u))for(let l=0;l<u.length;l++){let c=u[l];Cn(e,t,o,c,i)}else{let l=u,c=s[se];Lf(r)&&(l.flags|=128),xu(t,e,l,c,o,i,!0)}}function ID(t,e,n,r,o){let i=n[Jt],s=Xe(n);i!==s&&Cn(e,t,r,i,o);for(let a=fe;a<n.length;a++){let u=n[a];ai(u[x],u,t,e,r,i)}}function _D(t,e,n,r,o){if(e)o?t.addClass(n,r):t.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:dr.DashCase;o==null?t.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=dr.Important),t.setStyle(n,r,o,i))}}function CD(t,e,n){t.setAttribute(e,"style",n)}function ih(t,e,n){n===""?t.removeAttribute(e,"class"):t.setAttribute(e,"class",n)}function sh(t,e,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Hs(t,e,r),o!==null&&ih(t,e,o),i!==null&&CD(t,e,i)}var Ce={};function SP(t=1){ah(W(),C(),pt()+t,!1)}function ah(t,e,n,r){if(!r)if((e[S]&3)===3){let i=t.preOrderCheckHooks;i!==null&&Eo(e,i,n)}else{let i=t.preOrderHooks;i!==null&&wo(e,i,0,n)}Xt(n)}function z(t,e=F.Default){let n=C();if(n===null)return te(t,e);let r=ue();return Of(r,n,he(t),e)}function MP(){let t="invalid";throw new Error(t)}function uh(t,e,n,r,o,i){let s=P(null);try{let a=null;o&Nt.SignalBased&&(a=e[r][Qe]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Nt.HasDecoratorInputTransform&&(i=t.inputTransforms[r].call(e,i)),t.setInput!==null?t.setInput(e,a,i,n,r):af(e,a,r,i)}finally{P(s)}}function SD(t,e){let n=t.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Xt(~o);else{let i=o,s=n[++r],a=n[++r];Zy(s,i);let u=e[i];a(2,u)}}}finally{Xt(-1)}}function ui(t,e,n,r,o,i,s,a,u,l,c){let d=e.blueprint.slice();return d[et]=o,d[S]=r|4|128|8|64,(l!==null||t&&t[S]&2048)&&(d[S]|=2048),hf(d),d[se]=d[kn]=t,d[de]=n,d[ze]=s||t&&t[ze],d[G]=a||t&&t[G],d[On]=u||t&&t[On]||null,d[_e]=i,d[Zo]=_v(),d[An]=c,d[sf]=l,d[Pe]=e.type==2?t[Pe]:d,d}function $n(t,e,n,r,o){let i=t.data[e];if(i===null)i=MD(t,e,n,r,o),Yy()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Gy();i.injectorIndex=s===null?-1:s.injectorIndex}return sn(i,!0),i}function MD(t,e,n,r,o){let i=yf(),s=yu(),a=s?i:i&&i.parent,u=t.data[e]=PD(t,a,n,e,r,o);return t.firstChild===null&&(t.firstChild=u),i!==null&&(s?i.child==null&&u.parent!==null&&(i.child=u):i.next===null&&(i.next=u,u.prev=i)),u}function lh(t,e,n,r){if(n===0)return-1;let o=e.length;for(let i=0;i<n;i++)e.push(r),t.blueprint.push(r),t.data.push(null);return o}function ch(t,e,n,r,o){let i=pt(),s=r&2;try{Xt(-1),s&&e.length>ae&&ah(t,e,ae,!1),Ze(s?2:0,o),n(r,o)}finally{Xt(i),Ze(s?3:1,o)}}function Nu(t,e,n){if(du(e)){let r=P(null);try{let o=e.directiveStart,i=e.directiveEnd;for(let s=o;s<i;s++){let a=t.data[s];if(a.contentQueries){let u=n[s];a.contentQueries(1,u,s)}}}finally{P(r)}}}function Au(t,e,n){mf()&&(VD(t,e,n,Fe(n,e)),(n.flags&64)===64&&hh(t,e,n))}function Ou(t,e,n=Fe){let r=e.localNames;if(r!==null){let o=e.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(e,t):t[s];t[o++]=a}}}function dh(t){let e=t.tView;return e===null||e.incompleteFirstPass?t.tView=Pu(1,null,t.template,t.decls,t.vars,t.directiveDefs,t.pipeDefs,t.viewQuery,t.schemas,t.consts,t.id):e}function Pu(t,e,n,r,o,i,s,a,u,l,c){let d=ae+r,f=d+o,h=TD(d,f),p=typeof l=="function"?l():l;return h[x]={type:t,blueprint:h,template:n,queries:null,viewQuery:a,declTNode:e,data:h.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:f,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:u,consts:p,incompleteFirstPass:!1,ssrId:c}}function TD(t,e){let n=[];for(let r=0;r<e;r++)n.push(r<t?null:Ce);return n}function xD(t,e,n,r){let i=r.get(Fv,Hf)||n===or.ShadowDom,s=t.selectRootElement(e,i);return ND(s),s}function ND(t){AD(t)}var AD=()=>null;function OD(t,e,n,r){let o=gh(e);o.push(n),t.firstCreatePass&&yh(t).push(r,o.length-1)}function PD(t,e,n,r,o,i){let s=e?e.injectorIndex:-1,a=0;return gf()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:e,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function dd(t,e,n,r,o){for(let i in e){if(!e.hasOwnProperty(i))continue;let s=e[i];if(s===void 0)continue;r??={};let a,u=Nt.None;Array.isArray(s)?(a=s[0],u=s[1]):a=s;let l=i;if(o!==null){if(!o.hasOwnProperty(i))continue;l=o[i]}t===0?fd(r,n,l,a,u):fd(r,n,l,a)}return r}function fd(t,e,n,r,o){let i;t.hasOwnProperty(n)?(i=t[n]).push(e,r):i=t[n]=[e,r],o!==void 0&&i.push(o)}function FD(t,e,n){let r=e.directiveStart,o=e.directiveEnd,i=t.data,s=e.attrs,a=[],u=null,l=null;for(let c=r;c<o;c++){let d=i[c],f=n?n.get(d):null,h=f?f.inputs:null,p=f?f.outputs:null;u=dd(0,d.inputs,c,u,h),l=dd(1,d.outputs,c,l,p);let m=u!==null&&s!==null&&!ou(e)?YD(u,c,s):null;a.push(m)}u!==null&&(u.hasOwnProperty("class")&&(e.flags|=8),u.hasOwnProperty("style")&&(e.flags|=16)),e.initialInputs=a,e.inputs=u,e.outputs=l}function RD(t){return t==="class"?"className":t==="for"?"htmlFor":t==="formaction"?"formAction":t==="innerHtml"?"innerHTML":t==="readonly"?"readOnly":t==="tabindex"?"tabIndex":t}function wr(t,e,n,r,o,i,s,a){let u=Fe(e,n),l=e.inputs,c;!a&&l!=null&&(c=l[r])?(Ru(t,n,c,r,o),Jo(e)&&kD(n,e.index)):e.type&3?(r=RD(r),o=s!=null?s(o,e.value||"",r):o,i.setProperty(u,r,o)):e.type&12}function kD(t,e){let n=Ft(e,t);n[S]&16||(n[S]|=64)}function Fu(t,e,n,r){if(mf()){let o=r===null?null:{"":-1},i=$D(t,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&fh(t,e,n,s,o,a),o&&HD(n,r,o)}n.mergedAttrs=ir(n.mergedAttrs,n.attrs)}function fh(t,e,n,r,o,i){for(let l=0;l<r.length;l++)Ys(Fo(n,e),t,r[l].type);zD(n,t.data.length,r.length);for(let l=0;l<r.length;l++){let c=r[l];c.providersResolver&&c.providersResolver(c)}let s=!1,a=!1,u=lh(t,e,r.length,null);for(let l=0;l<r.length;l++){let c=r[l];n.mergedAttrs=ir(n.mergedAttrs,c.hostAttrs),qD(t,n,e,u,c),UD(u,c,o),c.contentQueries!==null&&(n.flags|=4),(c.hostBindings!==null||c.hostAttrs!==null||c.hostVars!==0)&&(n.flags|=64);let d=c.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((t.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((t.preOrderCheckHooks??=[]).push(n.index),a=!0),u++}FD(t,n,i)}function LD(t,e,n,r,o){let i=o.hostBindings;if(i){let s=t.hostBindingOpCodes;s===null&&(s=t.hostBindingOpCodes=[]);let a=~e.index;jD(s)!=a&&s.push(a),s.push(n,r,i)}}function jD(t){let e=t.length;for(;e>0;){let n=t[--e];if(typeof n=="number"&&n<0)return n}return 0}function VD(t,e,n,r){let o=n.directiveStart,i=n.directiveEnd;Jo(n)&&GD(e,n,t.data[o+n.componentOffset]),t.firstCreatePass||Fo(n,e),Ot(r,e);let s=n.initialInputs;for(let a=o;a<i;a++){let u=t.data[a],l=tn(e,t,a,n);if(Ot(l,e),s!==null&&KD(e,a-o,l,u,n,s),lt(u)){let c=Ft(n.index,e);c[de]=tn(e,t,a,n)}}}function hh(t,e,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Jy();try{Xt(i);for(let a=r;a<o;a++){let u=t.data[a],l=e[a];Qs(a),(u.hostBindings!==null||u.hostVars!==0||u.hostAttrs!==null)&&BD(u,l)}}finally{Xt(-1),Qs(s)}}function BD(t,e){t.hostBindings!==null&&t.hostBindings(1,e)}function $D(t,e){let n=t.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(Wd(e,s.selectors,!1))if(r||(r=[]),lt(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let u=a.length;da(t,e,u)}else r.unshift(s),da(t,e,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function da(t,e,n){e.componentOffset=n,(t.components??=[]).push(e.index)}function HD(t,e,n){if(e){let r=t.localNames=[];for(let o=0;o<e.length;o+=2){let i=n[e[o+1]];if(i==null)throw new g(-301,!1);r.push(e[o],i)}}}function UD(t,e,n){if(n){if(e.exportAs)for(let r=0;r<e.exportAs.length;r++)n[e.exportAs[r]]=t;lt(e)&&(n[""]=t)}}function zD(t,e,n){t.flags|=1,t.directiveStart=e,t.directiveEnd=e+n,t.providerIndexes=e}function qD(t,e,n,r,o){t.data[r]=o;let i=o.factory||(o.factory=Kt(o.type,!0)),s=new en(i,lt(o),z);t.blueprint[r]=s,n[r]=s,LD(t,e,r,lh(t,n,o.hostVars,Ce),o)}function GD(t,e,n){let r=Fe(e,t),o=dh(n),i=t[ze].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=li(t,ui(t,o,null,s,r,e,null,i.createRenderer(r,n),null,null,null));t[e.index]=a}function WD(t,e,n,r,o,i){let s=Fe(t,e);QD(e[G],s,i,t.value,n,r,o)}function QD(t,e,n,r,o,i,s){if(i==null)t.removeAttribute(e,o,n);else{let a=s==null?Tn(i):s(i,r||"",o);t.setAttribute(e,o,a,n)}}function KD(t,e,n,r,o,i){let s=i[e];if(s!==null)for(let a=0;a<s.length;){let u=s[a++],l=s[a++],c=s[a++],d=s[a++];uh(r,n,u,l,c,d)}}function YD(t,e,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(t.hasOwnProperty(i)){r===null&&(r=[]);let s=t[i];for(let a=0;a<s.length;a+=3)if(s[a]===e){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function ph(t,e,n,r){return[t,!0,0,e,null,r,null,n,null,null]}function mh(t,e){let n=t.contentQueries;if(n!==null){let r=P(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=t.data[s];wu(i),a.contentQueries(2,e[s],s)}}}finally{P(r)}}}function li(t,e){return t[ur]?t[Jc][Ue]=e:t[ur]=e,t[Jc]=e,e}function fa(t,e,n){wu(0);let r=P(null);try{e(t,n)}finally{P(r)}}function gh(t){return t[ar]||(t[ar]=[])}function yh(t){return t.cleanup||(t.cleanup=[])}function vh(t,e,n){return(t===null||lt(t))&&(n=Ry(n[e.index])),n[G]}function Dh(t,e){let n=t[On],r=n?n.get(nn,null):null;r&&r.handleError(e)}function Ru(t,e,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],u=n[i++],l=e[s],c=t.data[s];uh(c,l,r,a,u,o)}}function Eh(t,e,n){let r=ff(e,t);aD(t[G],r,n)}function ZD(t,e){let n=Ft(e,t),r=n[x];JD(r,n);let o=n[et];o!==null&&n[An]===null&&(n[An]=Su(o,n[On])),ku(r,n,n[de])}function JD(t,e){for(let n=e.length;n<t.blueprint.length;n++)e.push(t.blueprint[n])}function ku(t,e,n){bu(e);try{let r=t.viewQuery;r!==null&&fa(1,r,n);let o=t.template;o!==null&&ch(t,e,o,1,n),t.firstCreatePass&&(t.firstCreatePass=!1),e[ut]?.finishViewCreation(t),t.staticContentQueries&&mh(t,e),t.staticViewQueries&&fa(2,t.viewQuery,n);let i=t.components;i!==null&&XD(e,i)}catch(r){throw t.firstCreatePass&&(t.incompleteFirstPass=!0,t.firstCreatePass=!1),r}finally{e[S]&=-5,Iu()}}function XD(t,e){for(let n=0;n<e.length;n++)ZD(t,e[n])}function ci(t,e,n,r){let o=P(null);try{let i=e.tView,a=t[S]&4096?4096:16,u=ui(t,i,n,a,null,e,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=t[e.index];u[Dr]=l;let c=t[ut];return c!==null&&(u[ut]=c.createEmbeddedView(i)),ku(i,u,n),u}finally{P(o)}}function wh(t,e){let n=fe+e;if(n<t.length)return t[n]}function hr(t,e){return!e||e.firstChild===null||Lf(t)}function di(t,e,n,r=!0){let o=e[x];if(fD(o,e,t,n),r){let s=ca(n,t),a=e[G],u=Tu(a,t[Jt]);u!==null&&cD(o,t[_e],a,e,u,s)}let i=e[An];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function bh(t,e){let n=fr(t,e);return n!==void 0&&ii(n[x],n),n}function ko(t,e,n,r,o=!1){for(;n!==null;){let i=e[n.index];i!==null&&r.push(Xe(i)),ht(i)&&eE(i,r);let s=n.type;if(s&8)ko(t,e,n.child,r);else if(s&32){let a=Mu(n,e),u;for(;u=a();)r.push(u)}else if(s&16){let a=rh(e,n);if(Array.isArray(a))r.push(...a);else{let u=cr(e[Pe]);ko(u[x],u,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function eE(t,e){for(let n=fe;n<t.length;n++){let r=t[n],o=r[x].firstChild;o!==null&&ko(r[x],r,o,e)}t[Jt]!==t[et]&&e.push(t[Jt])}var Ih=[];function tE(t){return t[Zt]??nE(t)}function nE(t){let e=Ih.pop()??Object.create(oE);return e.lView=t,e}function rE(t){t.lView[Zt]!==t&&(t.lView=null,Ih.push(t))}var oE=It(ge({},Wn),{consumerIsAlwaysLive:!0,consumerMarkedDirty:t=>{lr(t.lView)},consumerOnSignalRead(){this.lView[Zt]=this}}),_h=100;function Ch(t,e=!0,n=0){let r=t[ze],o=r.rendererFactory,i=!1;i||o.begin?.();try{iE(t,n)}catch(s){throw e&&Dh(t,s),s}finally{i||(o.end?.(),r.inlineEffectRunner?.flush())}}function iE(t,e){ha(t,e);let n=0;for(;gu(t);){if(n===_h)throw new g(103,!1);n++,ha(t,1)}}function sE(t,e,n,r){let o=e[S];if((o&256)===256)return;let i=!1;!i&&e[ze].inlineEffectRunner?.flush(),bu(e);let s=null,a=null;!i&&aE(t)&&(a=tE(e),s=Fr(a));try{hf(e),Ky(t.bindingStartIndex),n!==null&&ch(t,e,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=t.preOrderCheckHooks;d!==null&&Eo(e,d,null)}else{let d=t.preOrderHooks;d!==null&&wo(e,d,0,null),Cs(e,0)}if(uE(e),Sh(e,0),t.contentQueries!==null&&mh(t,e),!i)if(u){let d=t.contentCheckHooks;d!==null&&Eo(e,d)}else{let d=t.contentHooks;d!==null&&wo(e,d,1),Cs(e,1)}SD(t,e);let l=t.components;l!==null&&Th(e,l,0);let c=t.viewQuery;if(c!==null&&fa(2,c,r),!i)if(u){let d=t.viewCheckHooks;d!==null&&Eo(e,d)}else{let d=t.viewHooks;d!==null&&wo(e,d,2),Cs(e,2)}if(t.firstUpdatePass===!0&&(t.firstUpdatePass=!1),e[Do]){for(let d of e[Do])d();e[Do]=null}i||(e[S]&=-73)}catch(u){throw lr(e),u}finally{a!==null&&(Rr(a,s),rE(a)),Iu()}}function aE(t){return t.type!==2}function Sh(t,e){for(let n=Vf(t);n!==null;n=Bf(n))for(let r=fe;r<n.length;r++){let o=n[r];Mh(o,e)}}function uE(t){for(let e=Vf(t);e!==null;e=Bf(e)){if(!(e[S]&cu.HasTransplantedViews))continue;let n=e[Pn];for(let r=0;r<n.length;r++){let o=n[r],i=o[se];jy(o)}}}function lE(t,e,n){let r=Ft(e,t);Mh(r,n)}function Mh(t,e){mu(t)&&ha(t,e)}function ha(t,e){let r=t[x],o=t[S],i=t[Zt],s=!!(e===0&&o&16);if(s||=!!(o&64&&e===0),s||=!!(o&1024),s||=!!(i?.dirty&&kr(i)),i&&(i.dirty=!1),t[S]&=-9217,s)sE(r,t,r.template,t[de]);else if(o&8192){Sh(t,1);let a=r.components;a!==null&&Th(t,a,1)}}function Th(t,e,n){for(let r=0;r<e.length;r++)lE(t,e[r],n)}function Lu(t){for(t[ze].changeDetectionScheduler?.notify();t;){t[S]|=64;let e=cr(t);if(Ty(t)&&!e)return t;t=e}return null}var rn=class{get rootNodes(){let e=this._lView,n=e[x];return ko(n,e,n.firstChild,[])}constructor(e,n,r=!0){this._lView=e,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[de]}set context(e){this._lView[de]=e}get destroyed(){return(this._lView[S]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let e=this._lView[se];if(ht(e)){let n=e[No],r=n?n.indexOf(this):-1;r>-1&&(fr(e,r),Mo(n,r))}this._attachedToViewContainer=!1}ii(this._lView[x],this._lView)}onDestroy(e){pf(this._lView,e)}markForCheck(){Lu(this._cdRefInjectingView||this._lView)}detach(){this._lView[S]&=-129}reattach(){Ws(this._lView),this._lView[S]|=128}detectChanges(){this._lView[S]|=1024,Ch(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new g(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,Jf(this._lView[x],this._lView)}attachToAppRef(e){if(this._attachedToViewContainer)throw new g(902,!1);this._appRef=e,Ws(this._lView)}},dt=(()=>{let e=class e{};e.__NG_ELEMENT_ID__=fE;let t=e;return t})(),cE=dt,dE=class extends cE{constructor(e,n,r){super(),this._declarationLView=e,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(e,n){return this.createEmbeddedViewImpl(e,n)}createEmbeddedViewImpl(e,n,r){let o=ci(this._declarationLView,this._declarationTContainer,e,{embeddedViewInjector:n,dehydratedView:r});return new rn(o)}};function fE(){return fi(ue(),C())}function fi(t,e){return t.type&4?new dE(e,t,Bn(t,e)):null}var xP=new RegExp(`^(\\d+)*(${Ov}|${Av})*(.*)`);var hE=()=>null;function pr(t,e){return hE(t,e)}var pa=class{},ma=class{},Lo=class{};function pE(t){let e=Error(`No component factory found for ${pe(t)}.`);return e[mE]=t,e}var mE="ngComponent";var ga=class{resolveComponentFactory(e){throw pE(e)}},hi=(()=>{let e=class e{};e.NULL=new ga;let t=e;return t})(),ya=class{},pi=(()=>{let e=class e{constructor(){this.destroyNode=null}};e.__NG_ELEMENT_ID__=()=>gE();let t=e;return t})();function gE(){let t=C(),e=ue(),n=Ft(e.index,t);return(Wt(n)?n:t)[G]}var yE=(()=>{let e=class e{};e.\u0275prov=H({token:e,providedIn:"root",factory:()=>null});let t=e;return t})(),Ns={};var hd=new Set;function an(t){hd.has(t)||(hd.add(t),performance?.mark?.("mark_feature_usage",{detail:{feature:t}}))}function pd(...t){}function vE(){let t=typeof $e.requestAnimationFrame=="function",e=$e[t?"requestAnimationFrame":"setTimeout"],n=$e[t?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&e&&n){let r=e[Zone.__symbol__("OriginalDelegate")];r&&(e=r);let o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:e,nativeCancelAnimationFrame:n}}var Oe=class t{constructor({enableLongStackTrace:e=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new He(!1),this.onMicrotaskEmpty=new He(!1),this.onStable=new He(!1),this.onError=new He(!1),typeof Zone>"u")throw new g(908,!1);Zone.assertZonePatched();let o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),e&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=vE().nativeRequestAnimationFrame,wE(o)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!t.isInAngularZone())throw new g(909,!1)}static assertNotInAngularZone(){if(t.isInAngularZone())throw new g(909,!1)}run(e,n,r){return this._inner.run(e,n,r)}runTask(e,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,e,DE,pd,pd);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(e,n,r){return this._inner.runGuarded(e,n,r)}runOutsideAngular(e){return this._outer.run(e)}},DE={};function ju(t){if(t._nesting==0&&!t.hasPendingMicrotasks&&!t.isStable)try{t._nesting++,t.onMicrotaskEmpty.emit(null)}finally{if(t._nesting--,!t.hasPendingMicrotasks)try{t.runOutsideAngular(()=>t.onStable.emit(null))}finally{t.isStable=!0}}}function EE(t){t.isCheckStableRunning||t.lastRequestAnimationFrameId!==-1||(t.lastRequestAnimationFrameId=t.nativeRequestAnimationFrame.call($e,()=>{t.fakeTopEventTask||(t.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{t.lastRequestAnimationFrameId=-1,va(t),t.isCheckStableRunning=!0,ju(t),t.isCheckStableRunning=!1},void 0,()=>{},()=>{})),t.fakeTopEventTask.invoke()}),va(t))}function wE(t){let e=()=>{EE(t)};t._inner=t._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(bE(a))return n.invokeTask(o,i,s,a);try{return md(t),n.invokeTask(o,i,s,a)}finally{(t.shouldCoalesceEventChangeDetection&&i.type==="eventTask"||t.shouldCoalesceRunChangeDetection)&&e(),gd(t)}},onInvoke:(n,r,o,i,s,a,u)=>{try{return md(t),n.invoke(o,i,s,a,u)}finally{t.shouldCoalesceRunChangeDetection&&e(),gd(t)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&(i.change=="microTask"?(t._hasPendingMicrotasks=i.microTask,va(t),ju(t)):i.change=="macroTask"&&(t.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),t.runOutsideAngular(()=>t.onError.emit(i)),!1)})}function va(t){t._hasPendingMicrotasks||(t.shouldCoalesceEventChangeDetection||t.shouldCoalesceRunChangeDetection)&&t.lastRequestAnimationFrameId!==-1?t.hasPendingMicrotasks=!0:t.hasPendingMicrotasks=!1}function md(t){t._nesting++,t.isStable&&(t.isStable=!1,t.onUnstable.emit(null))}function gd(t){t._nesting--,ju(t)}var Da=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new He,this.onMicrotaskEmpty=new He,this.onStable=new He,this.onError=new He}run(e,n,r){return e.apply(n,r)}runGuarded(e,n,r){return e.apply(n,r)}runOutsideAngular(e){return e()}runTask(e,n,r,o){return e.apply(n,r)}};function bE(t){return!Array.isArray(t)||t.length!==1?!1:t[0].data?.__ignore_ng_zone__===!0}function IE(t="zone.js",e){return t==="noop"?new Da:t==="zone.js"?new Oe(e):t}var xh=(()=>{let e=class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){let r=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let o of r)o()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}};e.\u0275prov=H({token:e,providedIn:"root",factory:()=>new e});let t=e;return t})();function jo(t,e,n){let r=n?t.styles:null,o=n?t.classes:null,i=0;if(e!==null)for(let s=0;s<e.length;s++){let a=e[s];if(typeof a=="number")i=a;else if(i==1)o=js(o,a);else if(i==2){let u=a,l=e[++s];r=js(r,u+": "+l+";")}}n?t.styles=r:t.stylesWithoutHost=r,n?t.classes=o:t.classesWithoutHost=o}var Vo=class extends hi{constructor(e){super(),this.ngModule=e}resolveComponentFactory(e){let n=Yt(e);return new mr(n,this.ngModule)}};function yd(t){let e=[];for(let n in t){if(!t.hasOwnProperty(n))continue;let r=t[n];r!==void 0&&e.push({propName:Array.isArray(r)?r[0]:r,templateName:n})}return e}function _E(t){let e=t.toLowerCase();return e==="svg"?df:e==="math"?Oy:null}var Ea=class{constructor(e,n){this.injector=e,this.parentInjector=n}get(e,n,r){r=Ko(r);let o=this.injector.get(e,Ns,r);return o!==Ns||n===Ns?o:this.parentInjector.get(e,n,r)}},mr=class extends Lo{get inputs(){let e=this.componentDef,n=e.inputTransforms,r=yd(e.inputs);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return yd(this.componentDef.outputs)}constructor(e,n){super(),this.componentDef=e,this.ngModule=n,this.componentType=e.type,this.selector=uy(e.selectors),this.ngContentSelectors=e.ngContentSelectors?e.ngContentSelectors:[],this.isBoundToModule=!!n}create(e,n,r,o){let i=P(null);try{o=o||this.ngModule;let s=o instanceof At?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new Ea(e,s):e,u=a.get(ya,null);if(u===null)throw new g(407,!1);let l=a.get(yE,null),c=a.get(xh,null),d=a.get(pa,null),f={rendererFactory:u,sanitizer:l,inlineEffectRunner:null,afterRenderEventManager:c,changeDetectionScheduler:d},h=u.createRenderer(null,this.componentDef),p=this.componentDef.selectors[0][0]||"div",m=r?xD(h,r,this.componentDef.encapsulation,a):Zf(h,p,_E(p)),y=512;this.componentDef.signals?y|=4096:this.componentDef.onPush||(y|=16);let D=null;m!==null&&(D=Su(m,a,!0));let T=Pu(0,null,null,1,0,null,null,null,null,null,null),R=ui(null,T,null,y,null,null,f,h,a,null,D);bu(R);let L,le;try{let Y=this.componentDef,Z,ne=null;Y.findHostDirectiveDefs?(Z=[],ne=new Map,Y.findHostDirectiveDefs(Y,Z,ne),Z.push(Y)):Z=[Y];let rt=CE(R,m),wt=SE(rt,m,Y,Z,R,f,h);le=hu(T,ae),m&&xE(h,Y,m,r),n!==void 0&&NE(le,this.ngContentSelectors,n),L=TE(wt,Y,Z,ne,R,[AE]),ku(T,R,null)}finally{Iu()}return new wa(this.componentType,L,Bn(le,R),R,le)}finally{P(i)}}},wa=class extends ma{constructor(e,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new rn(o,void 0,!1),this.componentType=e}setInput(e,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[e])){if(this.previousInputValues??=new Map,this.previousInputValues.has(e)&&Object.is(this.previousInputValues.get(e),n))return;let i=this._rootLView;Ru(i[x],i,o,e,n),this.previousInputValues.set(e,n);let s=Ft(this._tNode.index,i);Lu(s)}}get injector(){return new Qt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(e){this.hostView.onDestroy(e)}};function CE(t,e){let n=t[x],r=ae;return t[r]=e,$n(n,r,2,"#host",null)}function SE(t,e,n,r,o,i,s){let a=o[x];ME(r,t,e,s);let u=null;e!==null&&(u=Su(e,o[On]));let l=i.rendererFactory.createRenderer(e,n),c=16;n.signals?c=4096:n.onPush&&(c=64);let d=ui(o,dh(n),null,c,o[t.index],t,i,l,null,null,u);return a.firstCreatePass&&da(a,t,r.length-1),li(o,d),o[t.index]=d}function ME(t,e,n,r){for(let o of t)e.mergedAttrs=ir(e.mergedAttrs,o.hostAttrs);e.mergedAttrs!==null&&(jo(e,e.mergedAttrs,!0),n!==null&&sh(r,n,e))}function TE(t,e,n,r,o,i){let s=ue(),a=o[x],u=Fe(s,o);fh(a,o,s,n,null,r);for(let c=0;c<n.length;c++){let d=s.directiveStart+c,f=tn(o,a,d,s);Ot(f,o)}hh(a,o,s),u&&Ot(u,o);let l=tn(o,a,s.directiveStart+s.componentOffset,s);if(t[de]=o[de]=l,i!==null)for(let c of i)c(l,e);return Nu(a,s,o),l}function xE(t,e,n,r){if(r)Hs(t,n,["ng-version","17.3.10"]);else{let{attrs:o,classes:i}=ly(e.selectors[0]);o&&Hs(t,n,o),i&&i.length>0&&ih(t,n,i.join(" "))}}function NE(t,e,n){let r=t.projection=[];for(let o=0;o<e.length;o++){let i=n[o];r.push(i!=null?Array.from(i):null)}}function AE(){let t=ue();ni(C()[x],t)}var gt=(()=>{let e=class e{};e.__NG_ELEMENT_ID__=OE;let t=e;return t})();function OE(){let t=ue();return Ah(t,C())}var PE=gt,Nh=class extends PE{constructor(e,n,r){super(),this._lContainer=e,this._hostTNode=n,this._hostLView=r}get element(){return Bn(this._hostTNode,this._hostLView)}get injector(){return new Qt(this._hostTNode,this._hostLView)}get parentInjector(){let e=_u(this._hostTNode,this._hostLView);if(Sf(e)){let n=Oo(e,this._hostLView),r=Ao(e),o=n[x].data[r+8];return new Qt(o,n)}else return new Qt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(e){let n=vd(this._lContainer);return n!==null&&n[e]||null}get length(){return this._lContainer.length-fe}createEmbeddedView(e,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=pr(this._lContainer,e.ssrId),a=e.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,hr(this._hostTNode,s)),a}createComponent(e,n,r,o,i){let s=e&&!My(e),a;if(s)a=n;else{let p=n||{};a=p.index,r=p.injector,o=p.projectableNodes,i=p.environmentInjector||p.ngModuleRef}let u=s?e:new mr(Yt(e)),l=r||this.parentInjector;if(!i&&u.ngModule==null){let m=(s?l:this.parentInjector).get(At,null);m&&(i=m)}let c=Yt(u.componentType??{}),d=pr(this._lContainer,c?.id??null),f=d?.firstChild??null,h=u.create(l,o,f,i);return this.insertImpl(h.hostView,a,hr(this._hostTNode,d)),h}insert(e,n){return this.insertImpl(e,n,!0)}insertImpl(e,n,r){let o=e._lView;if(Ly(o)){let a=this.indexOf(e);if(a!==-1)this.detach(a);else{let u=o[se],l=new Nh(u,u[_e],u[se]);l.detach(l.indexOf(e))}}let i=this._adjustIndex(n),s=this._lContainer;return di(s,o,i,r),e.attachToViewContainerRef(),$d(As(s),i,e),e}move(e,n){return this.insert(e,n)}indexOf(e){let n=vd(this._lContainer);return n!==null?n.indexOf(e):-1}remove(e){let n=this._adjustIndex(e,-1),r=fr(this._lContainer,n);r&&(Mo(As(this._lContainer),n),ii(r[x],r))}detach(e){let n=this._adjustIndex(e,-1),r=fr(this._lContainer,n);return r&&Mo(As(this._lContainer),n)!=null?new rn(r):null}_adjustIndex(e,n=0){return e??this.length+n}};function vd(t){return t[No]}function As(t){return t[No]||(t[No]=[])}function Ah(t,e){let n,r=e[t.index];return ht(r)?n=r:(n=ph(r,e,null,t),e[t.index]=n,li(e,n)),RE(n,e,t,r),new Nh(n,t,e)}function FE(t,e){let n=t[G],r=n.createComment(""),o=Fe(e,t),i=Tu(n,o);return Ro(n,i,r,vD(n,o),!1),r}var RE=jE,kE=()=>!1;function LE(t,e,n){return kE(t,e,n)}function jE(t,e,n,r){if(t[Jt])return;let o;n.type&8?o=Xe(r):o=FE(e,n),t[Jt]=o}var ba=class t{constructor(e){this.queryList=e,this.matches=null}clone(){return new t(this.queryList)}setDirty(){this.queryList.setDirty()}},Ia=class t{constructor(e=[]){this.queries=e}createEmbeddedView(e){let n=e.queries;if(n!==null){let r=e.contentQueries!==null?e.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new t(o)}return null}insertView(e){this.dirtyQueriesWithMatches(e)}detachView(e){this.dirtyQueriesWithMatches(e)}finishViewCreation(e){this.dirtyQueriesWithMatches(e)}dirtyQueriesWithMatches(e){for(let n=0;n<this.queries.length;n++)Vu(e,n).matches!==null&&this.queries[n].setDirty()}},Bo=class{constructor(e,n,r=null){this.flags=n,this.read=r,typeof e=="string"?this.predicate=GE(e):this.predicate=e}},_a=class t{constructor(e=[]){this.queries=e}elementStart(e,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(e,n)}elementEnd(e){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(e)}embeddedTView(e){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(e,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new t(n):null}template(e,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(e,n)}getByIndex(e){return this.queries[e]}get length(){return this.queries.length}track(e){this.queries.push(e)}},Ca=class t{constructor(e,n=-1){this.metadata=e,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(e,n){this.isApplyingToNode(n)&&this.matchTNode(e,n)}elementEnd(e){this._declarationNodeIndex===e.index&&(this._appliesToNextNode=!1)}template(e,n){this.elementStart(e,n)}embeddedTView(e,n){return this.isApplyingToNode(e)?(this.crossesNgTemplate=!0,this.addMatch(-e.index,n),new t(this.metadata)):null}isApplyingToNode(e){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=e.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(e,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(e,n,VE(n,i)),this.matchTNodeWithReadOption(e,n,bo(n,e,i,!1,!1))}else r===dt?n.type&4&&this.matchTNodeWithReadOption(e,n,-1):this.matchTNodeWithReadOption(e,n,bo(n,e,r,!1,!1))}matchTNodeWithReadOption(e,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===kt||o===gt||o===dt&&n.type&4)this.addMatch(n.index,-2);else{let i=bo(n,e,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(e,n){this.matches===null?this.matches=[e,n]:this.matches.push(e,n)}};function VE(t,e){let n=t.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===e)return n[r+1]}return null}function BE(t,e){return t.type&11?Bn(t,e):t.type&4?fi(t,e):null}function $E(t,e,n,r){return n===-1?BE(e,t):n===-2?HE(t,e,r):tn(t,t[x],n,e)}function HE(t,e,n){if(n===kt)return Bn(e,t);if(n===dt)return fi(e,t);if(n===gt)return Ah(e,t)}function Oh(t,e,n,r){let o=e[ut].queries[r];if(o.matches===null){let i=t.data,s=n.matches,a=[];for(let u=0;s!==null&&u<s.length;u+=2){let l=s[u];if(l<0)a.push(null);else{let c=i[l];a.push($E(e,c,s[u+1],n.metadata.read))}}o.matches=a}return o.matches}function Sa(t,e,n,r){let o=t.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Oh(t,e,o,n);for(let a=0;a<i.length;a+=2){let u=i[a];if(u>0)r.push(s[a/2]);else{let l=i[a+1],c=e[-u];for(let d=fe;d<c.length;d++){let f=c[d];f[Dr]===f[se]&&Sa(f[x],f,l,r)}if(c[Pn]!==null){let d=c[Pn];for(let f=0;f<d.length;f++){let h=d[f];Sa(h[x],h,l,r)}}}}}return r}function UE(t,e){return t[ut].queries[e].queryList}function Ph(t,e,n){let r=new ea((n&4)===4);return OD(t,e,r,r.destroy),(e[ut]??=new Ia).queries.push(new ba(r))-1}function zE(t,e,n){let r=W();return r.firstCreatePass&&(Fh(r,new Bo(t,e,n),-1),(e&2)===2&&(r.staticViewQueries=!0)),Ph(r,C(),e)}function qE(t,e,n,r){let o=W();if(o.firstCreatePass){let i=ue();Fh(o,new Bo(e,n,r),i.index),WE(o,t),(n&2)===2&&(o.staticContentQueries=!0)}return Ph(o,C(),n)}function GE(t){return t.split(",").map(e=>e.trim())}function Fh(t,e,n){t.queries===null&&(t.queries=new _a),t.queries.track(new Ca(e,n))}function WE(t,e){let n=t.contentQueries||(t.contentQueries=[]),r=n.length?n[n.length-1]:-1;e!==r&&n.push(t.queries.length-1,e)}function Vu(t,e){return t.queries.getByIndex(e)}function QE(t,e){let n=t[x],r=Vu(n,e);return r.crossesNgTemplate?Sa(n,t,e,[]):Oh(n,t,r,e)}function KE(t){return typeof t=="function"&&t[Qe]!==void 0}function AP(t,e){an("NgSignals");let n=uc(t),r=n[Qe];return e?.equal&&(r.equal=e.equal),n.set=o=>Ji(r,o),n.update=o=>lc(r,o),n.asReadonly=YE.bind(n),n}function YE(){let t=this[Qe];if(t.readonlyFn===void 0){let e=()=>this();e[Qe]=t,t.readonlyFn=e}return t.readonlyFn}function Rh(t){return KE(t)&&typeof t.set=="function"}function ZE(t){let e=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=t(o);n.set(o,i=s.then(tw))}return i}return $o.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(l=>{o.template=l}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let l=o.styles.length,c=o.styleUrls;o.styleUrls.forEach((d,f)=>{a.push(""),s.push(r(d).then(h=>{a[l+f]=h,c.splice(c.indexOf(d),1),c.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(l=>{a.push(l),o.styleUrl=void 0}));let u=Promise.all(s).then(()=>nw(i));e.push(u)}),XE(),Promise.all(e).then(()=>{})}var $o=new Map,JE=new Set;function XE(){let t=$o;return $o=new Map,t}function ew(){return $o.size===0}function tw(t){return typeof t=="string"?t:t.text()}function nw(t){JE.delete(t)}function rw(t){return Object.getPrototypeOf(t.prototype).constructor}function ow(t){let e=rw(t.type),n=!0,r=[t];for(;e;){let o;if(lt(t))o=e.\u0275cmp||e.\u0275dir;else{if(e.\u0275cmp)throw new g(903,!1);o=e.\u0275dir}if(o){if(n){r.push(o);let s=t;s.inputs=mo(t.inputs),s.inputTransforms=mo(t.inputTransforms),s.declaredInputs=mo(t.declaredInputs),s.outputs=mo(t.outputs);let a=o.hostBindings;a&&lw(t,a);let u=o.viewQuery,l=o.contentQueries;if(u&&aw(t,u),l&&uw(t,l),iw(t,o),Mg(t.outputs,o.outputs),lt(o)&&o.data.animation){let c=t.data;c.animation=(c.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(t),a===ow&&(n=!1)}}e=Object.getPrototypeOf(e)}sw(r)}function iw(t,e){for(let n in e.inputs){if(!e.inputs.hasOwnProperty(n)||t.inputs.hasOwnProperty(n))continue;let r=e.inputs[n];if(r!==void 0&&(t.inputs[n]=r,t.declaredInputs[n]=e.declaredInputs[n],e.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!e.inputTransforms.hasOwnProperty(o))continue;t.inputTransforms??={},t.inputTransforms[o]=e.inputTransforms[o]}}}function sw(t){let e=0,n=null;for(let r=t.length-1;r>=0;r--){let o=t[r];o.hostVars=e+=o.hostVars,o.hostAttrs=ir(o.hostAttrs,n=ir(n,o.hostAttrs))}}function mo(t){return t===xn?{}:t===Ee?[]:t}function aw(t,e){let n=t.viewQuery;n?t.viewQuery=(r,o)=>{e(r,o),n(r,o)}:t.viewQuery=e}function uw(t,e){let n=t.contentQueries;n?t.contentQueries=(r,o,i)=>{e(r,o,i),n(r,o,i)}:t.contentQueries=e}function lw(t,e){let n=t.hostBindings;n?t.hostBindings=(r,o)=>{e(r,o),n(r,o)}:t.hostBindings=e}function cw(t){let e=t.inputConfig,n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}t.inputTransforms=n}var Pt=class{},Ma=class{};var Ho=class extends Pt{constructor(e,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Vo(this);let o=Yd(e);this._bootstrapComponents=Yf(o.bootstrap),this._r3Injector=Rf(e,n,[{provide:Pt,useValue:this},{provide:hi,useValue:this.componentFactoryResolver},...r],pe(e),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(e)}get injector(){return this._r3Injector}destroy(){let e=this._r3Injector;!e.destroyed&&e.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(e){this.destroyCbs.push(e)}},Uo=class extends Ma{constructor(e){super(),this.moduleType=e}create(e){return new Ho(this.moduleType,e,[])}};function dw(t,e,n){return new Ho(t,e,n)}var Ta=class extends Pt{constructor(e){super(),this.componentFactoryResolver=new Vo(this),this.instance=null;let n=new sr([...e.providers,{provide:Pt,useValue:this},{provide:hi,useValue:this.componentFactoryResolver}],e.parent||uu(),e.debugName,new Set(["environment"]));this.injector=n,e.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(e){this.injector.onDestroy(e)}};function fw(t,e,n=null){return new Ta({providers:t,parent:e,debugName:n,runEnvironmentInitializers:!0}).injector}var Bu=(()=>{let e=class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new Kn(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let r=this.taskId++;return this.pendingTasks.add(r),r}remove(r){this.pendingTasks.delete(r),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();function kh(t){return $u(t)?Array.isArray(t)||!(t instanceof Map)&&Symbol.iterator in t:!1}function hw(t,e){if(Array.isArray(t))for(let n=0;n<t.length;n++)e(t[n]);else{let n=t[Symbol.iterator](),r;for(;!(r=n.next()).done;)e(r.value)}}function $u(t){return t!==null&&(typeof t=="function"||typeof t=="object")}function mi(t,e,n){return t[e]=n}function pw(t,e){return t[e]}function we(t,e,n){let r=t[e];return Object.is(r,n)?!1:(t[e]=n,!0)}function Hu(t,e,n,r){let o=we(t,e,n);return we(t,e+1,r)||o}function mw(t,e,n,r,o){let i=Hu(t,e,n,r);return we(t,e+2,o)||i}function gw(t){return(t.flags&32)===32}function yw(t,e,n,r,o,i,s,a,u){let l=e.consts,c=$n(e,t,4,s||null,Fn(l,a));Fu(e,n,c,Fn(l,u)),ni(e,c);let d=c.tView=Pu(2,c,r,o,i,e.directiveRegistry,e.pipeRegistry,null,e.schemas,l,null);return e.queries!==null&&(e.queries.template(e,c),d.queries=e.queries.embeddedTView(c)),c}function xa(t,e,n,r,o,i,s,a){let u=C(),l=W(),c=t+ae,d=l.firstCreatePass?yw(c,l,u,e,n,r,o,i,s):l.data[c];sn(d,!1);let f=vw(l,u,d,t);ei()&&si(l,u,f,d),Ot(f,u);let h=ph(f,u,f,d);return u[c]=h,li(u,h),LE(h,d,u),Xo(d)&&Au(l,u,d),s!=null&&Ou(u,d,a),xa}var vw=Dw;function Dw(t,e,n,r){return ti(!0),e[G].createComment("")}function Ew(t,e,n,r){let o=C(),i=Rt();if(we(o,i,e)){let s=W(),a=jn();WD(a,o,t,e,n,r)}return Ew}function Uu(t,e,n,r){return we(t,Rt(),n)?e+Tn(n)+r:Ce}function ww(t,e,n,r,o,i){let s=Qy(),a=Hu(t,s,n,o);return Du(2),a?e+Tn(n)+r+Tn(o)+i:Ce}function go(t,e){return t<<17|e<<2}function on(t){return t>>17&32767}function bw(t){return(t&2)==2}function Iw(t,e){return t&131071|e<<17}function Na(t){return t|2}function Rn(t){return(t&131068)>>2}function Os(t,e){return t&-131069|e<<2}function _w(t){return(t&1)===1}function Aa(t){return t|1}function Cw(t,e,n,r,o,i){let s=i?e.classBindings:e.styleBindings,a=on(s),u=Rn(s);t[r]=n;let l=!1,c;if(Array.isArray(n)){let d=n;c=d[1],(c===null||vr(d,c)>0)&&(l=!0)}else c=n;if(o)if(u!==0){let f=on(t[a+1]);t[r+1]=go(f,a),f!==0&&(t[f+1]=Os(t[f+1],r)),t[a+1]=Iw(t[a+1],r)}else t[r+1]=go(a,0),a!==0&&(t[a+1]=Os(t[a+1],r)),a=r;else t[r+1]=go(u,0),a===0?a=r:t[u+1]=Os(t[u+1],r),u=r;l&&(t[r+1]=Na(t[r+1])),Dd(t,c,r,!0),Dd(t,c,r,!1),Sw(e,c,t,r,i),s=go(a,u),i?e.classBindings=s:e.styleBindings=s}function Sw(t,e,n,r,o){let i=o?t.residualClasses:t.residualStyles;i!=null&&typeof e=="string"&&vr(i,e)>=0&&(n[r+1]=Aa(n[r+1]))}function Dd(t,e,n,r){let o=t[n+1],i=e===null,s=r?on(o):Rn(o),a=!1;for(;s!==0&&(a===!1||i);){let u=t[s],l=t[s+1];Mw(u,e)&&(a=!0,t[s+1]=r?Aa(l):Na(l)),s=r?on(l):Rn(l)}a&&(t[n+1]=r?Na(o):Aa(o))}function Mw(t,e){return t===null||e==null||(Array.isArray(t)?t[1]:t)===e?!0:Array.isArray(t)&&typeof e=="string"?vr(t,e)>=0:!1}var Be={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function Tw(t){return t.substring(Be.key,Be.keyEnd)}function xw(t){return Nw(t),Lh(t,jh(t,0,Be.textEnd))}function Lh(t,e){let n=Be.textEnd;return n===e?-1:(e=Be.keyEnd=Aw(t,Be.key=e,n),jh(t,e,n))}function Nw(t){Be.key=0,Be.keyEnd=0,Be.value=0,Be.valueEnd=0,Be.textEnd=t.length}function jh(t,e,n){for(;e<n&&t.charCodeAt(e)<=32;)e++;return e}function Aw(t,e,n){for(;e<n&&t.charCodeAt(e)>32;)e++;return e}function Ow(t,e,n){let r=C(),o=Rt();if(we(r,o,e)){let i=W(),s=jn();wr(i,s,r,t,e,r[G],n,!1)}return Ow}function Oa(t,e,n,r,o){let i=e.inputs,s=o?"class":"style";Ru(t,n,i[s],s,r)}function Vh(t,e,n){return $h(t,e,n,!1),Vh}function Pw(t,e){return $h(t,e,null,!0),Pw}function OP(t){Hh(Vw,Bh,t,!0)}function Bh(t,e){for(let n=xw(e);n>=0;n=Lh(e,n))Yo(t,Tw(e),!0)}function $h(t,e,n,r){let o=C(),i=W(),s=Du(2);if(i.firstUpdatePass&&zh(i,t,s,r),e!==Ce&&we(o,s,e)){let a=i.data[pt()];qh(i,a,o,o[G],t,o[s+1]=$w(e,n),r,s)}}function Hh(t,e,n,r){let o=W(),i=Du(2);o.firstUpdatePass&&zh(o,null,i,r);let s=C();if(n!==Ce&&we(s,i,n)){let a=o.data[pt()];if(Gh(a,r)&&!Uh(o,i)){let u=r?a.classesWithoutHost:a.stylesWithoutHost;u!==null&&(n=js(u,n||"")),Oa(o,a,s,n,r)}else Bw(o,a,s,s[G],s[i+1],s[i+1]=jw(t,e,n),r,i)}}function Uh(t,e){return e>=t.expandoStartIndex}function zh(t,e,n,r){let o=t.data;if(o[n+1]===null){let i=o[pt()],s=Uh(t,n);Gh(i,r)&&e===null&&!s&&(e=!1),e=Fw(o,i,e,r),Cw(o,i,e,n,s,r)}}function Fw(t,e,n,r){let o=Eu(t),i=r?e.residualClasses:e.residualStyles;if(o===null)(r?e.classBindings:e.styleBindings)===0&&(n=Ps(null,t,e,n,r),n=gr(n,e.attrs,r),i=null);else{let s=e.directiveStylingLast;if(s===-1||t[s]!==o)if(n=Ps(o,t,e,n,r),i===null){let u=Rw(t,e,r);u!==void 0&&Array.isArray(u)&&(u=Ps(null,t,e,u[1],r),u=gr(u,e.attrs,r),kw(t,e,r,u))}else i=Lw(t,e,r)}return i!==void 0&&(r?e.residualClasses=i:e.residualStyles=i),n}function Rw(t,e,n){let r=n?e.classBindings:e.styleBindings;if(Rn(r)!==0)return t[on(r)]}function kw(t,e,n,r){let o=n?e.classBindings:e.styleBindings;t[on(o)]=r}function Lw(t,e,n){let r,o=e.directiveEnd;for(let i=1+e.directiveStylingLast;i<o;i++){let s=t[i].hostAttrs;r=gr(r,s,n)}return gr(r,e.attrs,n)}function Ps(t,e,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=e[a],r=gr(r,i.hostAttrs,o),i!==t);)a++;return t!==null&&(n.directiveStylingLast=a),r}function gr(t,e,n){let r=n?1:2,o=-1;if(e!==null)for(let i=0;i<e.length;i++){let s=e[i];typeof s=="number"?o=s:o===r&&(Array.isArray(t)||(t=t===void 0?[]:["",t]),Yo(t,s,n?!0:e[++i]))}return t===void 0?null:t}function jw(t,e,n){if(n==null||n==="")return Ee;let r=[],o=oi(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)t(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&t(r,i,o[i]);else typeof o=="string"&&e(r,o);return r}function Vw(t,e,n){let r=String(e);r!==""&&!r.includes(" ")&&Yo(t,r,n)}function Bw(t,e,n,r,o,i,s,a){o===Ce&&(o=Ee);let u=0,l=0,c=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;c!==null||d!==null;){let f=u<o.length?o[u+1]:void 0,h=l<i.length?i[l+1]:void 0,p=null,m;c===d?(u+=2,l+=2,f!==h&&(p=d,m=h)):d===null||c!==null&&c<d?(u+=2,p=c):(l+=2,p=d,m=h),p!==null&&qh(t,e,n,r,p,m,s,a),c=u<o.length?o[u]:null,d=l<i.length?i[l]:null}}function qh(t,e,n,r,o,i,s,a){if(!(e.type&3))return;let u=t.data,l=u[a+1],c=_w(l)?Ed(u,e,n,o,Rn(l),s):void 0;if(!zo(c)){zo(i)||bw(l)&&(i=Ed(u,null,n,o,a,s));let d=ff(pt(),n);_D(r,s,d,o,i)}}function Ed(t,e,n,r,o,i){let s=e===null,a;for(;o>0;){let u=t[o],l=Array.isArray(u),c=l?u[1]:u,d=c===null,f=n[o+1];f===Ce&&(f=d?Ee:void 0);let h=d?Is(f,r):c===r?f:void 0;if(l&&!zo(h)&&(h=Is(u,r)),zo(h)&&(a=h,s))return a;let p=t[o+1];o=s?on(p):Rn(p)}if(e!==null){let u=i?e.residualClasses:e.residualStyles;u!=null&&(a=Is(u,r))}return a}function zo(t){return t!==void 0}function $w(t,e){return t==null||t===""||(typeof e=="string"?t=t+e:typeof t=="object"&&(t=pe(oi(t)))),t}function Gh(t,e){return(t.flags&(e?8:16))!==0}function PP(t,e,n){let r=C(),o=Uu(r,t,e,n);Hh(Yo,Bh,o,!0)}var Pa=class{destroy(e){}updateValue(e,n){}swap(e,n){let r=Math.min(e,n),o=Math.max(e,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(e,n){this.attach(n,this.detach(e))}};function Fs(t,e,n,r,o){return t===n&&Object.is(e,r)?1:Object.is(o(t,e),o(n,r))?-1:0}function Hw(t,e,n){let r,o,i=0,s=t.length-1;if(Array.isArray(e)){let a=e.length-1;for(;i<=s&&i<=a;){let u=t.at(i),l=e[i],c=Fs(i,u,i,l,n);if(c!==0){c<0&&t.updateValue(i,l),i++;continue}let d=t.at(s),f=e[a],h=Fs(s,d,a,f,n);if(h!==0){h<0&&t.updateValue(s,f),s--,a--;continue}let p=n(i,u),m=n(s,d),y=n(i,l);if(Object.is(y,m)){let D=n(a,f);Object.is(D,p)?(t.swap(i,s),t.updateValue(s,f),a--,s--):t.move(s,i),t.updateValue(i,l),i++;continue}if(r??=new qo,o??=bd(t,i,s,n),Fa(t,r,i,y))t.updateValue(i,l),i++,s++;else if(o.has(y))r.set(p,t.detach(i)),s--;else{let D=t.create(i,e[i]);t.attach(i,D),i++,s++}}for(;i<=a;)wd(t,r,n,i,e[i]),i++}else if(e!=null){let a=e[Symbol.iterator](),u=a.next();for(;!u.done&&i<=s;){let l=t.at(i),c=u.value,d=Fs(i,l,i,c,n);if(d!==0)d<0&&t.updateValue(i,c),i++,u=a.next();else{r??=new qo,o??=bd(t,i,s,n);let f=n(i,c);if(Fa(t,r,i,f))t.updateValue(i,c),i++,s++,u=a.next();else if(!o.has(f))t.attach(i,t.create(i,c)),i++,s++,u=a.next();else{let h=n(i,l);r.set(h,t.detach(i)),s--}}}for(;!u.done;)wd(t,r,n,t.length,u.value),u=a.next()}for(;i<=s;)t.destroy(t.detach(s--));r?.forEach(a=>{t.destroy(a)})}function Fa(t,e,n,r){return e!==void 0&&e.has(r)?(t.attach(n,e.get(r)),e.delete(r),!0):!1}function wd(t,e,n,r,o){if(Fa(t,e,r,n(r,o)))t.updateValue(r,o);else{let i=t.create(r,o);t.attach(r,i)}}function bd(t,e,n,r){let o=new Set;for(let i=e;i<=n;i++)o.add(r(i,t.at(i)));return o}var qo=class{constructor(){this.kvMap=new Map,this._vMap=void 0}has(e){return this.kvMap.has(e)}delete(e){if(!this.has(e))return!1;let n=this.kvMap.get(e);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(e,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(e),!0}get(e){return this.kvMap.get(e)}set(e,n){if(this.kvMap.has(e)){let r=this.kvMap.get(e);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(e,n)}forEach(e){for(let[n,r]of this.kvMap)if(e(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),e(r,n)}}};function FP(t,e,n){an("NgControlFlow");let r=C(),o=Rt(),i=ja(r,ae+t),s=0;if(we(r,o,e)){let a=P(null);try{if(bh(i,s),e!==-1){let u=Va(r[x],ae+e),l=pr(i,u.tView.ssrId),c=ci(r,u,n,{dehydratedView:l});di(i,c,s,hr(u,l))}}finally{P(a)}}else{let a=wh(i,s);a!==void 0&&(a[de]=n)}}var Ra=class{constructor(e,n,r){this.lContainer=e,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-fe}};function RP(t){return t}function kP(t,e){return e}var ka=class{constructor(e,n,r){this.hasEmptyBlock=e,this.trackByFn=n,this.liveCollection=r}};function LP(t,e,n,r,o,i,s,a,u,l,c,d,f){an("NgControlFlow");let h=u!==void 0,p=C(),m=a?s.bind(p[Pe][de]):s,y=new ka(h,m);p[ae+t]=y,xa(t+1,e,n,r,o,i),h&&xa(t+2,u,l,c,d,f)}var La=class extends Pa{constructor(e,n,r){super(),this.lContainer=e,this.hostLView=n,this.templateTNode=r,this.needsIndexUpdate=!1}get length(){return this.lContainer.length-fe}at(e){return this.getLView(e)[de].$implicit}attach(e,n){let r=n[An];this.needsIndexUpdate||=e!==this.length,di(this.lContainer,n,e,hr(this.templateTNode,r))}detach(e){return this.needsIndexUpdate||=e!==this.length-1,Uw(this.lContainer,e)}create(e,n){let r=pr(this.lContainer,this.templateTNode.tView.ssrId);return ci(this.hostLView,this.templateTNode,new Ra(this.lContainer,n,e),{dehydratedView:r})}destroy(e){ii(e[x],e)}updateValue(e,n){this.getLView(e)[de].$implicit=n}reset(){this.needsIndexUpdate=!1}updateIndexes(){if(this.needsIndexUpdate)for(let e=0;e<this.length;e++)this.getLView(e)[de].$index=e}getLView(e){return zw(this.lContainer,e)}};function jP(t){let e=P(null),n=pt();try{let r=C(),o=r[x],i=r[n];if(i.liveCollection===void 0){let a=n+1,u=ja(r,a),l=Va(o,a);i.liveCollection=new La(u,r,l)}else i.liveCollection.reset();let s=i.liveCollection;if(Hw(s,t,i.trackByFn),s.updateIndexes(),i.hasEmptyBlock){let a=Rt(),u=s.length===0;if(we(r,a,u)){let l=n+2,c=ja(r,l);if(u){let d=Va(o,l),f=pr(c,d.tView.ssrId),h=ci(r,d,void 0,{dehydratedView:f});di(c,h,0,hr(d,f))}else bh(c,0)}}}finally{P(e)}}function ja(t,e){return t[e]}function Uw(t,e){return fr(t,e)}function zw(t,e){return wh(t,e)}function Va(t,e){return hu(t,e)}function qw(t,e,n,r,o,i){let s=e.consts,a=Fn(s,o),u=$n(e,t,2,r,a);return Fu(e,n,u,Fn(s,i)),u.attrs!==null&&jo(u,u.attrs,!1),u.mergedAttrs!==null&&jo(u,u.mergedAttrs,!0),e.queries!==null&&e.queries.elementStart(e,u),u}function Wh(t,e,n,r){let o=C(),i=W(),s=ae+t,a=o[G],u=i.firstCreatePass?qw(s,i,o,e,n,r):i.data[s],l=Ww(i,o,u,a,e,t);o[s]=l;let c=Xo(u);return sn(u,!0),sh(a,l,u),!gw(u)&&ei()&&si(i,o,l,u),$y()===0&&Ot(l,o),Hy(),c&&(Au(i,o,u),Nu(i,u,o)),r!==null&&Ou(o,u),Wh}function Qh(){let t=ue();yu()?vu():(t=t.parent,sn(t,!1));let e=t;zy(e)&&qy(),Uy();let n=W();return n.firstCreatePass&&(ni(n,t),du(t)&&n.queries.elementEnd(t)),e.classesWithoutHost!=null&&sv(e)&&Oa(n,e,C(),e.classesWithoutHost,!0),e.stylesWithoutHost!=null&&av(e)&&Oa(n,e,C(),e.stylesWithoutHost,!1),Qh}function Gw(t,e,n,r){return Wh(t,e,n,r),Qh(),Gw}var Ww=(t,e,n,r,o,i)=>(ti(!0),Zf(r,o,nv()));function Qw(t,e,n,r,o){let i=e.consts,s=Fn(i,r),a=$n(e,t,8,"ng-container",s);s!==null&&jo(a,s,!0);let u=Fn(i,o);return Fu(e,n,a,u),e.queries!==null&&e.queries.elementStart(e,a),a}function Kh(t,e,n){let r=C(),o=W(),i=t+ae,s=o.firstCreatePass?Qw(i,o,r,e,n):o.data[i];sn(s,!0);let a=Yw(o,r,s,t);return r[i]=a,ei()&&si(o,r,a,s),Ot(a,r),Xo(s)&&(Au(o,r,s),Nu(o,s,r)),n!=null&&Ou(r,s),Kh}function Yh(){let t=ue(),e=W();return yu()?vu():(t=t.parent,sn(t,!1)),e.firstCreatePass&&(ni(e,t),du(t)&&e.queries.elementEnd(t)),Yh}function Kw(t,e,n){return Kh(t,e,n),Yh(),Kw}var Yw=(t,e,n,r)=>(ti(!0),uD(e[G],""));function VP(){return C()}function Zw(t,e,n){let r=C(),o=Rt();if(we(r,o,e)){let i=W(),s=jn();wr(i,s,r,t,e,r[G],n,!0)}return Zw}function Jw(t,e,n){let r=C(),o=Rt();if(we(r,o,e)){let i=W(),s=jn(),a=Eu(i.data),u=vh(a,s,r);wr(i,s,r,t,e,u,n,!0)}return Jw}var Gt=void 0;function Xw(t){let e=t,n=Math.floor(Math.abs(t)),r=t.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var eb=["en",[["a","p"],["AM","PM"],Gt],[["AM","PM"],Gt,Gt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Gt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Gt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Gt,"{1} 'at' {0}",Gt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",Xw],Rs={};function Se(t){let e=tb(t),n=Id(e);if(n)return n;let r=e.split("-")[0];if(n=Id(r),n)return n;if(r==="en")return eb;throw new g(701,!1)}function Id(t){return t in Rs||(Rs[t]=$e.ng&&$e.ng.common&&$e.ng.common.locales&&$e.ng.common.locales[t]),Rs[t]}var J=function(t){return t[t.LocaleId=0]="LocaleId",t[t.DayPeriodsFormat=1]="DayPeriodsFormat",t[t.DayPeriodsStandalone=2]="DayPeriodsStandalone",t[t.DaysFormat=3]="DaysFormat",t[t.DaysStandalone=4]="DaysStandalone",t[t.MonthsFormat=5]="MonthsFormat",t[t.MonthsStandalone=6]="MonthsStandalone",t[t.Eras=7]="Eras",t[t.FirstDayOfWeek=8]="FirstDayOfWeek",t[t.WeekendRange=9]="WeekendRange",t[t.DateFormat=10]="DateFormat",t[t.TimeFormat=11]="TimeFormat",t[t.DateTimeFormat=12]="DateTimeFormat",t[t.NumberSymbols=13]="NumberSymbols",t[t.NumberFormats=14]="NumberFormats",t[t.CurrencyCode=15]="CurrencyCode",t[t.CurrencySymbol=16]="CurrencySymbol",t[t.CurrencyName=17]="CurrencyName",t[t.Currencies=18]="Currencies",t[t.Directionality=19]="Directionality",t[t.PluralCase=20]="PluralCase",t[t.ExtraData=21]="ExtraData",t}(J||{});function tb(t){return t.toLowerCase().replace(/_/g,"-")}var Go="en-US";var nb=Go;function rb(t){typeof t=="string"&&(nb=t.toLowerCase().replace(/_/g,"-"))}function ob(t,e,n,r){let o=C(),i=W(),s=ue();return zu(i,o,o[G],s,t,e,r),ob}function ib(t,e){let n=ue(),r=C(),o=W(),i=Eu(o.data),s=vh(i,n,r);return zu(o,r,s,n,t,e),ib}function sb(t,e,n,r){let o=t.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=e[ar],u=o[i+2];return a.length>u?a[u]:null}typeof s=="string"&&(i+=2)}return null}function zu(t,e,n,r,o,i,s){let a=Xo(r),l=t.firstCreatePass&&yh(t),c=e[de],d=gh(e),f=!0;if(r.type&3||s){let m=Fe(r,e),y=s?s(m):m,D=d.length,T=s?L=>s(Xe(L[r.index])):r.index,R=null;if(!s&&a&&(R=sb(t,e,o,r.index)),R!==null){let L=R.__ngLastListenerFn__||R;L.__ngNextListenerFn__=i,R.__ngLastListenerFn__=i,f=!1}else{i=Cd(r,e,c,i,!1);let L=n.listen(y,o,i);d.push(i,L),l&&l.push(o,T,D,D+1)}}else i=Cd(r,e,c,i,!1);let h=r.outputs,p;if(f&&h!==null&&(p=h[o])){let m=p.length;if(m)for(let y=0;y<m;y+=2){let D=p[y],T=p[y+1],le=e[D][T].subscribe(i),Y=d.length;d.push(i,le),l&&l.push(o,r.index,Y,-(Y+1))}}}function _d(t,e,n,r){let o=P(null);try{return Ze(6,e,n),n(r)!==!1}catch(i){return Dh(t,i),!1}finally{Ze(7,e,n),P(o)}}function Cd(t,e,n,r,o){return function i(s){if(s===Function)return r;let a=t.componentOffset>-1?Ft(t.index,e):e;Lu(a);let u=_d(e,n,r,s),l=i.__ngNextListenerFn__;for(;l;)u=_d(e,n,l,s)&&u,l=l.__ngNextListenerFn__;return o&&u===!1&&s.preventDefault(),u}}function BP(t=1){return ev(t)}function ab(t,e){let n=null,r=ry(t);for(let o=0;o<e.length;o++){let i=e[o];if(i==="*"){n=o;continue}if(r===null?Wd(t,i,!0):sy(r,i))return o}return n}function $P(t){let e=C()[Pe][_e];if(!e.projection){let n=t?t.length:1,r=e.projection=Qg(n,null),o=r.slice(),i=e.child;for(;i!==null;){let s=t?ab(i,t):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i),i=i.next}}}function HP(t,e=0,n){let r=C(),o=W(),i=$n(o,ae+t,16,null,n||null);i.projection===null&&(i.projection=e),vu(),(!r[An]||gf())&&(i.flags&32)!==32&&bD(o,r,i)}function ub(t,e,n){return Zh(t,"",e,"",n),ub}function Zh(t,e,n,r,o){let i=C(),s=Uu(i,e,n,r);if(s!==Ce){let a=W(),u=jn();wr(a,u,i,t,s,i[G],o,!1)}return Zh}function UP(t,e,n,r){qE(t,e,n,r)}function zP(t,e,n){zE(t,e,n)}function qP(t){let e=C(),n=W(),r=vf();wu(r+1);let o=Vu(n,r);if(t.dirty&&ky(e)===((o.metadata.flags&2)===2)){if(o.matches===null)t.reset([]);else{let i=QE(e,r);t.reset(i,wv),t.notifyOnChanges()}return!0}return!1}function GP(){return UE(C(),vf())}function lb(t,e,n,r){n>=t.data.length&&(t.data[n]=null,t.blueprint[n]=null),e[n]=r}function WP(t){let e=Wy();return pu(e,ae+t)}function QP(t,e=""){let n=C(),r=W(),o=t+ae,i=r.firstCreatePass?$n(r,o,1,e,null):r.data[o],s=cb(r,n,i,e,t);n[o]=s,ei()&&si(r,n,s,i),sn(i,!1)}var cb=(t,e,n,r,o)=>(ti(!0),sD(e[G],r));function db(t){return Jh("",t,""),db}function Jh(t,e,n){let r=C(),o=Uu(r,t,e,n);return o!==Ce&&Eh(r,pt(),o),Jh}function fb(t,e,n,r,o){let i=C(),s=ww(i,t,e,n,r,o);return s!==Ce&&Eh(i,pt(),s),fb}function hb(t,e,n){Rh(e)&&(e=e());let r=C(),o=Rt();if(we(r,o,e)){let i=W(),s=jn();wr(i,s,r,t,e,r[G],n,!1)}return hb}function KP(t,e){let n=Rh(t);return n&&t.set(e),n}function pb(t,e){let n=C(),r=W(),o=ue();return zu(r,n,n[G],o,t,e),pb}function mb(t,e,n){let r=W();if(r.firstCreatePass){let o=lt(t);Ba(n,r.data,r.blueprint,o,!0),Ba(e,r.data,r.blueprint,o,!1)}}function Ba(t,e,n,r,o){if(t=he(t),Array.isArray(t))for(let i=0;i<t.length;i++)Ba(t[i],e,n,r,o);else{let i=W(),s=C(),a=ue(),u=Nn(t)?t:he(t.provide),l=rf(t),c=a.providerIndexes&1048575,d=a.directiveStart,f=a.providerIndexes>>20;if(Nn(t)||!t.multi){let h=new en(l,o,z),p=Ls(u,e,o?c:c+f,d);p===-1?(Ys(Fo(a,s),i,u),ks(i,t,e.length),e.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(h),s.push(h)):(n[p]=h,s[p]=h)}else{let h=Ls(u,e,c+f,d),p=Ls(u,e,c,c+f),m=h>=0&&n[h],y=p>=0&&n[p];if(o&&!y||!o&&!m){Ys(Fo(a,s),i,u);let D=vb(o?yb:gb,n.length,o,r,l);!o&&y&&(n[p].providerFactory=D),ks(i,t,e.length,0),e.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(D),s.push(D)}else{let D=Xh(n[o?p:h],l,!o&&r);ks(i,t,h>-1?h:p,D)}!o&&r&&y&&n[p].componentProviders++}}}function ks(t,e,n,r){let o=Nn(e),i=vy(e);if(o||i){let u=(i?he(e.useClass):e).prototype.ngOnDestroy;if(u){let l=t.destroyHooks||(t.destroyHooks=[]);if(!o&&e.multi){let c=l.indexOf(n);c===-1?l.push(n,[r,u]):l[c+1].push(r,u)}else l.push(n,u)}}}function Xh(t,e,n){return n&&t.componentProviders++,t.multi.push(e)-1}function Ls(t,e,n,r){for(let o=n;o<r;o++)if(e[o]===t)return o;return-1}function gb(t,e,n,r){return $a(this.multi,[])}function yb(t,e,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=tn(n,n[x],this.providerFactory.index,r);i=a.slice(0,s),$a(o,i);for(let u=s;u<a.length;u++)i.push(a[u])}else i=[],$a(o,i);return i}function $a(t,e){for(let n=0;n<t.length;n++){let r=t[n];e.push(r())}return e}function vb(t,e,n,r,o){let i=new en(t,n,z);return i.multi=[],i.index=e,i.componentProviders=0,Xh(i,o,r&&!n),i}function YP(t,e=[]){return n=>{n.providersResolver=(r,o)=>mb(r,o?o(t):t,e)}}var Db=(()=>{let e=class e{constructor(r){this._injector=r,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(r){if(!r.standalone)return null;if(!this.cachedInjectors.has(r)){let o=Xd(!1,r.type),i=o.length>0?fw([o],this._injector,`Standalone[${r.type.name}]`):null;this.cachedInjectors.set(r,i)}return this.cachedInjectors.get(r)}ngOnDestroy(){try{for(let r of this.cachedInjectors.values())r!==null&&r.destroy()}finally{this.cachedInjectors.clear()}}};e.\u0275prov=H({token:e,providedIn:"environment",factory:()=>new e(te(At))});let t=e;return t})();function ZP(t){an("NgStandalone"),t.getStandaloneInjector=e=>e.get(Db).getOrCreateStandaloneInjector(t)}function JP(t,e,n){let r=Ln()+t,o=C();return o[r]===Ce?mi(o,r,n?e.call(n):e()):pw(o,r)}function XP(t,e,n,r){return ep(C(),Ln(),t,e,n,r)}function eF(t,e,n,r,o){return tp(C(),Ln(),t,e,n,r,o)}function tF(t,e,n,r,o,i){return Eb(C(),Ln(),t,e,n,r,o,i)}function qu(t,e){let n=t[e];return n===Ce?void 0:n}function ep(t,e,n,r,o,i){let s=e+n;return we(t,s,o)?mi(t,s+1,i?r.call(i,o):r(o)):qu(t,s+1)}function tp(t,e,n,r,o,i,s){let a=e+n;return Hu(t,a,o,i)?mi(t,a+2,s?r.call(s,o,i):r(o,i)):qu(t,a+2)}function Eb(t,e,n,r,o,i,s,a){let u=e+n;return mw(t,u,o,i,s)?mi(t,u+3,a?r.call(a,o,i,s):r(o,i,s)):qu(t,u+3)}function nF(t,e){let n=W(),r,o=t+ae;n.firstCreatePass?(r=wb(e,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Kt(r.type,!0)),s,a=De(z);try{let u=Po(!1),l=i();return Po(u),lb(n,C(),o,l),l}finally{De(a)}}function wb(t,e){if(e)for(let n=e.length-1;n>=0;n--){let r=e[n];if(t===r.name)return r}}function rF(t,e,n){let r=t+ae,o=C(),i=pu(o,r);return np(o,r)?ep(o,Ln(),e,i.transform,n,i):i.transform(n)}function oF(t,e,n,r){let o=t+ae,i=C(),s=pu(i,o);return np(i,o)?tp(i,Ln(),e,s.transform,n,r,s):s.transform(n,r)}function np(t,e){return t[x].data[e].pure}function iF(t,e){return fi(t,e)}var yo=null;function bb(t){yo!==null&&(t.defaultEncapsulation!==yo.defaultEncapsulation||t.preserveWhitespaces!==yo.preserveWhitespaces)||(yo=t)}var sF=(()=>{let e=class e{log(r){console.log(r)}warn(r){console.warn(r)}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"platform"});let t=e;return t})();var Ib=new B(""),_b=new B(""),aF=(()=>{let e=class e{constructor(r,o,i){this._ngZone=r,this.registry=o,this._pendingCount=0,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,Gu||(Sb(i),i.addToWindow(o)),this._watchAngularEvents(),r.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{Oe.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&this._pendingCount===0&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let r=this._callbacks.pop();clearTimeout(r.timeoutId),r.doneCb()}});else{let r=this.getPendingTasks();this._callbacks=this._callbacks.filter(o=>o.updateCb&&o.updateCb(r)?(clearTimeout(o.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(r=>({source:r.source,creationLocation:r.creationLocation,data:r.data})):[]}addCallback(r,o,i){let s=-1;o&&o>0&&(s=setTimeout(()=>{this._callbacks=this._callbacks.filter(a=>a.timeoutId!==s),r()},o)),this._callbacks.push({doneCb:r,timeoutId:s,updateCb:i})}whenStable(r,o,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(r,o,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(r){this.registry.registerApplication(r,this)}unregisterApplication(r){this.registry.unregisterApplication(r)}findProviders(r,o,i){return[]}};e.\u0275fac=function(o){return new(o||e)(te(Oe),te(Cb),te(_b))},e.\u0275prov=H({token:e,factory:e.\u0275fac});let t=e;return t})(),Cb=(()=>{let e=class e{constructor(){this._applications=new Map}registerApplication(r,o){this._applications.set(r,o)}unregisterApplication(r){this._applications.delete(r)}unregisterAllApplications(){this._applications.clear()}getTestability(r){return this._applications.get(r)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(r,o=!0){return Gu?.findTestabilityInTree(this,r,o)??null}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"platform"});let t=e;return t})();function Sb(t){Gu=t}var Gu;function Wu(t){return!!t&&typeof t.then=="function"}function rp(t){return!!t&&typeof t.subscribe=="function"}var Mb=new B(""),op=(()=>{let e=class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((r,o)=>{this.resolve=r,this.reject=o}),this.appInits=Q(Mb,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let r=[];for(let i of this.appInits){let s=i();if(Wu(s))r.push(s);else if(rp(s)){let a=new Promise((u,l)=>{s.subscribe({complete:u,error:l})});r.push(a)}}let o=()=>{this.done=!0,this.resolve()};Promise.all(r).then(()=>{o()}).catch(i=>{this.reject(i)}),r.length===0&&o(),this.initialized=!0}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})(),Tb=new B("");function xb(){ac(()=>{throw new g(600,!1)})}function Nb(t){return t.isBoundToModule}function Ab(t,e,n){try{let r=n();return Wu(r)?r.catch(o=>{throw e.runOutsideAngular(()=>t.handleError(o)),o}):r}catch(r){throw e.runOutsideAngular(()=>t.handleError(r)),r}}function ip(t,e){return Array.isArray(e)?e.reduce(ip,t):ge(ge({},t),e)}var Qu=(()=>{let e=class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=Q(kf),this.afterRenderEffectManager=Q(xh),this.externalTestViews=new Set,this.beforeRender=new ye,this.afterTick=new ye,this.componentTypes=[],this.components=[],this.isStable=Q(Bu).hasPendingTasks.pipe(Ie(r=>!r)),this._injector=Q(At)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(r,o){let i=r instanceof Lo;if(!this._injector.get(op).done){let h=!i&&fy(r),p=!1;throw new g(405,p)}let a;i?a=r:a=this._injector.get(hi).resolveComponentFactory(r),this.componentTypes.push(a.componentType);let u=Nb(a)?void 0:this._injector.get(Pt),l=o||a.selector,c=a.create(Vn.NULL,[],l,u),d=c.location.nativeElement,f=c.injector.get(Ib,null);return f?.registerApplication(d),c.onDestroy(()=>{this.detachView(c.hostView),_o(this.components,c),f?.unregisterApplication(d)}),this._loadComponent(c),c}tick(){this._tick(!0)}_tick(r){if(this._runningTick)throw new g(101,!1);let o=P(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(r)}catch(i){this.internalErrorHandler(i)}finally{this.afterTick.next(),this._runningTick=!1,P(o)}}detectChangesInAttachedViews(r){let o=0,i=this.afterRenderEffectManager;for(;;){if(o===_h)throw new g(103,!1);if(r){let s=o===0;this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:u}of this._views)Ob(a,s,u)}if(o++,i.executeInternalCallbacks(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>Ha(s))&&(i.execute(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>Ha(s))))break}}attachView(r){let o=r;this._views.push(o),o.attachToAppRef(this)}detachView(r){let o=r;_o(this._views,o),o.detachFromAppRef()}_loadComponent(r){this.attachView(r.hostView),this.tick(),this.components.push(r);let o=this._injector.get(Tb,[]);[...this._bootstrapListeners,...o].forEach(i=>i(r))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(r=>r()),this._views.slice().forEach(r=>r.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(r){return this._destroyListeners.push(r),()=>_o(this._destroyListeners,r)}destroy(){if(this._destroyed)throw new g(406,!1);let r=this._injector;r.destroy&&!r.destroyed&&r.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();function _o(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}function Ob(t,e,n){!e&&!Ha(t)||Pb(t,n,e)}function Ha(t){return gu(t)}function Pb(t,e,n){let r;n?(r=0,t[S]|=1024):t[S]&64?r=0:r=1,Ch(t,e,r)}var Ua=class{constructor(e,n){this.ngModuleFactory=e,this.componentFactories=n}},uF=(()=>{let e=class e{compileModuleSync(r){return new Uo(r)}compileModuleAsync(r){return Promise.resolve(this.compileModuleSync(r))}compileModuleAndAllComponentsSync(r){let o=this.compileModuleSync(r),i=Yd(r),s=Yf(i.declarations).reduce((a,u)=>{let l=Yt(u);return l&&a.push(new mr(l)),a},[]);return new Ua(o,s)}compileModuleAndAllComponentsAsync(r){return Promise.resolve(this.compileModuleAndAllComponentsSync(r))}clearCache(){}clearCacheFor(r){}getModuleId(r){}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})(),Fb=new B("");function Rb(t,e,n){let r=new Uo(n);return Promise.resolve(r)}function Sd(t){for(let e=t.length-1;e>=0;e--)if(t[e]!==void 0)return t[e]}var kb=(()=>{let e=class e{constructor(){this.zone=Q(Oe),this.applicationRef=Q(Qu)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();function Lb(t){return[{provide:Oe,useFactory:t},{provide:To,multi:!0,useFactory:()=>{let e=Q(kb,{optional:!0});return()=>e.initialize()}},{provide:To,multi:!0,useFactory:()=>{let e=Q(Bb);return()=>{e.initialize()}}},{provide:kf,useFactory:jb}]}function jb(){let t=Q(Oe),e=Q(nn);return n=>t.runOutsideAngular(()=>e.handleError(n))}function Vb(t){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:t?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:t?.runCoalescing??!1}}var Bb=(()=>{let e=class e{constructor(){this.subscription=new ee,this.initialized=!1,this.zone=Q(Oe),this.pendingTasks=Q(Bu)}initialize(){if(this.initialized)return;this.initialized=!0;let r=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(r=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{Oe.assertNotInAngularZone(),queueMicrotask(()=>{r!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(r),r=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{Oe.assertInAngularZone(),r??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();function $b(){return typeof $localize<"u"&&$localize.locale||Go}var br=new B("",{providedIn:"root",factory:()=>Q(br,F.Optional|F.SkipSelf)||$b()});var sp=new B(""),ap=(()=>{let e=class e{constructor(r){this._injector=r,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(r,o){let i=IE(o?.ngZone,Vb({eventCoalescing:o?.ngZoneEventCoalescing,runCoalescing:o?.ngZoneRunCoalescing}));return i.run(()=>{let s=dw(r.moduleType,this.injector,Lb(()=>i)),a=s.injector.get(nn,null);return i.runOutsideAngular(()=>{let u=i.onError.subscribe({next:l=>{a.handleError(l)}});s.onDestroy(()=>{_o(this._modules,s),u.unsubscribe()})}),Ab(a,i,()=>{let u=s.injector.get(op);return u.runInitializers(),u.donePromise.then(()=>{let l=s.injector.get(br,Go);return rb(l||Go),this._moduleDoBootstrap(s),s})})})}bootstrapModule(r,o=[]){let i=ip({},o);return Rb(this.injector,i,r).then(s=>this.bootstrapModuleFactory(s,i))}_moduleDoBootstrap(r){let o=r.injector.get(Qu);if(r._bootstrapComponents.length>0)r._bootstrapComponents.forEach(i=>o.bootstrap(i));else if(r.instance.ngDoBootstrap)r.instance.ngDoBootstrap(o);else throw new g(-403,!1);this._modules.push(r)}onDestroy(r){this._destroyListeners.push(r)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new g(404,!1);this._modules.slice().forEach(o=>o.destroy()),this._destroyListeners.forEach(o=>o());let r=this._injector.get(sp,null);r&&(r.forEach(o=>o()),r.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}};e.\u0275fac=function(o){return new(o||e)(te(Vn))},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"platform"});let t=e;return t})(),nr=null,up=new B("");function Hb(t){if(nr&&!nr.get(up,!1))throw new g(400,!1);xb(),nr=t;let e=t.get(ap);return Gb(t),e}function Ub(t,e,n=[]){let r=`Platform: ${e}`,o=new B(r);return(i=[])=>{let s=lp();if(!s||s.injector.get(up,!1)){let a=[...n,...i,{provide:o,useValue:!0}];t?t(a):Hb(zb(a,r))}return qb(o)}}function zb(t=[],e){return Vn.create({name:e,providers:[{provide:nf,useValue:"platform"},{provide:sp,useValue:new Set([()=>nr=null])},...t]})}function qb(t){let e=lp();if(!e)throw new g(401,!1);return e}function lp(){return nr?.get(ap)??null}function Gb(t){t.get(xv,null)?.forEach(n=>n())}function lF(){return!1}var Ku=(()=>{let e=class e{};e.__NG_ELEMENT_ID__=Wb;let t=e;return t})();function Wb(t){return Qb(ue(),C(),(t&16)===16)}function Qb(t,e,n){if(Jo(t)&&!n){let r=Ft(t.index,e);return new rn(r,r)}else if(t.type&47){let r=e[Pe];return new rn(r,e)}return null}var za=class{constructor(){}supports(e){return kh(e)}create(e){return new qa(e)}},Kb=(t,e)=>e,qa=class{constructor(e){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=e||Kb}forEachItem(e){let n;for(n=this._itHead;n!==null;n=n._next)e(n)}forEachOperation(e){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Md(r,o,i)?n:r,a=Md(s,o,i),u=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,c=u-o;if(l!=c){for(let f=0;f<l;f++){let h=f<i.length?i[f]:i[f]=0,p=h+f;c<=p&&p<l&&(i[f]=h+1)}let d=s.previousIndex;i[d]=c-l}}a!==u&&e(s,a,u)}}forEachPreviousItem(e){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)e(n)}forEachAddedItem(e){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)e(n)}forEachMovedItem(e){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)e(n)}forEachRemovedItem(e){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)e(n)}forEachIdentityChange(e){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)e(n)}diff(e){if(e==null&&(e=[]),!kh(e))throw new g(900,!1);return this.check(e)?this:null}onDestroy(){}check(e){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(e)){this.length=e.length;for(let a=0;a<this.length;a++)i=e[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,hw(e,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=e,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let e;for(e=this._previousItHead=this._itHead;e!==null;e=e._next)e._nextPrevious=e._next;for(e=this._additionsHead;e!==null;e=e._nextAdded)e.previousIndex=e.currentIndex;for(this._additionsHead=this._additionsTail=null,e=this._movesHead;e!==null;e=e._nextMoved)e.previousIndex=e.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(e,n,r,o){let i;return e===null?i=this._itTail:(i=e._prev,this._remove(e)),e=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),e!==null?(Object.is(e.item,n)||this._addIdentityChange(e,n),this._reinsertAfter(e,i,o)):(e=this._linkedRecords===null?null:this._linkedRecords.get(r,o),e!==null?(Object.is(e.item,n)||this._addIdentityChange(e,n),this._moveAfter(e,i,o)):e=this._addAfter(new Ga(n,r),i,o)),e}_verifyReinsertion(e,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?e=this._reinsertAfter(i,e._prev,o):e.currentIndex!=o&&(e.currentIndex=o,this._addToMoves(e,o)),e}_truncate(e){for(;e!==null;){let n=e._next;this._addToRemovals(this._unlink(e)),e=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(e,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(e);let o=e._prevRemoved,i=e._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(e,n,r),this._addToMoves(e,r),e}_moveAfter(e,n,r){return this._unlink(e),this._insertAfter(e,n,r),this._addToMoves(e,r),e}_addAfter(e,n,r){return this._insertAfter(e,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=e:this._additionsTail=this._additionsTail._nextAdded=e,e}_insertAfter(e,n,r){let o=n===null?this._itHead:n._next;return e._next=o,e._prev=n,o===null?this._itTail=e:o._prev=e,n===null?this._itHead=e:n._next=e,this._linkedRecords===null&&(this._linkedRecords=new Wo),this._linkedRecords.put(e),e.currentIndex=r,e}_remove(e){return this._addToRemovals(this._unlink(e))}_unlink(e){this._linkedRecords!==null&&this._linkedRecords.remove(e);let n=e._prev,r=e._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,e}_addToMoves(e,n){return e.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=e:this._movesTail=this._movesTail._nextMoved=e),e}_addToRemovals(e){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Wo),this._unlinkedRecords.put(e),e.currentIndex=null,e._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=e,e._prevRemoved=null):(e._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=e),e}_addIdentityChange(e,n){return e.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=e:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=e,e}},Ga=class{constructor(e,n){this.item=e,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},Wa=class{constructor(){this._head=null,this._tail=null}add(e){this._head===null?(this._head=this._tail=e,e._nextDup=null,e._prevDup=null):(this._tail._nextDup=e,e._prevDup=this._tail,e._nextDup=null,this._tail=e)}get(e,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,e))return r;return null}remove(e){let n=e._prevDup,r=e._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Wo=class{constructor(){this.map=new Map}put(e){let n=e.trackById,r=this.map.get(n);r||(r=new Wa,this.map.set(n,r)),r.add(e)}get(e,n){let r=e,o=this.map.get(r);return o?o.get(e,n):null}remove(e){let n=e.trackById;return this.map.get(n).remove(e)&&this.map.delete(n),e}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Md(t,e,n){let r=t.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+e+o}var Qa=class{constructor(){}supports(e){return e instanceof Map||$u(e)}create(){return new Ka}},Ka=class{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(e){let n;for(n=this._mapHead;n!==null;n=n._next)e(n)}forEachPreviousItem(e){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)e(n)}forEachChangedItem(e){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)e(n)}forEachAddedItem(e){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)e(n)}forEachRemovedItem(e){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)e(n)}diff(e){if(!e)e=new Map;else if(!(e instanceof Map||$u(e)))throw new g(900,!1);return this.check(e)?this:null}onDestroy(){}check(e){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(e,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(e,n){if(e){let r=e._prev;return n._next=e,n._prev=r,e._prev=n,r&&(r._next=n),e===this._mapHead&&(this._mapHead=n),this._appendAfter=e,e}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(e,n){if(this._records.has(e)){let o=this._records.get(e);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new Ya(e);return this._records.set(e,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let e;for(this._previousMapHead=this._mapHead,e=this._previousMapHead;e!==null;e=e._next)e._nextPrevious=e._next;for(e=this._changesHead;e!==null;e=e._nextChanged)e.previousValue=e.currentValue;for(e=this._additionsHead;e!=null;e=e._nextAdded)e.previousValue=e.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(e,n){Object.is(n,e.currentValue)||(e.previousValue=e.currentValue,e.currentValue=n,this._addToChanges(e))}_addToAdditions(e){this._additionsHead===null?this._additionsHead=this._additionsTail=e:(this._additionsTail._nextAdded=e,this._additionsTail=e)}_addToChanges(e){this._changesHead===null?this._changesHead=this._changesTail=e:(this._changesTail._nextChanged=e,this._changesTail=e)}_forEach(e,n){e instanceof Map?e.forEach(n):Object.keys(e).forEach(r=>n(e[r],r))}},Ya=class{constructor(e){this.key=e,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}};function Td(){return new Yu([new za])}var Yu=(()=>{let e=class e{constructor(r){this.factories=r}static create(r,o){if(o!=null){let i=o.factories.slice();r=r.concat(i)}return new e(r)}static extend(r){return{provide:e,useFactory:o=>e.create(r,o||Td()),deps:[[e,new Bd,new Vd]]}}find(r){let o=this.factories.find(i=>i.supports(r));if(o!=null)return o;throw new g(901,!1)}};e.\u0275prov=H({token:e,providedIn:"root",factory:Td});let t=e;return t})();function xd(){return new Zu([new Qa])}var Zu=(()=>{let e=class e{constructor(r){this.factories=r}static create(r,o){if(o){let i=o.factories.slice();r=r.concat(i)}return new e(r)}static extend(r){return{provide:e,useFactory:o=>e.create(r,o||xd()),deps:[[e,new Bd,new Vd]]}}find(r){let o=this.factories.find(i=>i.supports(r));if(o)return o;throw new g(901,!1)}};e.\u0275prov=H({token:e,providedIn:"root",factory:xd});let t=e;return t})();var cF=Ub(null,"core",[]),dF=(()=>{let e=class e{constructor(r){}};e.\u0275fac=function(o){return new(o||e)(te(Qu))},e.\u0275mod=iu({type:e}),e.\u0275inj=eu({});let t=e;return t})();function Yb(t){return typeof t=="boolean"?t:t!=null&&t!=="false"}function Zb(t,e=NaN){return!isNaN(parseFloat(t))&&!isNaN(Number(t))?Number(t):e}function fF(t,e){an("NgSignals");let n=oc(t);return e?.equal&&(n[Qe].equal=e.equal),n}var Jb=new B("",{providedIn:"root",factory:()=>Q(Xb)}),Xb=(()=>{let e=class e{};e.\u0275prov=H({token:e,providedIn:"root",factory:()=>new Za});let t=e;return t})(),Za=class{constructor(){this.queuedEffectCount=0,this.queues=new Map,this.pendingTasks=Q(Bu),this.taskId=null}scheduleEffect(e){if(this.enqueue(e),this.taskId===null){let n=this.taskId=this.pendingTasks.add();queueMicrotask(()=>{this.flush(),this.pendingTasks.remove(n),this.taskId=null})}}enqueue(e){let n=e.creationZone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(e)||(this.queuedEffectCount++,r.add(e))}flush(){for(;this.queuedEffectCount>0;)for(let[e,n]of this.queues)e===null?this.flushQueue(n):e.run(()=>this.flushQueue(n))}flushQueue(e){for(let n of e)e.delete(n),this.queuedEffectCount--,n.run()}},Ja=class{constructor(e,n,r,o,i,s){this.scheduler=e,this.effectFn=n,this.creationZone=r,this.injector=i,this.watcher=dc(a=>this.runEffect(a),()=>this.schedule(),s),this.unregisterOnDestroy=o?.onDestroy(()=>this.destroy())}runEffect(e){try{this.effectFn(e)}catch(n){this.injector.get(nn,null,{optional:!0})?.handleError(n)}}run(){this.watcher.run()}schedule(){this.scheduler.scheduleEffect(this)}destroy(){this.watcher.destroy(),this.unregisterOnDestroy?.()}};function eI(t,e){an("NgSignals"),!e?.injector&&Cy(eI);let n=e?.injector??Q(Vn),r=e?.manualCleanup!==!0?n.get(Cu):null,o=new Ja(n.get(Jb),t,typeof Zone>"u"?null:Zone.current,r,n,e?.allowSignalWrites??!1),i=n.get(Ku,null,{optional:!0});return!i||!(i._lView[S]&8)?o.watcher.notify():(i._lView[Do]??=[]).push(o.watcher.notify),o}var Ep=null;function Ju(){return Ep}function PF(t){Ep??=t}var dp=class{};var wp=new B(""),bp=(()=>{let e=class e{historyGo(r){throw new Error("")}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:()=>Q(tI),providedIn:"platform"});let t=e;return t})();var tI=(()=>{let e=class e extends bp{constructor(){super(),this._doc=Q(wp),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Ju().getBaseHref(this._doc)}onPopState(r){let o=Ju().getGlobalEventTarget(this._doc,"window");return o.addEventListener("popstate",r,!1),()=>o.removeEventListener("popstate",r)}onHashChange(r){let o=Ju().getGlobalEventTarget(this._doc,"window");return o.addEventListener("hashchange",r,!1),()=>o.removeEventListener("hashchange",r)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(r){this._location.pathname=r}pushState(r,o,i){this._history.pushState(r,o,i)}replaceState(r,o,i){this._history.replaceState(r,o,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(r=0){this._history.go(r)}getState(){return this._history.state}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:()=>new e,providedIn:"platform"});let t=e;return t})();function Ip(t,e){if(t.length==0)return e;if(e.length==0)return t;let n=0;return t.endsWith("/")&&n++,e.startsWith("/")&&n++,n==2?t+e.substring(1):n==1?t+e:t+"/"+e}function fp(t){let e=t.match(/#|\?|$/),n=e&&e.index||t.length,r=n-(t[n-1]==="/"?1:0);return t.slice(0,r)+t.slice(n)}function un(t){return t&&t[0]!=="?"?"?"+t:t}var al=(()=>{let e=class e{historyGo(r){throw new Error("")}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:()=>Q(rI),providedIn:"root"});let t=e;return t})(),nI=new B(""),rI=(()=>{let e=class e extends al{constructor(r,o){super(),this._platformLocation=r,this._removeListenerFns=[],this._baseHref=o??this._platformLocation.getBaseHrefFromDOM()??Q(wp).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(r){this._removeListenerFns.push(this._platformLocation.onPopState(r),this._platformLocation.onHashChange(r))}getBaseHref(){return this._baseHref}prepareExternalUrl(r){return Ip(this._baseHref,r)}path(r=!1){let o=this._platformLocation.pathname+un(this._platformLocation.search),i=this._platformLocation.hash;return i&&r?`${o}${i}`:o}pushState(r,o,i,s){let a=this.prepareExternalUrl(i+un(s));this._platformLocation.pushState(r,o,a)}replaceState(r,o,i,s){let a=this.prepareExternalUrl(i+un(s));this._platformLocation.replaceState(r,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(r=0){this._platformLocation.historyGo?.(r)}};e.\u0275fac=function(o){return new(o||e)(te(bp),te(nI,8))},e.\u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();var oI=(()=>{let e=class e{constructor(r){this._subject=new He,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=r;let o=this._locationStrategy.getBaseHref();this._basePath=aI(fp(hp(o))),this._locationStrategy.onPopState(i=>{this._subject.emit({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(r=!1){return this.normalize(this._locationStrategy.path(r))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(r,o=""){return this.path()==this.normalize(r+un(o))}normalize(r){return e.stripTrailingSlash(sI(this._basePath,hp(r)))}prepareExternalUrl(r){return r&&r[0]!=="/"&&(r="/"+r),this._locationStrategy.prepareExternalUrl(r)}go(r,o="",i=null){this._locationStrategy.pushState(i,"",r,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(r+un(o)),i)}replaceState(r,o="",i=null){this._locationStrategy.replaceState(i,"",r,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(r+un(o)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(r=0){this._locationStrategy.historyGo?.(r)}onUrlChange(r){return this._urlChangeListeners.push(r),this._urlChangeSubscription??=this.subscribe(o=>{this._notifyUrlChangeListeners(o.url,o.state)}),()=>{let o=this._urlChangeListeners.indexOf(r);this._urlChangeListeners.splice(o,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(r="",o){this._urlChangeListeners.forEach(i=>i(r,o))}subscribe(r,o,i){return this._subject.subscribe({next:r,error:o,complete:i})}};e.normalizeQueryParams=un,e.joinWithSlash=Ip,e.stripTrailingSlash=fp,e.\u0275fac=function(o){return new(o||e)(te(al))},e.\u0275prov=H({token:e,factory:()=>iI(),providedIn:"root"});let t=e;return t})();function iI(){return new oI(te(al))}function sI(t,e){if(!t||!e.startsWith(t))return e;let n=e.substring(t.length);return n===""||["/",";","?","#"].includes(n[0])?n:e}function hp(t){return t.replace(/\/index.html$/,"")}function aI(t){if(new RegExp("^(https?:)?//").test(t)){let[,n]=t.split(/\/\/[^\/]+/);return n}return t}var _p=function(t){return t[t.Decimal=0]="Decimal",t[t.Percent=1]="Percent",t[t.Currency=2]="Currency",t[t.Scientific=3]="Scientific",t}(_p||{});var me=function(t){return t[t.Format=0]="Format",t[t.Standalone=1]="Standalone",t}(me||{}),U=function(t){return t[t.Narrow=0]="Narrow",t[t.Abbreviated=1]="Abbreviated",t[t.Wide=2]="Wide",t[t.Short=3]="Short",t}(U||{}),Me=function(t){return t[t.Short=0]="Short",t[t.Medium=1]="Medium",t[t.Long=2]="Long",t[t.Full=3]="Full",t}(Me||{}),Te={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function uI(t){return Se(t)[J.LocaleId]}function lI(t,e,n){let r=Se(t),o=[r[J.DayPeriodsFormat],r[J.DayPeriodsStandalone]],i=Re(o,e);return Re(i,n)}function cI(t,e,n){let r=Se(t),o=[r[J.DaysFormat],r[J.DaysStandalone]],i=Re(o,e);return Re(i,n)}function dI(t,e,n){let r=Se(t),o=[r[J.MonthsFormat],r[J.MonthsStandalone]],i=Re(o,e);return Re(i,n)}function fI(t,e){let r=Se(t)[J.Eras];return Re(r,e)}function gi(t,e){let n=Se(t);return Re(n[J.DateFormat],e)}function yi(t,e){let n=Se(t);return Re(n[J.TimeFormat],e)}function vi(t,e){let r=Se(t)[J.DateTimeFormat];return Re(r,e)}function vt(t,e){let n=Se(t),r=n[J.NumberSymbols][e];if(typeof r>"u"){if(e===Te.CurrencyDecimal)return n[J.NumberSymbols][Te.Decimal];if(e===Te.CurrencyGroup)return n[J.NumberSymbols][Te.Group]}return r}function hI(t,e){return Se(t)[J.NumberFormats][e]}function Cp(t){if(!t[J.ExtraData])throw new Error(`Missing extra locale data for the locale "${t[J.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function pI(t){let e=Se(t);return Cp(e),(e[J.ExtraData][2]||[]).map(r=>typeof r=="string"?Xu(r):[Xu(r[0]),Xu(r[1])])}function mI(t,e,n){let r=Se(t);Cp(r);let o=[r[J.ExtraData][0],r[J.ExtraData][1]],i=Re(o,e)||[];return Re(i,n)||[]}function Re(t,e){for(let n=e;n>-1;n--)if(typeof t[n]<"u")return t[n];throw new Error("Locale data API: locale data undefined")}function Xu(t){let[e,n]=t.split(":");return{hours:+e,minutes:+n}}var gI=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Di={},yI=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,Dt=function(t){return t[t.Short=0]="Short",t[t.ShortGMT=1]="ShortGMT",t[t.Long=2]="Long",t[t.Extended=3]="Extended",t}(Dt||{}),V=function(t){return t[t.FullYear=0]="FullYear",t[t.Month=1]="Month",t[t.Date=2]="Date",t[t.Hours=3]="Hours",t[t.Minutes=4]="Minutes",t[t.Seconds=5]="Seconds",t[t.FractionalSeconds=6]="FractionalSeconds",t[t.Day=7]="Day",t}(V||{}),j=function(t){return t[t.DayPeriods=0]="DayPeriods",t[t.Days=1]="Days",t[t.Months=2]="Months",t[t.Eras=3]="Eras",t}(j||{});function vI(t,e,n,r){let o=MI(t);e=yt(n,e)||e;let s=[],a;for(;e;)if(a=yI.exec(e),a){s=s.concat(a.slice(1));let c=s.pop();if(!c)break;e=c}else{s.push(e);break}let u=o.getTimezoneOffset();r&&(u=Mp(r,u),o=SI(o,r,!0));let l="";return s.forEach(c=>{let d=_I(c);l+=d?d(o,n,u):c==="''"?"'":c.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),l}function _i(t,e,n){let r=new Date(0);return r.setFullYear(t,e,n),r.setHours(0,0,0),r}function yt(t,e){let n=uI(t);if(Di[n]??={},Di[n][e])return Di[n][e];let r="";switch(e){case"shortDate":r=gi(t,Me.Short);break;case"mediumDate":r=gi(t,Me.Medium);break;case"longDate":r=gi(t,Me.Long);break;case"fullDate":r=gi(t,Me.Full);break;case"shortTime":r=yi(t,Me.Short);break;case"mediumTime":r=yi(t,Me.Medium);break;case"longTime":r=yi(t,Me.Long);break;case"fullTime":r=yi(t,Me.Full);break;case"short":let o=yt(t,"shortTime"),i=yt(t,"shortDate");r=Ei(vi(t,Me.Short),[o,i]);break;case"medium":let s=yt(t,"mediumTime"),a=yt(t,"mediumDate");r=Ei(vi(t,Me.Medium),[s,a]);break;case"long":let u=yt(t,"longTime"),l=yt(t,"longDate");r=Ei(vi(t,Me.Long),[u,l]);break;case"full":let c=yt(t,"fullTime"),d=yt(t,"fullDate");r=Ei(vi(t,Me.Full),[c,d]);break}return r&&(Di[n][e]=r),r}function Ei(t,e){return e&&(t=t.replace(/\{([^}]+)}/g,function(n,r){return e!=null&&r in e?e[r]:n})),t}function qe(t,e,n="-",r,o){let i="";(t<0||o&&t<=0)&&(o?t=-t+1:(t=-t,i=n));let s=String(t);for(;s.length<e;)s="0"+s;return r&&(s=s.slice(s.length-e)),i+s}function DI(t,e){return qe(t,3).substring(0,e)}function X(t,e,n=0,r=!1,o=!1){return function(i,s){let a=EI(t,i);if((n>0||a>-n)&&(a+=n),t===V.Hours)a===0&&n===-12&&(a=12);else if(t===V.FractionalSeconds)return DI(a,e);let u=vt(s,Te.MinusSign);return qe(a,e,u,r,o)}}function EI(t,e){switch(t){case V.FullYear:return e.getFullYear();case V.Month:return e.getMonth();case V.Date:return e.getDate();case V.Hours:return e.getHours();case V.Minutes:return e.getMinutes();case V.Seconds:return e.getSeconds();case V.FractionalSeconds:return e.getMilliseconds();case V.Day:return e.getDay();default:throw new Error(`Unknown DateType value "${t}".`)}}function q(t,e,n=me.Format,r=!1){return function(o,i){return wI(o,i,t,e,n,r)}}function wI(t,e,n,r,o,i){switch(n){case j.Months:return dI(e,o,r)[t.getMonth()];case j.Days:return cI(e,o,r)[t.getDay()];case j.DayPeriods:let s=t.getHours(),a=t.getMinutes();if(i){let l=pI(e),c=mI(e,o,r),d=l.findIndex(f=>{if(Array.isArray(f)){let[h,p]=f,m=s>=h.hours&&a>=h.minutes,y=s<p.hours||s===p.hours&&a<p.minutes;if(h.hours<p.hours){if(m&&y)return!0}else if(m||y)return!0}else if(f.hours===s&&f.minutes===a)return!0;return!1});if(d!==-1)return c[d]}return lI(e,o,r)[s<12?0:1];case j.Eras:return fI(e,r)[t.getFullYear()<=0?0:1];default:let u=n;throw new Error(`unexpected translation type ${u}`)}}function wi(t){return function(e,n,r){let o=-1*r,i=vt(n,Te.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(t){case Dt.Short:return(o>=0?"+":"")+qe(s,2,i)+qe(Math.abs(o%60),2,i);case Dt.ShortGMT:return"GMT"+(o>=0?"+":"")+qe(s,1,i);case Dt.Long:return"GMT"+(o>=0?"+":"")+qe(s,2,i)+":"+qe(Math.abs(o%60),2,i);case Dt.Extended:return r===0?"Z":(o>=0?"+":"")+qe(s,2,i)+":"+qe(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${t}"`)}}}var bI=0,Ii=4;function II(t){let e=_i(t,bI,1).getDay();return _i(t,0,1+(e<=Ii?Ii:Ii+7)-e)}function Sp(t){let e=t.getDay(),n=e===0?-3:Ii-e;return _i(t.getFullYear(),t.getMonth(),t.getDate()+n)}function el(t,e=!1){return function(n,r){let o;if(e){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=Sp(n),s=II(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return qe(o,t,vt(r,Te.MinusSign))}}function bi(t,e=!1){return function(n,r){let i=Sp(n).getFullYear();return qe(i,t,vt(r,Te.MinusSign),e)}}var tl={};function _I(t){if(tl[t])return tl[t];let e;switch(t){case"G":case"GG":case"GGG":e=q(j.Eras,U.Abbreviated);break;case"GGGG":e=q(j.Eras,U.Wide);break;case"GGGGG":e=q(j.Eras,U.Narrow);break;case"y":e=X(V.FullYear,1,0,!1,!0);break;case"yy":e=X(V.FullYear,2,0,!0,!0);break;case"yyy":e=X(V.FullYear,3,0,!1,!0);break;case"yyyy":e=X(V.FullYear,4,0,!1,!0);break;case"Y":e=bi(1);break;case"YY":e=bi(2,!0);break;case"YYY":e=bi(3);break;case"YYYY":e=bi(4);break;case"M":case"L":e=X(V.Month,1,1);break;case"MM":case"LL":e=X(V.Month,2,1);break;case"MMM":e=q(j.Months,U.Abbreviated);break;case"MMMM":e=q(j.Months,U.Wide);break;case"MMMMM":e=q(j.Months,U.Narrow);break;case"LLL":e=q(j.Months,U.Abbreviated,me.Standalone);break;case"LLLL":e=q(j.Months,U.Wide,me.Standalone);break;case"LLLLL":e=q(j.Months,U.Narrow,me.Standalone);break;case"w":e=el(1);break;case"ww":e=el(2);break;case"W":e=el(1,!0);break;case"d":e=X(V.Date,1);break;case"dd":e=X(V.Date,2);break;case"c":case"cc":e=X(V.Day,1);break;case"ccc":e=q(j.Days,U.Abbreviated,me.Standalone);break;case"cccc":e=q(j.Days,U.Wide,me.Standalone);break;case"ccccc":e=q(j.Days,U.Narrow,me.Standalone);break;case"cccccc":e=q(j.Days,U.Short,me.Standalone);break;case"E":case"EE":case"EEE":e=q(j.Days,U.Abbreviated);break;case"EEEE":e=q(j.Days,U.Wide);break;case"EEEEE":e=q(j.Days,U.Narrow);break;case"EEEEEE":e=q(j.Days,U.Short);break;case"a":case"aa":case"aaa":e=q(j.DayPeriods,U.Abbreviated);break;case"aaaa":e=q(j.DayPeriods,U.Wide);break;case"aaaaa":e=q(j.DayPeriods,U.Narrow);break;case"b":case"bb":case"bbb":e=q(j.DayPeriods,U.Abbreviated,me.Standalone,!0);break;case"bbbb":e=q(j.DayPeriods,U.Wide,me.Standalone,!0);break;case"bbbbb":e=q(j.DayPeriods,U.Narrow,me.Standalone,!0);break;case"B":case"BB":case"BBB":e=q(j.DayPeriods,U.Abbreviated,me.Format,!0);break;case"BBBB":e=q(j.DayPeriods,U.Wide,me.Format,!0);break;case"BBBBB":e=q(j.DayPeriods,U.Narrow,me.Format,!0);break;case"h":e=X(V.Hours,1,-12);break;case"hh":e=X(V.Hours,2,-12);break;case"H":e=X(V.Hours,1);break;case"HH":e=X(V.Hours,2);break;case"m":e=X(V.Minutes,1);break;case"mm":e=X(V.Minutes,2);break;case"s":e=X(V.Seconds,1);break;case"ss":e=X(V.Seconds,2);break;case"S":e=X(V.FractionalSeconds,1);break;case"SS":e=X(V.FractionalSeconds,2);break;case"SSS":e=X(V.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":e=wi(Dt.Short);break;case"ZZZZZ":e=wi(Dt.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":e=wi(Dt.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":e=wi(Dt.Long);break;default:return null}return tl[t]=e,e}function Mp(t,e){t=t.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+t)/6e4;return isNaN(n)?e:n}function CI(t,e){return t=new Date(t.getTime()),t.setMinutes(t.getMinutes()+e),t}function SI(t,e,n){let r=n?-1:1,o=t.getTimezoneOffset(),i=Mp(e,o);return CI(t,r*(i-o))}function MI(t){if(pp(t))return t;if(typeof t=="number"&&!isNaN(t))return new Date(t);if(typeof t=="string"){if(t=t.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(t)){let[o,i=1,s=1]=t.split("-").map(a=>+a);return _i(o,i-1,s)}let n=parseFloat(t);if(!isNaN(t-n))return new Date(n);let r;if(r=t.match(gI))return TI(r)}let e=new Date(t);if(!pp(e))throw new Error(`Unable to convert "${t}" into a date`);return e}function TI(t){let e=new Date(0),n=0,r=0,o=t[8]?e.setUTCFullYear:e.setFullYear,i=t[8]?e.setUTCHours:e.setHours;t[9]&&(n=Number(t[9]+t[10]),r=Number(t[9]+t[11])),o.call(e,Number(t[1]),Number(t[2])-1,Number(t[3]));let s=Number(t[4]||0)-n,a=Number(t[5]||0)-r,u=Number(t[6]||0),l=Math.floor(parseFloat("0."+(t[7]||0))*1e3);return i.call(e,s,a,u,l),e}function pp(t){return t instanceof Date&&!isNaN(t.valueOf())}var xI=/^(\d+)?\.((\d+)(-(\d+))?)?$/,mp=22,Ci=".",Ir="0",NI=";",AI=",",nl="#";function OI(t,e,n,r,o,i,s=!1){let a="",u=!1;if(!isFinite(t))a=vt(n,Te.Infinity);else{let l=kI(t);s&&(l=RI(l));let c=e.minInt,d=e.minFrac,f=e.maxFrac;if(i){let T=i.match(xI);if(T===null)throw new Error(`${i} is not a valid digit info`);let R=T[1],L=T[3],le=T[5];R!=null&&(c=rl(R)),L!=null&&(d=rl(L)),le!=null?f=rl(le):L!=null&&d>f&&(f=d)}LI(l,d,f);let h=l.digits,p=l.integerLen,m=l.exponent,y=[];for(u=h.every(T=>!T);p<c;p++)h.unshift(0);for(;p<0;p++)h.unshift(0);p>0?y=h.splice(p,h.length):(y=h,h=[0]);let D=[];for(h.length>=e.lgSize&&D.unshift(h.splice(-e.lgSize,h.length).join(""));h.length>e.gSize;)D.unshift(h.splice(-e.gSize,h.length).join(""));h.length&&D.unshift(h.join("")),a=D.join(vt(n,r)),y.length&&(a+=vt(n,o)+y.join("")),m&&(a+=vt(n,Te.Exponential)+"+"+m)}return t<0&&!u?a=e.negPre+a+e.negSuf:a=e.posPre+a+e.posSuf,a}function PI(t,e,n){let r=hI(e,_p.Decimal),o=FI(r,vt(e,Te.MinusSign));return OI(t,o,e,Te.Group,Te.Decimal,n)}function FI(t,e="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=t.split(NI),o=r[0],i=r[1],s=o.indexOf(Ci)!==-1?o.split(Ci):[o.substring(0,o.lastIndexOf(Ir)+1),o.substring(o.lastIndexOf(Ir)+1)],a=s[0],u=s[1]||"";n.posPre=a.substring(0,a.indexOf(nl));for(let c=0;c<u.length;c++){let d=u.charAt(c);d===Ir?n.minFrac=n.maxFrac=c+1:d===nl?n.maxFrac=c+1:n.posSuf+=d}let l=a.split(AI);if(n.gSize=l[1]?l[1].length:0,n.lgSize=l[2]||l[1]?(l[2]||l[1]).length:0,i){let c=o.length-n.posPre.length-n.posSuf.length,d=i.indexOf(nl);n.negPre=i.substring(0,d).replace(/'/g,""),n.negSuf=i.slice(d+c).replace(/'/g,"")}else n.negPre=e+n.posPre,n.negSuf=n.posSuf;return n}function RI(t){if(t.digits[0]===0)return t;let e=t.digits.length-t.integerLen;return t.exponent?t.exponent+=2:(e===0?t.digits.push(0,0):e===1&&t.digits.push(0),t.integerLen+=2),t}function kI(t){let e=Math.abs(t)+"",n=0,r,o,i,s,a;for((o=e.indexOf(Ci))>-1&&(e=e.replace(Ci,"")),(i=e.search(/e/i))>0?(o<0&&(o=i),o+=+e.slice(i+1),e=e.substring(0,i)):o<0&&(o=e.length),i=0;e.charAt(i)===Ir;i++);if(i===(a=e.length))r=[0],o=1;else{for(a--;e.charAt(a)===Ir;)a--;for(o-=i,r=[],s=0;i<=a;i++,s++)r[s]=Number(e.charAt(i))}return o>mp&&(r=r.splice(0,mp-1),n=o-1,o=1),{digits:r,exponent:n,integerLen:o}}function LI(t,e,n){if(e>n)throw new Error(`The minimum number of digits after fraction (${e}) is higher than the maximum (${n}).`);let r=t.digits,o=r.length-t.integerLen,i=Math.min(Math.max(e,o),n),s=i+t.integerLen,a=r[s];if(s>0){r.splice(Math.max(t.integerLen,s));for(let d=s;d<r.length;d++)r[d]=0}else{o=Math.max(0,o),t.integerLen=1,r.length=Math.max(1,s=i+1),r[0]=0;for(let d=1;d<s;d++)r[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)r.unshift(0),t.integerLen++;r.unshift(1),t.integerLen++}else r[s-1]++;for(;o<Math.max(0,i);o++)r.push(0);let u=i!==0,l=e+t.integerLen,c=r.reduceRight(function(d,f,h,p){return f=f+d,p[h]=f<10?f:f-10,u&&(p[h]===0&&h>=l?p.pop():u=!1),f>=10?1:0},0);c&&(r.unshift(c),t.integerLen++)}function rl(t){let e=parseInt(t);if(isNaN(e))throw new Error("Invalid integer literal when parsing "+t);return e}function FF(t,e){e=encodeURIComponent(e);for(let n of t.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===e)return decodeURIComponent(i)}return null}var ol=/\s+/,gp=[],RF=(()=>{let e=class e{constructor(r,o){this._ngEl=r,this._renderer=o,this.initialClasses=gp,this.stateMap=new Map}set klass(r){this.initialClasses=r!=null?r.trim().split(ol):gp}set ngClass(r){this.rawClass=typeof r=="string"?r.trim().split(ol):r}ngDoCheck(){for(let o of this.initialClasses)this._updateState(o,!0);let r=this.rawClass;if(Array.isArray(r)||r instanceof Set)for(let o of r)this._updateState(o,!0);else if(r!=null)for(let o of Object.keys(r))this._updateState(o,!!r[o]);this._applyStateDiff()}_updateState(r,o){let i=this.stateMap.get(r);i!==void 0?(i.enabled!==o&&(i.changed=!0,i.enabled=o),i.touched=!0):this.stateMap.set(r,{enabled:o,changed:!0,touched:!0})}_applyStateDiff(){for(let r of this.stateMap){let o=r[0],i=r[1];i.changed?(this._toggleClass(o,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(o,!1),this.stateMap.delete(o)),i.touched=!1}}_toggleClass(r,o){r=r.trim(),r.length>0&&r.split(ol).forEach(i=>{o?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}};e.\u0275fac=function(o){return new(o||e)(z(kt),z(pi))},e.\u0275dir=ft({type:e,selectors:[["","ngClass",""]],inputs:{klass:[Nt.None,"class","klass"],ngClass:"ngClass"},standalone:!0});let t=e;return t})();var il=class{constructor(e,n,r,o){this.$implicit=e,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},kF=(()=>{let e=class e{set ngForOf(r){this._ngForOf=r,this._ngForOfDirty=!0}set ngForTrackBy(r){this._trackByFn=r}get ngForTrackBy(){return this._trackByFn}constructor(r,o,i){this._viewContainer=r,this._template=o,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(r){r&&(this._template=r)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let r=this._ngForOf;if(!this._differ&&r)if(0)try{}catch{}else this._differ=this._differs.find(r).create(this.ngForTrackBy)}if(this._differ){let r=this._differ.diff(this._ngForOf);r&&this._applyChanges(r)}}_applyChanges(r){let o=this._viewContainer;r.forEachOperation((i,s,a)=>{if(i.previousIndex==null)o.createEmbeddedView(this._template,new il(i.item,this._ngForOf,-1,-1),a===null?void 0:a);else if(a==null)o.remove(s===null?void 0:s);else if(s!==null){let u=o.get(s);o.move(u,a),yp(u,i)}});for(let i=0,s=o.length;i<s;i++){let u=o.get(i).context;u.index=i,u.count=s,u.ngForOf=this._ngForOf}r.forEachIdentityChange(i=>{let s=o.get(i.currentIndex);yp(s,i)})}static ngTemplateContextGuard(r,o){return!0}};e.\u0275fac=function(o){return new(o||e)(z(gt),z(dt),z(Yu))},e.\u0275dir=ft({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0});let t=e;return t})();function yp(t,e){t.context.$implicit=e.item}var LF=(()=>{let e=class e{constructor(r,o){this._viewContainer=r,this._context=new sl,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=o}set ngIf(r){this._context.$implicit=this._context.ngIf=r,this._updateView()}set ngIfThen(r){vp("ngIfThen",r),this._thenTemplateRef=r,this._thenViewRef=null,this._updateView()}set ngIfElse(r){vp("ngIfElse",r),this._elseTemplateRef=r,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(r,o){return!0}};e.\u0275fac=function(o){return new(o||e)(z(gt),z(dt))},e.\u0275dir=ft({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0});let t=e;return t})(),sl=class{constructor(){this.$implicit=null,this.ngIf=null}};function vp(t,e){if(!!!(!e||e.createEmbeddedView))throw new Error(`${t} must be a TemplateRef, but received '${pe(e)}'.`)}var jI=!0,Si=class{constructor(e,n){this._viewContainerRef=e,this._templateRef=n,this._created=!1}create(){this._created=!0,this._viewContainerRef.createEmbeddedView(this._templateRef)}destroy(){this._created=!1,this._viewContainerRef.clear()}enforceState(e){e&&!this._created?this.create():!e&&this._created&&this.destroy()}},Tp=(()=>{let e=class e{constructor(){this._defaultViews=[],this._defaultUsed=!1,this._caseCount=0,this._lastCaseCheckIndex=0,this._lastCasesMatched=!1}set ngSwitch(r){this._ngSwitch=r,this._caseCount===0&&this._updateDefaultCases(!0)}_addCase(){return this._caseCount++}_addDefault(r){this._defaultViews.push(r)}_matchCase(r){let o=jI?r===this._ngSwitch:r==this._ngSwitch;return this._lastCasesMatched||=o,this._lastCaseCheckIndex++,this._lastCaseCheckIndex===this._caseCount&&(this._updateDefaultCases(!this._lastCasesMatched),this._lastCaseCheckIndex=0,this._lastCasesMatched=!1),o}_updateDefaultCases(r){if(this._defaultViews.length>0&&r!==this._defaultUsed){this._defaultUsed=r;for(let o of this._defaultViews)o.enforceState(r)}}};e.\u0275fac=function(o){return new(o||e)},e.\u0275dir=ft({type:e,selectors:[["","ngSwitch",""]],inputs:{ngSwitch:"ngSwitch"},standalone:!0});let t=e;return t})(),jF=(()=>{let e=class e{constructor(r,o,i){this.ngSwitch=i,i._addCase(),this._view=new Si(r,o)}ngDoCheck(){this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase))}};e.\u0275fac=function(o){return new(o||e)(z(gt),z(dt),z(Tp,9))},e.\u0275dir=ft({type:e,selectors:[["","ngSwitchCase",""]],inputs:{ngSwitchCase:"ngSwitchCase"},standalone:!0});let t=e;return t})(),VF=(()=>{let e=class e{constructor(r,o,i){i._addDefault(new Si(r,o))}};e.\u0275fac=function(o){return new(o||e)(z(gt),z(dt),z(Tp,9))},e.\u0275dir=ft({type:e,selectors:[["","ngSwitchDefault",""]],standalone:!0});let t=e;return t})();var BF=(()=>{let e=class e{constructor(r,o,i){this._ngEl=r,this._differs=o,this._renderer=i,this._ngStyle=null,this._differ=null}set ngStyle(r){this._ngStyle=r,!this._differ&&r&&(this._differ=this._differs.find(r).create())}ngDoCheck(){if(this._differ){let r=this._differ.diff(this._ngStyle);r&&this._applyChanges(r)}}_setStyle(r,o){let[i,s]=r.split("."),a=i.indexOf("-")===-1?void 0:dr.DashCase;o!=null?this._renderer.setStyle(this._ngEl.nativeElement,i,s?`${o}${s}`:o,a):this._renderer.removeStyle(this._ngEl.nativeElement,i,a)}_applyChanges(r){r.forEachRemovedItem(o=>this._setStyle(o.key,null)),r.forEachAddedItem(o=>this._setStyle(o.key,o.currentValue)),r.forEachChangedItem(o=>this._setStyle(o.key,o.currentValue))}};e.\u0275fac=function(o){return new(o||e)(z(kt),z(Zu),z(pi))},e.\u0275dir=ft({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"},standalone:!0});let t=e;return t})(),$F=(()=>{let e=class e{constructor(r){this._viewContainerRef=r,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(r){if(this._shouldRecreateView(r)){let o=this._viewContainerRef;if(this._viewRef&&o.remove(o.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let i=this._createContextForwardProxy();this._viewRef=o.createEmbeddedView(this.ngTemplateOutlet,i,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(r){return!!r.ngTemplateOutlet||!!r.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(r,o,i)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,o,i):!1,get:(r,o,i)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,o,i)}})}};e.\u0275fac=function(o){return new(o||e)(z(gt))},e.\u0275dir=ft({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[fu]});let t=e;return t})();function xp(t,e){return new g(2100,!1)}var VI="mediumDate",BI=new B(""),$I=new B(""),HF=(()=>{let e=class e{constructor(r,o,i){this.locale=r,this.defaultTimezone=o,this.defaultOptions=i}transform(r,o,i,s){if(r==null||r===""||r!==r)return null;try{let a=o??this.defaultOptions?.dateFormat??VI,u=i??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return vI(r,a,s||this.locale,u)}catch(a){throw xp(e,a.message)}}};e.\u0275fac=function(o){return new(o||e)(z(br,16),z(BI,24),z($I,24))},e.\u0275pipe=su({name:"date",type:e,pure:!0,standalone:!0});let t=e;return t})();var UF=(()=>{let e=class e{constructor(r){this._locale=r}transform(r,o,i){if(!HI(r))return null;i||=this._locale;try{let s=UI(r);return PI(s,i,o)}catch(s){throw xp(e,s.message)}}};e.\u0275fac=function(o){return new(o||e)(z(br,16))},e.\u0275pipe=su({name:"number",type:e,pure:!0,standalone:!0});let t=e;return t})();function HI(t){return!(t==null||t===""||t!==t)}function UI(t){if(typeof t=="string"&&!isNaN(Number(t)-parseFloat(t)))return Number(t);if(typeof t!="number")throw new Error(`${t} is not a number`);return t}var zF=(()=>{let e=class e{};e.\u0275fac=function(o){return new(o||e)},e.\u0275mod=iu({type:e}),e.\u0275inj=eu({});let t=e;return t})(),zI="browser",qI="server";function qF(t){return t===zI}function GF(t){return t===qI}var Dp=class{};var M=function(t){return t[t.State=0]="State",t[t.Transition=1]="Transition",t[t.Sequence=2]="Sequence",t[t.Group=3]="Group",t[t.Animate=4]="Animate",t[t.Keyframes=5]="Keyframes",t[t.Style=6]="Style",t[t.Trigger=7]="Trigger",t[t.Reference=8]="Reference",t[t.AnimateChild=9]="AnimateChild",t[t.AnimateRef=10]="AnimateRef",t[t.Query=11]="Query",t[t.Stagger=12]="Stagger",t}(M||{}),tt="*";function KF(t,e){return{type:M.Trigger,name:t,definitions:e,options:{}}}function YF(t,e=null){return{type:M.Animate,styles:e,timings:t}}function ZF(t,e=null){return{type:M.Group,steps:t,options:e}}function Np(t,e=null){return{type:M.Sequence,steps:t,options:e}}function ul(t){return{type:M.Style,styles:t,offset:null}}function JF(t,e,n){return{type:M.State,name:t,styles:e,options:n}}function XF(t){return{type:M.Keyframes,steps:t}}function eR(t,e,n=null){return{type:M.Transition,expr:t,animation:e,options:n}}function tR(t=null){return{type:M.AnimateChild,options:t}}function nR(t,e,n=null){return{type:M.Query,selector:t,animation:e,options:n}}var Lt=class{constructor(e=0,n=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._originalOnDoneFns=[],this._originalOnStartFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=e+n}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(e=>e()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(e){this._position=this.totalTime?e*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(e){let n=e=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},_r=class{constructor(e){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=e;let n=0,r=0,o=0,i=this.players.length;i==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(s=>{s.onDone(()=>{++n==i&&this._onFinish()}),s.onDestroy(()=>{++r==i&&this._onDestroy()}),s.onStart(()=>{++o==i&&this._onStart()})}),this.totalTime=this.players.reduce((s,a)=>Math.max(s,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this.players.forEach(e=>e.init())}onStart(e){this._onStartFns.push(e)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(e=>e()),this._onStartFns=[])}onDone(e){this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(e=>e.play())}pause(){this.players.forEach(e=>e.pause())}restart(){this.players.forEach(e=>e.restart())}finish(){this._onFinish(),this.players.forEach(e=>e.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(e=>e.destroy()),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this.players.forEach(e=>e.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(e){let n=e*this.totalTime;this.players.forEach(r=>{let o=r.totalTime?Math.min(1,n/r.totalTime):1;r.setPosition(o)})}getPosition(){let e=this.players.reduce((n,r)=>n===null||r.totalTime>n.totalTime?r:n,null);return e!=null?e.getPosition():0}beforeDestroy(){this.players.forEach(e=>{e.beforeDestroy&&e.beforeDestroy()})}triggerCallback(e){let n=e=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},Mi="!";function Ap(t){return new g(3e3,!1)}function GI(){return new g(3100,!1)}function WI(){return new g(3101,!1)}function QI(t){return new g(3001,!1)}function KI(t){return new g(3003,!1)}function YI(t){return new g(3004,!1)}function ZI(t,e){return new g(3005,!1)}function JI(){return new g(3006,!1)}function XI(){return new g(3007,!1)}function e_(t,e){return new g(3008,!1)}function t_(t){return new g(3002,!1)}function n_(t,e,n,r,o){return new g(3010,!1)}function r_(){return new g(3011,!1)}function o_(){return new g(3012,!1)}function i_(){return new g(3200,!1)}function s_(){return new g(3202,!1)}function a_(){return new g(3013,!1)}function u_(t){return new g(3014,!1)}function l_(t){return new g(3015,!1)}function c_(t){return new g(3016,!1)}function d_(t){return new g(3500,!1)}function f_(t){return new g(3501,!1)}function h_(t,e){return new g(3404,!1)}function p_(t){return new g(3502,!1)}function m_(t){return new g(3503,!1)}function g_(){return new g(3300,!1)}function y_(t){return new g(3504,!1)}function v_(t){return new g(3301,!1)}function D_(t,e){return new g(3302,!1)}function E_(t){return new g(3303,!1)}function w_(t,e){return new g(3400,!1)}function b_(t){return new g(3401,!1)}function I_(t){return new g(3402,!1)}function __(t,e){return new g(3505,!1)}var C_=new Set(["-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-ms-grid-columns","-ms-grid-rows","-webkit-line-clamp","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color","accent-color","all","backdrop-filter","background","background-color","background-position","background-size","block-size","border","border-block-end","border-block-end-color","border-block-end-width","border-block-start","border-block-start-color","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-width","border-color","border-end-end-radius","border-end-start-radius","border-image-outset","border-image-slice","border-image-width","border-inline-end","border-inline-end-color","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-width","border-left","border-left-color","border-left-width","border-radius","border-right","border-right-color","border-right-width","border-start-end-radius","border-start-start-radius","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-width","border-width","bottom","box-shadow","caret-color","clip","clip-path","color","column-count","column-gap","column-rule","column-rule-color","column-rule-width","column-width","columns","filter","flex","flex-basis","flex-grow","flex-shrink","font","font-size","font-size-adjust","font-stretch","font-variation-settings","font-weight","gap","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","inline-size","input-security","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","left","letter-spacing","line-clamp","line-height","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","mask","mask-border","mask-position","mask-size","max-block-size","max-height","max-inline-size","max-lines","max-width","min-block-size","min-height","min-inline-size","min-width","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","outline","outline-color","outline-offset","outline-width","padding","padding-block-end","padding-block-start","padding-bottom","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","perspective","perspective-origin","right","rotate","row-gap","scale","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-coordinate","scroll-snap-destination","scrollbar-color","shape-image-threshold","shape-margin","shape-outside","tab-size","text-decoration","text-decoration-color","text-decoration-thickness","text-emphasis","text-emphasis-color","text-indent","text-shadow","text-underline-offset","top","transform","transform-origin","translate","vertical-align","visibility","width","word-spacing","z-index","zoom"]);function jt(t){switch(t.length){case 0:return new Lt;case 1:return t[0];default:return new _r(t)}}function Kp(t,e,n=new Map,r=new Map){let o=[],i=[],s=-1,a=null;if(e.forEach(u=>{let l=u.get("offset"),c=l==s,d=c&&a||new Map;u.forEach((f,h)=>{let p=h,m=f;if(h!=="offset")switch(p=t.normalizePropertyName(p,o),m){case Mi:m=n.get(h);break;case tt:m=r.get(h);break;default:m=t.normalizeStyleValue(h,p,m,o);break}d.set(p,m)}),c||i.push(d),a=d,s=l}),o.length)throw p_(o);return i}function Fl(t,e,n,r){switch(e){case"start":t.onStart(()=>r(n&&ll(n,"start",t)));break;case"done":t.onDone(()=>r(n&&ll(n,"done",t)));break;case"destroy":t.onDestroy(()=>r(n&&ll(n,"destroy",t)));break}}function ll(t,e,n){let r=n.totalTime,o=!!n.disabled,i=Rl(t.element,t.triggerName,t.fromState,t.toState,e||t.phaseName,r??t.totalTime,o),s=t._data;return s!=null&&(i._data=s),i}function Rl(t,e,n,r,o="",i=0,s){return{element:t,triggerName:e,fromState:n,toState:r,phaseName:o,totalTime:i,disabled:!!s}}function Ne(t,e,n){let r=t.get(e);return r||t.set(e,r=n),r}function Op(t){let e=t.indexOf(":"),n=t.substring(1,e),r=t.slice(e+1);return[n,r]}var S_=typeof document>"u"?null:document.documentElement;function kl(t){let e=t.parentNode||t.host||null;return e===S_?null:e}function M_(t){return t.substring(1,6)=="ebkit"}var ln=null,Pp=!1;function T_(t){ln||(ln=x_()||{},Pp=ln.style?"WebkitAppearance"in ln.style:!1);let e=!0;return ln.style&&!M_(t)&&(e=t in ln.style,!e&&Pp&&(e="Webkit"+t.charAt(0).toUpperCase()+t.slice(1)in ln.style)),e}function aR(t){return C_.has(t)}function x_(){return typeof document<"u"?document.body:null}function Yp(t,e){for(;e;){if(e===t)return!0;e=kl(e)}return!1}function Zp(t,e,n){if(n)return Array.from(t.querySelectorAll(e));let r=t.querySelector(e);return r?[r]:[]}var Jp=(()=>{let e=class e{validateStyleProperty(r){return T_(r)}matchesElement(r,o){return!1}containsElement(r,o){return Yp(r,o)}getParentElement(r){return kl(r)}query(r,o,i){return Zp(r,o,i)}computeStyle(r,o,i){return i||""}animate(r,o,i,s,a,u=[],l){return new Lt(i,s)}};e.\u0275fac=function(o){return new(o||e)},e.\u0275prov=H({token:e,factory:e.\u0275fac});let t=e;return t})(),Ul=class Ul{};Ul.NOOP=new Jp;var Fp=Ul,ml=class{},gl=class{normalizePropertyName(e,n){return e}normalizeStyleValue(e,n,r,o){return r}},N_=1e3,Xp="{{",A_="}}",Ll="ng-enter",Pi="ng-leave",Ti="ng-trigger",Fi=".ng-trigger",Rp="ng-animating",yl=".ng-animating";function Et(t){if(typeof t=="number")return t;let e=t.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:vl(parseFloat(e[1]),e[2])}function vl(t,e){switch(e){case"s":return t*N_;default:return t}}function Ri(t,e,n){return t.hasOwnProperty("duration")?t:O_(t,e,n)}function O_(t,e,n){let r=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,o,i=0,s="";if(typeof t=="string"){let a=t.match(r);if(a===null)return e.push(Ap(t)),{duration:0,delay:0,easing:""};o=vl(parseFloat(a[1]),a[2]);let u=a[3];u!=null&&(i=vl(parseFloat(u),a[4]));let l=a[5];l&&(s=l)}else o=t;if(!n){let a=!1,u=e.length;o<0&&(e.push(GI()),a=!0),i<0&&(e.push(WI()),a=!0),a&&e.splice(u,0,Ap(t))}return{duration:o,delay:i,easing:s}}function P_(t){return t.length?t[0]instanceof Map?t:t.map(e=>new Map(Object.entries(e))):[]}function kp(t){return Array.isArray(t)?new Map(...t):new Map(t)}function nt(t,e,n){e.forEach((r,o)=>{let i=jl(o);n&&!n.has(o)&&n.set(o,t.style[i]),t.style[i]=r})}function dn(t,e){e.forEach((n,r)=>{let o=jl(r);t.style[o]=""})}function Cr(t){return Array.isArray(t)?t.length==1?t[0]:Np(t):t}function F_(t,e,n){let r=e.params||{},o=em(t);o.length&&o.forEach(i=>{r.hasOwnProperty(i)||n.push(QI(i))})}var Dl=new RegExp(`${Xp}\\s*(.+?)\\s*${A_}`,"g");function em(t){let e=[];if(typeof t=="string"){let n;for(;n=Dl.exec(t);)e.push(n[1]);Dl.lastIndex=0}return e}function Mr(t,e,n){let r=`${t}`,o=r.replace(Dl,(i,s)=>{let a=e[s];return a==null&&(n.push(KI(s)),a=""),a.toString()});return o==r?t:o}var R_=/-+([a-z0-9])/g;function jl(t){return t.replace(R_,(...e)=>e[1].toUpperCase())}function uR(t){return t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function k_(t,e){return t===0||e===0}function L_(t,e,n){if(n.size&&e.length){let r=e[0],o=[];if(n.forEach((i,s)=>{r.has(s)||o.push(s),r.set(s,i)}),o.length)for(let i=1;i<e.length;i++){let s=e[i];o.forEach(a=>s.set(a,Vl(t,a)))}}return e}function xe(t,e,n){switch(e.type){case M.Trigger:return t.visitTrigger(e,n);case M.State:return t.visitState(e,n);case M.Transition:return t.visitTransition(e,n);case M.Sequence:return t.visitSequence(e,n);case M.Group:return t.visitGroup(e,n);case M.Animate:return t.visitAnimate(e,n);case M.Keyframes:return t.visitKeyframes(e,n);case M.Style:return t.visitStyle(e,n);case M.Reference:return t.visitReference(e,n);case M.AnimateChild:return t.visitAnimateChild(e,n);case M.AnimateRef:return t.visitAnimateRef(e,n);case M.Query:return t.visitQuery(e,n);case M.Stagger:return t.visitStagger(e,n);default:throw YI(e.type)}}function Vl(t,e){return window.getComputedStyle(t)[e]}var j_=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),El=class extends ml{normalizePropertyName(e,n){return jl(e)}normalizeStyleValue(e,n,r,o){let i="",s=r.toString().trim();if(j_.has(n)&&r!==0&&r!=="0")if(typeof r=="number")i="px";else{let a=r.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&o.push(ZI(e,r))}return s+i}};var ki="*";function V_(t,e){let n=[];return typeof t=="string"?t.split(/\s*,\s*/).forEach(r=>B_(r,n,e)):n.push(t),n}function B_(t,e,n){if(t[0]==":"){let u=$_(t,n);if(typeof u=="function"){e.push(u);return}t=u}let r=t.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(r==null||r.length<4)return n.push(l_(t)),e;let o=r[1],i=r[2],s=r[3];e.push(Lp(o,s));let a=o==ki&&s==ki;i[0]=="<"&&!a&&e.push(Lp(s,o))}function $_(t,e){switch(t){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(n,r)=>parseFloat(r)>parseFloat(n);case":decrement":return(n,r)=>parseFloat(r)<parseFloat(n);default:return e.push(c_(t)),"* => *"}}var xi=new Set(["true","1"]),Ni=new Set(["false","0"]);function Lp(t,e){let n=xi.has(t)||Ni.has(t),r=xi.has(e)||Ni.has(e);return(o,i)=>{let s=t==ki||t==o,a=e==ki||e==i;return!s&&n&&typeof o=="boolean"&&(s=o?xi.has(t):Ni.has(t)),!a&&r&&typeof i=="boolean"&&(a=i?xi.has(e):Ni.has(e)),s&&a}}var tm=":self",H_=new RegExp(`s*${tm}s*,?`,"g");function Bl(t,e,n,r){return new wl(t).build(e,n,r)}var jp="",wl=class{constructor(e){this._driver=e}build(e,n,r){let o=new bl(n);return this._resetContextStyleTimingState(o),xe(this,Cr(e),o)}_resetContextStyleTimingState(e){e.currentQuerySelector=jp,e.collectedStyles=new Map,e.collectedStyles.set(jp,new Map),e.currentTime=0}visitTrigger(e,n){let r=n.queryCount=0,o=n.depCount=0,i=[],s=[];return e.name.charAt(0)=="@"&&n.errors.push(JI()),e.definitions.forEach(a=>{if(this._resetContextStyleTimingState(n),a.type==M.State){let u=a,l=u.name;l.toString().split(/\s*,\s*/).forEach(c=>{u.name=c,i.push(this.visitState(u,n))}),u.name=l}else if(a.type==M.Transition){let u=this.visitTransition(a,n);r+=u.queryCount,o+=u.depCount,s.push(u)}else n.errors.push(XI())}),{type:M.Trigger,name:e.name,states:i,transitions:s,queryCount:r,depCount:o,options:null}}visitState(e,n){let r=this.visitStyle(e.styles,n),o=e.options&&e.options.params||null;if(r.containsDynamicStyles){let i=new Set,s=o||{};r.styles.forEach(a=>{a instanceof Map&&a.forEach(u=>{em(u).forEach(l=>{s.hasOwnProperty(l)||i.add(l)})})}),i.size&&n.errors.push(e_(e.name,[...i.values()]))}return{type:M.State,name:e.name,style:r,options:o?{params:o}:null}}visitTransition(e,n){n.queryCount=0,n.depCount=0;let r=xe(this,Cr(e.animation),n),o=V_(e.expr,n.errors);return{type:M.Transition,matchers:o,animation:r,queryCount:n.queryCount,depCount:n.depCount,options:cn(e.options)}}visitSequence(e,n){return{type:M.Sequence,steps:e.steps.map(r=>xe(this,r,n)),options:cn(e.options)}}visitGroup(e,n){let r=n.currentTime,o=0,i=e.steps.map(s=>{n.currentTime=r;let a=xe(this,s,n);return o=Math.max(o,n.currentTime),a});return n.currentTime=o,{type:M.Group,steps:i,options:cn(e.options)}}visitAnimate(e,n){let r=G_(e.timings,n.errors);n.currentAnimateTimings=r;let o,i=e.styles?e.styles:ul({});if(i.type==M.Keyframes)o=this.visitKeyframes(i,n);else{let s=e.styles,a=!1;if(!s){a=!0;let l={};r.easing&&(l.easing=r.easing),s=ul(l)}n.currentTime+=r.duration+r.delay;let u=this.visitStyle(s,n);u.isEmptyStep=a,o=u}return n.currentAnimateTimings=null,{type:M.Animate,timings:r,style:o,options:null}}visitStyle(e,n){let r=this._makeStyleAst(e,n);return this._validateStyleAst(r,n),r}_makeStyleAst(e,n){let r=[],o=Array.isArray(e.styles)?e.styles:[e.styles];for(let a of o)typeof a=="string"?a===tt?r.push(a):n.errors.push(t_(a)):r.push(new Map(Object.entries(a)));let i=!1,s=null;return r.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(s=a.get("easing"),a.delete("easing")),!i)){for(let u of a.values())if(u.toString().indexOf(Xp)>=0){i=!0;break}}}),{type:M.Style,styles:r,easing:s,offset:e.offset,containsDynamicStyles:i,options:null}}_validateStyleAst(e,n){let r=n.currentAnimateTimings,o=n.currentTime,i=n.currentTime;r&&i>0&&(i-=r.duration+r.delay),e.styles.forEach(s=>{typeof s!="string"&&s.forEach((a,u)=>{let l=n.collectedStyles.get(n.currentQuerySelector),c=l.get(u),d=!0;c&&(i!=o&&i>=c.startTime&&o<=c.endTime&&(n.errors.push(n_(u,c.startTime,c.endTime,i,o)),d=!1),i=c.startTime),d&&l.set(u,{startTime:i,endTime:o}),n.options&&F_(a,n.options,n.errors)})})}visitKeyframes(e,n){let r={type:M.Keyframes,styles:[],options:null};if(!n.currentAnimateTimings)return n.errors.push(r_()),r;let o=1,i=0,s=[],a=!1,u=!1,l=0,c=e.steps.map(D=>{let T=this._makeStyleAst(D,n),R=T.offset!=null?T.offset:q_(T.styles),L=0;return R!=null&&(i++,L=T.offset=R),u=u||L<0||L>1,a=a||L<l,l=L,s.push(L),T});u&&n.errors.push(o_()),a&&n.errors.push(i_());let d=e.steps.length,f=0;i>0&&i<d?n.errors.push(s_()):i==0&&(f=o/(d-1));let h=d-1,p=n.currentTime,m=n.currentAnimateTimings,y=m.duration;return c.forEach((D,T)=>{let R=f>0?T==h?1:f*T:s[T],L=R*y;n.currentTime=p+m.delay+L,m.duration=L,this._validateStyleAst(D,n),D.offset=R,r.styles.push(D)}),r}visitReference(e,n){return{type:M.Reference,animation:xe(this,Cr(e.animation),n),options:cn(e.options)}}visitAnimateChild(e,n){return n.depCount++,{type:M.AnimateChild,options:cn(e.options)}}visitAnimateRef(e,n){return{type:M.AnimateRef,animation:this.visitReference(e.animation,n),options:cn(e.options)}}visitQuery(e,n){let r=n.currentQuerySelector,o=e.options||{};n.queryCount++,n.currentQuery=e;let[i,s]=U_(e.selector);n.currentQuerySelector=r.length?r+" "+i:i,Ne(n.collectedStyles,n.currentQuerySelector,new Map);let a=xe(this,Cr(e.animation),n);return n.currentQuery=null,n.currentQuerySelector=r,{type:M.Query,selector:i,limit:o.limit||0,optional:!!o.optional,includeSelf:s,animation:a,originalSelector:e.selector,options:cn(e.options)}}visitStagger(e,n){n.currentQuery||n.errors.push(a_());let r=e.timings==="full"?{duration:0,delay:0,easing:"full"}:Ri(e.timings,n.errors,!0);return{type:M.Stagger,animation:xe(this,Cr(e.animation),n),timings:r,options:null}}};function U_(t){let e=!!t.split(/\s*,\s*/).find(n=>n==tm);return e&&(t=t.replace(H_,"")),t=t.replace(/@\*/g,Fi).replace(/@\w+/g,n=>Fi+"-"+n.slice(1)).replace(/:animating/g,yl),[t,e]}function z_(t){return t?ge({},t):null}var bl=class{constructor(e){this.errors=e,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}};function q_(t){if(typeof t=="string")return null;let e=null;if(Array.isArray(t))t.forEach(n=>{if(n instanceof Map&&n.has("offset")){let r=n;e=parseFloat(r.get("offset")),r.delete("offset")}});else if(t instanceof Map&&t.has("offset")){let n=t;e=parseFloat(n.get("offset")),n.delete("offset")}return e}function G_(t,e){if(t.hasOwnProperty("duration"))return t;if(typeof t=="number"){let i=Ri(t,e).duration;return cl(i,0,"")}let n=t;if(n.split(/\s+/).some(i=>i.charAt(0)=="{"&&i.charAt(1)=="{")){let i=cl(0,0,"");return i.dynamic=!0,i.strValue=n,i}let o=Ri(n,e);return cl(o.duration,o.delay,o.easing)}function cn(t){return t?(t=ge({},t),t.params&&(t.params=z_(t.params))):t={},t}function cl(t,e,n){return{duration:t,delay:e,easing:n}}function $l(t,e,n,r,o,i,s=null,a=!1){return{type:1,element:t,keyframes:e,preStyleProps:n,postStyleProps:r,duration:o,delay:i,totalTime:o+i,easing:s,subTimeline:a}}var zn=class{constructor(){this._map=new Map}get(e){return this._map.get(e)||[]}append(e,n){let r=this._map.get(e);r||this._map.set(e,r=[]),r.push(...n)}has(e){return this._map.has(e)}clear(){this._map.clear()}},W_=1,Q_=":enter",K_=new RegExp(Q_,"g"),Y_=":leave",Z_=new RegExp(Y_,"g");function Hl(t,e,n,r,o,i=new Map,s=new Map,a,u,l=[]){return new Il().buildKeyframes(t,e,n,r,o,i,s,a,u,l)}var Il=class{buildKeyframes(e,n,r,o,i,s,a,u,l,c=[]){l=l||new zn;let d=new _l(e,n,l,o,i,c,[]);d.options=u;let f=u.delay?Et(u.delay):0;d.currentTimeline.delayNextStep(f),d.currentTimeline.setStyles([s],null,d.errors,u),xe(this,r,d);let h=d.timelines.filter(p=>p.containsAnimation());if(h.length&&a.size){let p;for(let m=h.length-1;m>=0;m--){let y=h[m];if(y.element===n){p=y;break}}p&&!p.allowOnlyTimelineStyles()&&p.setStyles([a],null,d.errors,u)}return h.length?h.map(p=>p.buildKeyframes()):[$l(n,[],[],[],0,f,"",!1)]}visitTrigger(e,n){}visitState(e,n){}visitTransition(e,n){}visitAnimateChild(e,n){let r=n.subInstructions.get(n.element);if(r){let o=n.createSubContext(e.options),i=n.currentTimeline.currentTime,s=this._visitSubInstructions(r,o,o.options);i!=s&&n.transformIntoNewTimeline(s)}n.previousNode=e}visitAnimateRef(e,n){let r=n.createSubContext(e.options);r.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],n,r),this.visitReference(e.animation,r),n.transformIntoNewTimeline(r.currentTimeline.currentTime),n.previousNode=e}_applyAnimationRefDelays(e,n,r){for(let o of e){let i=o?.delay;if(i){let s=typeof i=="number"?i:Et(Mr(i,o?.params??{},n.errors));r.delayNextStep(s)}}}_visitSubInstructions(e,n,r){let i=n.currentTimeline.currentTime,s=r.duration!=null?Et(r.duration):null,a=r.delay!=null?Et(r.delay):null;return s!==0&&e.forEach(u=>{let l=n.appendInstructionToTimeline(u,s,a);i=Math.max(i,l.duration+l.delay)}),i}visitReference(e,n){n.updateOptions(e.options,!0),xe(this,e.animation,n),n.previousNode=e}visitSequence(e,n){let r=n.subContextCount,o=n,i=e.options;if(i&&(i.params||i.delay)&&(o=n.createSubContext(i),o.transformIntoNewTimeline(),i.delay!=null)){o.previousNode.type==M.Style&&(o.currentTimeline.snapshotCurrentStyles(),o.previousNode=Li);let s=Et(i.delay);o.delayNextStep(s)}e.steps.length&&(e.steps.forEach(s=>xe(this,s,o)),o.currentTimeline.applyStylesToKeyframe(),o.subContextCount>r&&o.transformIntoNewTimeline()),n.previousNode=e}visitGroup(e,n){let r=[],o=n.currentTimeline.currentTime,i=e.options&&e.options.delay?Et(e.options.delay):0;e.steps.forEach(s=>{let a=n.createSubContext(e.options);i&&a.delayNextStep(i),xe(this,s,a),o=Math.max(o,a.currentTimeline.currentTime),r.push(a.currentTimeline)}),r.forEach(s=>n.currentTimeline.mergeTimelineCollectedStyles(s)),n.transformIntoNewTimeline(o),n.previousNode=e}_visitTiming(e,n){if(e.dynamic){let r=e.strValue,o=n.params?Mr(r,n.params,n.errors):r;return Ri(o,n.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,n){let r=n.currentAnimateTimings=this._visitTiming(e.timings,n),o=n.currentTimeline;r.delay&&(n.incrementTime(r.delay),o.snapshotCurrentStyles());let i=e.style;i.type==M.Keyframes?this.visitKeyframes(i,n):(n.incrementTime(r.duration),this.visitStyle(i,n),o.applyStylesToKeyframe()),n.currentAnimateTimings=null,n.previousNode=e}visitStyle(e,n){let r=n.currentTimeline,o=n.currentAnimateTimings;!o&&r.hasCurrentStyleProperties()&&r.forwardFrame();let i=o&&o.easing||e.easing;e.isEmptyStep?r.applyEmptyStep(i):r.setStyles(e.styles,i,n.errors,n.options),n.previousNode=e}visitKeyframes(e,n){let r=n.currentAnimateTimings,o=n.currentTimeline.duration,i=r.duration,a=n.createSubContext().currentTimeline;a.easing=r.easing,e.styles.forEach(u=>{let l=u.offset||0;a.forwardTime(l*i),a.setStyles(u.styles,u.easing,n.errors,n.options),a.applyStylesToKeyframe()}),n.currentTimeline.mergeTimelineCollectedStyles(a),n.transformIntoNewTimeline(o+i),n.previousNode=e}visitQuery(e,n){let r=n.currentTimeline.currentTime,o=e.options||{},i=o.delay?Et(o.delay):0;i&&(n.previousNode.type===M.Style||r==0&&n.currentTimeline.hasCurrentStyleProperties())&&(n.currentTimeline.snapshotCurrentStyles(),n.previousNode=Li);let s=r,a=n.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!o.optional,n.errors);n.currentQueryTotal=a.length;let u=null;a.forEach((l,c)=>{n.currentQueryIndex=c;let d=n.createSubContext(e.options,l);i&&d.delayNextStep(i),l===n.element&&(u=d.currentTimeline),xe(this,e.animation,d),d.currentTimeline.applyStylesToKeyframe();let f=d.currentTimeline.currentTime;s=Math.max(s,f)}),n.currentQueryIndex=0,n.currentQueryTotal=0,n.transformIntoNewTimeline(s),u&&(n.currentTimeline.mergeTimelineCollectedStyles(u),n.currentTimeline.snapshotCurrentStyles()),n.previousNode=e}visitStagger(e,n){let r=n.parentContext,o=n.currentTimeline,i=e.timings,s=Math.abs(i.duration),a=s*(n.currentQueryTotal-1),u=s*n.currentQueryIndex;switch(i.duration<0?"reverse":i.easing){case"reverse":u=a-u;break;case"full":u=r.currentStaggerTime;break}let c=n.currentTimeline;u&&c.delayNextStep(u);let d=c.currentTime;xe(this,e.animation,n),n.previousNode=e,r.currentStaggerTime=o.currentTime-d+(o.startTime-r.currentTimeline.startTime)}},Li={},_l=class t{constructor(e,n,r,o,i,s,a,u){this._driver=e,this.element=n,this.subInstructions=r,this._enterClassName=o,this._leaveClassName=i,this.errors=s,this.timelines=a,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=Li,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=u||new ji(this._driver,n,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,n){if(!e)return;let r=e,o=this.options;r.duration!=null&&(o.duration=Et(r.duration)),r.delay!=null&&(o.delay=Et(r.delay));let i=r.params;if(i){let s=o.params;s||(s=this.options.params={}),Object.keys(i).forEach(a=>{(!n||!s.hasOwnProperty(a))&&(s[a]=Mr(i[a],s,this.errors))})}}_copyOptions(){let e={};if(this.options){let n=this.options.params;if(n){let r=e.params={};Object.keys(n).forEach(o=>{r[o]=n[o]})}}return e}createSubContext(e=null,n,r){let o=n||this.element,i=new t(this._driver,o,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(o,r||0));return i.previousNode=this.previousNode,i.currentAnimateTimings=this.currentAnimateTimings,i.options=this._copyOptions(),i.updateOptions(e),i.currentQueryIndex=this.currentQueryIndex,i.currentQueryTotal=this.currentQueryTotal,i.parentContext=this,this.subContextCount++,i}transformIntoNewTimeline(e){return this.previousNode=Li,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,n,r){let o={duration:n??e.duration,delay:this.currentTimeline.currentTime+(r??0)+e.delay,easing:""},i=new Cl(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,o,e.stretchStartingKeyframe);return this.timelines.push(i),o}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,n,r,o,i,s){let a=[];if(o&&a.push(this.element),e.length>0){e=e.replace(K_,"."+this._enterClassName),e=e.replace(Z_,"."+this._leaveClassName);let u=r!=1,l=this._driver.query(this.element,e,u);r!==0&&(l=r<0?l.slice(l.length+r,l.length):l.slice(0,r)),a.push(...l)}return!i&&a.length==0&&s.push(u_(n)),a}},ji=class t{constructor(e,n,r,o){this._driver=e,this.element=n,this.startTime=r,this._elementTimelineStylesLookup=o,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(n),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(n,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let n=this._keyframes.size===1&&this._pendingStyles.size;this.duration||n?(this.forwardTime(this.currentTime+e),n&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,n){return this.applyStylesToKeyframe(),new t(this._driver,e,n||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=W_,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,n){this._localTimelineStyles.set(e,n),this._globalTimelineStyles.set(e,n),this._styleSummary.set(e,{time:this.currentTime,value:n})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[n,r]of this._globalTimelineStyles)this._backFill.set(n,r||tt),this._currentKeyframe.set(n,tt);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,n,r,o){n&&this._previousKeyframe.set("easing",n);let i=o&&o.params||{},s=J_(e,this._globalTimelineStyles);for(let[a,u]of s){let l=Mr(u,i,r);this._pendingStyles.set(a,l),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??tt),this._updateStyle(a,l)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,n)=>{this._currentKeyframe.set(n,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,n)=>{this._currentKeyframe.has(n)||this._currentKeyframe.set(n,e)}))}snapshotCurrentStyles(){for(let[e,n]of this._localTimelineStyles)this._pendingStyles.set(e,n),this._updateStyle(e,n)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let n in this._currentKeyframe)e.push(n);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((n,r)=>{let o=this._styleSummary.get(r);(!o||n.time>o.time)&&this._updateStyle(r,n.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,n=new Set,r=this._keyframes.size===1&&this.duration===0,o=[];this._keyframes.forEach((a,u)=>{let l=new Map([...this._backFill,...a]);l.forEach((c,d)=>{c===Mi?e.add(d):c===tt&&n.add(d)}),r||l.set("offset",u/this.duration),o.push(l)});let i=[...e.values()],s=[...n.values()];if(r){let a=o[0],u=new Map(a);a.set("offset",0),u.set("offset",1),o=[a,u]}return $l(this.element,o,i,s,this.duration,this.startTime,this.easing,!1)}},Cl=class extends ji{constructor(e,n,r,o,i,s,a=!1){super(e,n,s.delay),this.keyframes=r,this.preStyleProps=o,this.postStyleProps=i,this._stretchStartingKeyframe=a,this.timings={duration:s.duration,delay:s.delay,easing:s.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:n,duration:r,easing:o}=this.timings;if(this._stretchStartingKeyframe&&n){let i=[],s=r+n,a=n/s,u=new Map(e[0]);u.set("offset",0),i.push(u);let l=new Map(e[0]);l.set("offset",Vp(a)),i.push(l);let c=e.length-1;for(let d=1;d<=c;d++){let f=new Map(e[d]),h=f.get("offset"),p=n+h*r;f.set("offset",Vp(p/s)),i.push(f)}r=s,n=0,o="",e=i}return $l(this.element,e,this.preStyleProps,this.postStyleProps,r,n,o,!0)}};function Vp(t,e=3){let n=Math.pow(10,e-1);return Math.round(t*n)/n}function J_(t,e){let n=new Map,r;return t.forEach(o=>{if(o==="*"){r??=e.keys();for(let i of r)n.set(i,tt)}else for(let[i,s]of o)n.set(i,s)}),n}function Bp(t,e,n,r,o,i,s,a,u,l,c,d,f){return{type:0,element:t,triggerName:e,isRemovalTransition:o,fromState:n,fromStyles:i,toState:r,toStyles:s,timelines:a,queriedElements:u,preStyleProps:l,postStyleProps:c,totalTime:d,errors:f}}var dl={},Vi=class{constructor(e,n,r){this._triggerName=e,this.ast=n,this._stateStyles=r}match(e,n,r,o){return X_(this.ast.matchers,e,n,r,o)}buildStyles(e,n,r){let o=this._stateStyles.get("*");return e!==void 0&&(o=this._stateStyles.get(e?.toString())||o),o?o.buildStyles(n,r):new Map}build(e,n,r,o,i,s,a,u,l,c){let d=[],f=this.ast.options&&this.ast.options.params||dl,h=a&&a.params||dl,p=this.buildStyles(r,h,d),m=u&&u.params||dl,y=this.buildStyles(o,m,d),D=new Set,T=new Map,R=new Map,L=o==="void",le={params:nm(m,f),delay:this.ast.options?.delay},Y=c?[]:Hl(e,n,this.ast.animation,i,s,p,y,le,l,d),Z=0;return Y.forEach(ne=>{Z=Math.max(ne.duration+ne.delay,Z)}),d.length?Bp(n,this._triggerName,r,o,L,p,y,[],[],T,R,Z,d):(Y.forEach(ne=>{let rt=ne.element,wt=Ne(T,rt,new Set);ne.preStyleProps.forEach(Vt=>wt.add(Vt));let zl=Ne(R,rt,new Set);ne.postStyleProps.forEach(Vt=>zl.add(Vt)),rt!==n&&D.add(rt)}),Bp(n,this._triggerName,r,o,L,p,y,Y,[...D.values()],T,R,Z))}};function X_(t,e,n,r,o){return t.some(i=>i(e,n,r,o))}function nm(t,e){let n=ge({},e);return Object.entries(t).forEach(([r,o])=>{o!=null&&(n[r]=o)}),n}var Sl=class{constructor(e,n,r){this.styles=e,this.defaultParams=n,this.normalizer=r}buildStyles(e,n){let r=new Map,o=nm(e,this.defaultParams);return this.styles.styles.forEach(i=>{typeof i!="string"&&i.forEach((s,a)=>{s&&(s=Mr(s,o,n));let u=this.normalizer.normalizePropertyName(a,n);s=this.normalizer.normalizeStyleValue(a,u,s,n),r.set(a,s)})}),r}};function eC(t,e,n){return new Ml(t,e,n)}var Ml=class{constructor(e,n,r){this.name=e,this.ast=n,this._normalizer=r,this.transitionFactories=[],this.states=new Map,n.states.forEach(o=>{let i=o.options&&o.options.params||{};this.states.set(o.name,new Sl(o.style,i,r))}),$p(this.states,"true","1"),$p(this.states,"false","0"),n.transitions.forEach(o=>{this.transitionFactories.push(new Vi(e,o,this.states))}),this.fallbackTransition=tC(e,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,n,r,o){return this.transitionFactories.find(s=>s.match(e,n,r,o))||null}matchStyles(e,n,r){return this.fallbackTransition.buildStyles(e,n,r)}};function tC(t,e,n){let r=[(s,a)=>!0],o={type:M.Sequence,steps:[],options:null},i={type:M.Transition,animation:o,matchers:r,options:null,queryCount:0,depCount:0};return new Vi(t,i,e)}function $p(t,e,n){t.has(e)?t.has(n)||t.set(n,t.get(e)):t.has(n)&&t.set(e,t.get(n))}var nC=new zn,Tl=class{constructor(e,n,r){this.bodyNode=e,this._driver=n,this._normalizer=r,this._animations=new Map,this._playersById=new Map,this.players=[]}register(e,n){let r=[],o=[],i=Bl(this._driver,n,r,o);if(r.length)throw m_(r);o.length&&void 0,this._animations.set(e,i)}_buildPlayer(e,n,r){let o=e.element,i=Kp(this._normalizer,e.keyframes,n,r);return this._driver.animate(o,i,e.duration,e.delay,e.easing,[],!0)}create(e,n,r={}){let o=[],i=this._animations.get(e),s,a=new Map;if(i?(s=Hl(this._driver,n,i,Ll,Pi,new Map,new Map,r,nC,o),s.forEach(c=>{let d=Ne(a,c.element,new Map);c.postStyleProps.forEach(f=>d.set(f,null))})):(o.push(g_()),s=[]),o.length)throw y_(o);a.forEach((c,d)=>{c.forEach((f,h)=>{c.set(h,this._driver.computeStyle(d,h,tt))})});let u=s.map(c=>{let d=a.get(c.element);return this._buildPlayer(c,new Map,d)}),l=jt(u);return this._playersById.set(e,l),l.onDestroy(()=>this.destroy(e)),this.players.push(l),l}destroy(e){let n=this._getPlayer(e);n.destroy(),this._playersById.delete(e);let r=this.players.indexOf(n);r>=0&&this.players.splice(r,1)}_getPlayer(e){let n=this._playersById.get(e);if(!n)throw v_(e);return n}listen(e,n,r,o){let i=Rl(n,"","","");return Fl(this._getPlayer(e),r,i,o),()=>{}}command(e,n,r,o){if(r=="register"){this.register(e,o[0]);return}if(r=="create"){let s=o[0]||{};this.create(e,n,s);return}let i=this._getPlayer(e);switch(r){case"play":i.play();break;case"pause":i.pause();break;case"reset":i.reset();break;case"restart":i.restart();break;case"finish":i.finish();break;case"init":i.init();break;case"setPosition":i.setPosition(parseFloat(o[0]));break;case"destroy":this.destroy(e);break}}},Hp="ng-animate-queued",rC=".ng-animate-queued",fl="ng-animate-disabled",oC=".ng-animate-disabled",iC="ng-star-inserted",sC=".ng-star-inserted",aC=[],rm={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},uC={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},Ge="__ng_removed",Tr=class{get params(){return this.options.params}constructor(e,n=""){this.namespaceId=n;let r=e&&e.hasOwnProperty("value"),o=r?e.value:e;if(this.value=cC(o),r){let i=e,{value:s}=i,a=Yl(i,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let n=e.params;if(n){let r=this.options.params;Object.keys(n).forEach(o=>{r[o]==null&&(r[o]=n[o])})}}},Sr="void",hl=new Tr(Sr),xl=class{constructor(e,n,r){this.id=e,this.hostElement=n,this._engine=r,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+e,ke(n,this._hostClassName)}listen(e,n,r,o){if(!this._triggers.has(n))throw D_(r,n);if(r==null||r.length==0)throw E_(n);if(!dC(r))throw w_(r,n);let i=Ne(this._elementListeners,e,[]),s={name:n,phase:r,callback:o};i.push(s);let a=Ne(this._engine.statesByElement,e,new Map);return a.has(n)||(ke(e,Ti),ke(e,Ti+"-"+n),a.set(n,hl)),()=>{this._engine.afterFlush(()=>{let u=i.indexOf(s);u>=0&&i.splice(u,1),this._triggers.has(n)||a.delete(n)})}}register(e,n){return this._triggers.has(e)?!1:(this._triggers.set(e,n),!0)}_getTrigger(e){let n=this._triggers.get(e);if(!n)throw b_(e);return n}trigger(e,n,r,o=!0){let i=this._getTrigger(n),s=new xr(this.id,n,e),a=this._engine.statesByElement.get(e);a||(ke(e,Ti),ke(e,Ti+"-"+n),this._engine.statesByElement.set(e,a=new Map));let u=a.get(n),l=new Tr(r,this.id);if(!(r&&r.hasOwnProperty("value"))&&u&&l.absorbOptions(u.options),a.set(n,l),u||(u=hl),!(l.value===Sr)&&u.value===l.value){if(!pC(u.params,l.params)){let m=[],y=i.matchStyles(u.value,u.params,m),D=i.matchStyles(l.value,l.params,m);m.length?this._engine.reportError(m):this._engine.afterFlush(()=>{dn(e,y),nt(e,D)})}return}let f=Ne(this._engine.playersByElement,e,[]);f.forEach(m=>{m.namespaceId==this.id&&m.triggerName==n&&m.queued&&m.destroy()});let h=i.matchTransition(u.value,l.value,e,l.params),p=!1;if(!h){if(!o)return;h=i.fallbackTransition,p=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:n,transition:h,fromState:u,toState:l,player:s,isFallbackTransition:p}),p||(ke(e,Hp),s.onStart(()=>{Hn(e,Hp)})),s.onDone(()=>{let m=this.players.indexOf(s);m>=0&&this.players.splice(m,1);let y=this._engine.playersByElement.get(e);if(y){let D=y.indexOf(s);D>=0&&y.splice(D,1)}}),this.players.push(s),f.push(s),s}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(n=>n.delete(e)),this._elementListeners.forEach((n,r)=>{this._elementListeners.set(r,n.filter(o=>o.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let n=this._engine.playersByElement.get(e);n&&(n.forEach(r=>r.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,n){let r=this._engine.driver.query(e,Fi,!0);r.forEach(o=>{if(o[Ge])return;let i=this._engine.fetchNamespacesByElement(o);i.size?i.forEach(s=>s.triggerLeaveAnimation(o,n,!1,!0)):this.clearElementCache(o)}),this._engine.afterFlushAnimationsDone(()=>r.forEach(o=>this.clearElementCache(o)))}triggerLeaveAnimation(e,n,r,o){let i=this._engine.statesByElement.get(e),s=new Map;if(i){let a=[];if(i.forEach((u,l)=>{if(s.set(l,u.value),this._triggers.has(l)){let c=this.trigger(e,l,Sr,o);c&&a.push(c)}}),a.length)return this._engine.markElementAsRemoved(this.id,e,!0,n,s),r&&jt(a).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let n=this._elementListeners.get(e),r=this._engine.statesByElement.get(e);if(n&&r){let o=new Set;n.forEach(i=>{let s=i.name;if(o.has(s))return;o.add(s);let u=this._triggers.get(s).fallbackTransition,l=r.get(s)||hl,c=new Tr(Sr),d=new xr(this.id,s,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:s,transition:u,fromState:l,toState:c,player:d,isFallbackTransition:!0})})}}removeNode(e,n){let r=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,n),this.triggerLeaveAnimation(e,n,!0))return;let o=!1;if(r.totalAnimations){let i=r.players.length?r.playersByQueriedElement.get(e):[];if(i&&i.length)o=!0;else{let s=e;for(;s=s.parentNode;)if(r.statesByElement.get(s)){o=!0;break}}}if(this.prepareLeaveAnimationListeners(e),o)r.markElementAsRemoved(this.id,e,!1,n);else{let i=e[Ge];(!i||i===rm)&&(r.afterFlush(()=>this.clearElementCache(e)),r.destroyInnerAnimations(e),r._onRemovalComplete(e,n))}}insertNode(e,n){ke(e,this._hostClassName)}drainQueuedTransitions(e){let n=[];return this._queue.forEach(r=>{let o=r.player;if(o.destroyed)return;let i=r.element,s=this._elementListeners.get(i);s&&s.forEach(a=>{if(a.name==r.triggerName){let u=Rl(i,r.triggerName,r.fromState.value,r.toState.value);u._data=e,Fl(r.player,a.phase,u,a.callback)}}),o.markedForDestroy?this._engine.afterFlush(()=>{o.destroy()}):n.push(r)}),this._queue=[],n.sort((r,o)=>{let i=r.transition.ast.depCount,s=o.transition.ast.depCount;return i==0||s==0?i-s:this._engine.driver.containsElement(r.element,o.element)?1:-1})}destroy(e){this.players.forEach(n=>n.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},Nl=class{_onRemovalComplete(e,n){this.onRemovalComplete(e,n)}constructor(e,n,r,o){this.bodyNode=e,this.driver=n,this._normalizer=r,this.scheduler=o,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(i,s)=>{}}get queuedPlayers(){let e=[];return this._namespaceList.forEach(n=>{n.players.forEach(r=>{r.queued&&e.push(r)})}),e}createNamespace(e,n){let r=new xl(e,n,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,n)?this._balanceNamespaceList(r,n):(this.newHostElements.set(n,r),this.collectEnterElement(n)),this._namespaceLookup[e]=r}_balanceNamespaceList(e,n){let r=this._namespaceList,o=this.namespacesByHostElement;if(r.length-1>=0){let s=!1,a=this.driver.getParentElement(n);for(;a;){let u=o.get(a);if(u){let l=r.indexOf(u);r.splice(l+1,0,e),s=!0;break}a=this.driver.getParentElement(a)}s||r.unshift(e)}else r.push(e);return o.set(n,e),e}register(e,n){let r=this._namespaceLookup[e];return r||(r=this.createNamespace(e,n)),r}registerTrigger(e,n,r){let o=this._namespaceLookup[e];o&&o.register(n,r)&&this.totalAnimations++}destroy(e,n){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let r=this._fetchNamespace(e);this.namespacesByHostElement.delete(r.hostElement);let o=this._namespaceList.indexOf(r);o>=0&&this._namespaceList.splice(o,1),r.destroy(n),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let n=new Set,r=this.statesByElement.get(e);if(r){for(let o of r.values())if(o.namespaceId){let i=this._fetchNamespace(o.namespaceId);i&&n.add(i)}}return n}trigger(e,n,r,o){if(Ai(n)){let i=this._fetchNamespace(e);if(i)return i.trigger(n,r,o),!0}return!1}insertNode(e,n,r,o){if(!Ai(n))return;let i=n[Ge];if(i&&i.setForRemoval){i.setForRemoval=!1,i.setForMove=!0;let s=this.collectedLeaveElements.indexOf(n);s>=0&&this.collectedLeaveElements.splice(s,1)}if(e){let s=this._fetchNamespace(e);s&&s.insertNode(n,r)}o&&this.collectEnterElement(n)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,n){n?this.disabledNodes.has(e)||(this.disabledNodes.add(e),ke(e,fl)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),Hn(e,fl))}removeNode(e,n,r){if(Ai(n)){this.scheduler?.notify();let o=e?this._fetchNamespace(e):null;o?o.removeNode(n,r):this.markElementAsRemoved(e,n,!1,r);let i=this.namespacesByHostElement.get(n);i&&i.id!==e&&i.removeNode(n,r)}else this._onRemovalComplete(n,r)}markElementAsRemoved(e,n,r,o,i){this.collectedLeaveElements.push(n),n[Ge]={namespaceId:e,setForRemoval:o,hasAnimation:r,removedBeforeQueried:!1,previousTriggersValues:i}}listen(e,n,r,o,i){return Ai(n)?this._fetchNamespace(e).listen(n,r,o,i):()=>{}}_buildInstruction(e,n,r,o,i){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,r,o,e.fromState.options,e.toState.options,n,i)}destroyInnerAnimations(e){let n=this.driver.query(e,Fi,!0);n.forEach(r=>this.destroyActiveAnimationsForElement(r)),this.playersByQueriedElement.size!=0&&(n=this.driver.query(e,yl,!0),n.forEach(r=>this.finishActiveQueriedAnimationOnElement(r)))}destroyActiveAnimationsForElement(e){let n=this.playersByElement.get(e);n&&n.forEach(r=>{r.queued?r.markedForDestroy=!0:r.destroy()})}finishActiveQueriedAnimationOnElement(e){let n=this.playersByQueriedElement.get(e);n&&n.forEach(r=>r.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return jt(this.players).onDone(()=>e());e()})}processLeaveNode(e){let n=e[Ge];if(n&&n.setForRemoval){if(e[Ge]=rm,n.namespaceId){this.destroyInnerAnimations(e);let r=this._fetchNamespace(n.namespaceId);r&&r.clearElementCache(e)}this._onRemovalComplete(e,n.setForRemoval)}e.classList?.contains(fl)&&this.markElementAsDisabled(e,!1),this.driver.query(e,oC,!0).forEach(r=>{this.markElementAsDisabled(r,!1)})}flush(e=-1){let n=[];if(this.newHostElements.size&&(this.newHostElements.forEach((r,o)=>this._balanceNamespaceList(r,o)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let r=0;r<this.collectedEnterElements.length;r++){let o=this.collectedEnterElements[r];ke(o,iC)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let r=[];try{n=this._flushAnimations(r,e)}finally{for(let o=0;o<r.length;o++)r[o]()}}else for(let r=0;r<this.collectedLeaveElements.length;r++){let o=this.collectedLeaveElements[r];this.processLeaveNode(o)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(r=>r()),this._flushFns=[],this._whenQuietFns.length){let r=this._whenQuietFns;this._whenQuietFns=[],n.length?jt(n).onDone(()=>{r.forEach(o=>o())}):r.forEach(o=>o())}}reportError(e){throw I_(e)}_flushAnimations(e,n){let r=new zn,o=[],i=new Map,s=[],a=new Map,u=new Map,l=new Map,c=new Set;this.disabledNodes.forEach(v=>{c.add(v);let b=this.driver.query(v,rC,!0);for(let _=0;_<b.length;_++)c.add(b[_])});let d=this.bodyNode,f=Array.from(this.statesByElement.keys()),h=qp(f,this.collectedEnterElements),p=new Map,m=0;h.forEach((v,b)=>{let _=Ll+m++;p.set(b,_),v.forEach(k=>ke(k,_))});let y=[],D=new Set,T=new Set;for(let v=0;v<this.collectedLeaveElements.length;v++){let b=this.collectedLeaveElements[v],_=b[Ge];_&&_.setForRemoval&&(y.push(b),D.add(b),_.hasAnimation?this.driver.query(b,sC,!0).forEach(k=>D.add(k)):T.add(b))}let R=new Map,L=qp(f,Array.from(D));L.forEach((v,b)=>{let _=Pi+m++;R.set(b,_),v.forEach(k=>ke(k,_))}),e.push(()=>{h.forEach((v,b)=>{let _=p.get(b);v.forEach(k=>Hn(k,_))}),L.forEach((v,b)=>{let _=R.get(b);v.forEach(k=>Hn(k,_))}),y.forEach(v=>{this.processLeaveNode(v)})});let le=[],Y=[];for(let v=this._namespaceList.length-1;v>=0;v--)this._namespaceList[v].drainQueuedTransitions(n).forEach(_=>{let k=_.player,re=_.element;if(le.push(k),this.collectedEnterElements.length){let ce=re[Ge];if(ce&&ce.setForMove){if(ce.previousTriggersValues&&ce.previousTriggersValues.has(_.triggerName)){let Bt=ce.previousTriggersValues.get(_.triggerName),Ae=this.statesByElement.get(_.element);if(Ae&&Ae.has(_.triggerName)){let Nr=Ae.get(_.triggerName);Nr.value=Bt,Ae.set(_.triggerName,Nr)}}k.destroy();return}}let We=!d||!this.driver.containsElement(d,re),be=R.get(re),bt=p.get(re),K=this._buildInstruction(_,r,bt,be,We);if(K.errors&&K.errors.length){Y.push(K);return}if(We){k.onStart(()=>dn(re,K.fromStyles)),k.onDestroy(()=>nt(re,K.toStyles)),o.push(k);return}if(_.isFallbackTransition){k.onStart(()=>dn(re,K.fromStyles)),k.onDestroy(()=>nt(re,K.toStyles)),o.push(k);return}let Wl=[];K.timelines.forEach(ce=>{ce.stretchStartingKeyframe=!0,this.disabledNodes.has(ce.element)||Wl.push(ce)}),K.timelines=Wl,r.append(re,K.timelines);let sm={instruction:K,player:k,element:re};s.push(sm),K.queriedElements.forEach(ce=>Ne(a,ce,[]).push(k)),K.preStyleProps.forEach((ce,Bt)=>{if(ce.size){let Ae=u.get(Bt);Ae||u.set(Bt,Ae=new Set),ce.forEach((Nr,zi)=>Ae.add(zi))}}),K.postStyleProps.forEach((ce,Bt)=>{let Ae=l.get(Bt);Ae||l.set(Bt,Ae=new Set),ce.forEach((Nr,zi)=>Ae.add(zi))})});if(Y.length){let v=[];Y.forEach(b=>{v.push(__(b.triggerName,b.errors))}),le.forEach(b=>b.destroy()),this.reportError(v)}let Z=new Map,ne=new Map;s.forEach(v=>{let b=v.element;r.has(b)&&(ne.set(b,b),this._beforeAnimationBuild(v.player.namespaceId,v.instruction,Z))}),o.forEach(v=>{let b=v.element;this._getPreviousPlayers(b,!1,v.namespaceId,v.triggerName,null).forEach(k=>{Ne(Z,b,[]).push(k),k.destroy()})});let rt=y.filter(v=>Gp(v,u,l)),wt=new Map;zp(wt,this.driver,T,l,tt).forEach(v=>{Gp(v,u,l)&&rt.push(v)});let Vt=new Map;h.forEach((v,b)=>{zp(Vt,this.driver,new Set(v),u,Mi)}),rt.forEach(v=>{let b=wt.get(v),_=Vt.get(v);wt.set(v,new Map([...b?.entries()??[],..._?.entries()??[]]))});let Ui=[],ql=[],Gl={};s.forEach(v=>{let{element:b,player:_,instruction:k}=v;if(r.has(b)){if(c.has(b)){_.onDestroy(()=>nt(b,k.toStyles)),_.disabled=!0,_.overrideTotalTime(k.totalTime),o.push(_);return}let re=Gl;if(ne.size>1){let be=b,bt=[];for(;be=be.parentNode;){let K=ne.get(be);if(K){re=K;break}bt.push(be)}bt.forEach(K=>ne.set(K,re))}let We=this._buildAnimation(_.namespaceId,k,Z,i,Vt,wt);if(_.setRealPlayer(We),re===Gl)Ui.push(_);else{let be=this.playersByElement.get(re);be&&be.length&&(_.parentPlayer=jt(be)),o.push(_)}}else dn(b,k.fromStyles),_.onDestroy(()=>nt(b,k.toStyles)),ql.push(_),c.has(b)&&o.push(_)}),ql.forEach(v=>{let b=i.get(v.element);if(b&&b.length){let _=jt(b);v.setRealPlayer(_)}}),o.forEach(v=>{v.parentPlayer?v.syncPlayerEvents(v.parentPlayer):v.destroy()});for(let v=0;v<y.length;v++){let b=y[v],_=b[Ge];if(Hn(b,Pi),_&&_.hasAnimation)continue;let k=[];if(a.size){let We=a.get(b);We&&We.length&&k.push(...We);let be=this.driver.query(b,yl,!0);for(let bt=0;bt<be.length;bt++){let K=a.get(be[bt]);K&&K.length&&k.push(...K)}}let re=k.filter(We=>!We.destroyed);re.length?fC(this,b,re):this.processLeaveNode(b)}return y.length=0,Ui.forEach(v=>{this.players.push(v),v.onDone(()=>{v.destroy();let b=this.players.indexOf(v);this.players.splice(b,1)}),v.play()}),Ui}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,n,r,o,i){let s=[];if(n){let a=this.playersByQueriedElement.get(e);a&&(s=a)}else{let a=this.playersByElement.get(e);if(a){let u=!i||i==Sr;a.forEach(l=>{l.queued||!u&&l.triggerName!=o||s.push(l)})}}return(r||o)&&(s=s.filter(a=>!(r&&r!=a.namespaceId||o&&o!=a.triggerName))),s}_beforeAnimationBuild(e,n,r){let o=n.triggerName,i=n.element,s=n.isRemovalTransition?void 0:e,a=n.isRemovalTransition?void 0:o;for(let u of n.timelines){let l=u.element,c=l!==i,d=Ne(r,l,[]);this._getPreviousPlayers(l,c,s,a,n.toState).forEach(h=>{let p=h.getRealPlayer();p.beforeDestroy&&p.beforeDestroy(),h.destroy(),d.push(h)})}dn(i,n.fromStyles)}_buildAnimation(e,n,r,o,i,s){let a=n.triggerName,u=n.element,l=[],c=new Set,d=new Set,f=n.timelines.map(p=>{let m=p.element;c.add(m);let y=m[Ge];if(y&&y.removedBeforeQueried)return new Lt(p.duration,p.delay);let D=m!==u,T=hC((r.get(m)||aC).map(Z=>Z.getRealPlayer())).filter(Z=>{let ne=Z;return ne.element?ne.element===m:!1}),R=i.get(m),L=s.get(m),le=Kp(this._normalizer,p.keyframes,R,L),Y=this._buildPlayer(p,le,T);if(p.subTimeline&&o&&d.add(m),D){let Z=new xr(e,a,m);Z.setRealPlayer(Y),l.push(Z)}return Y});l.forEach(p=>{Ne(this.playersByQueriedElement,p.element,[]).push(p),p.onDone(()=>lC(this.playersByQueriedElement,p.element,p))}),c.forEach(p=>ke(p,Rp));let h=jt(f);return h.onDestroy(()=>{c.forEach(p=>Hn(p,Rp)),nt(u,n.toStyles)}),d.forEach(p=>{Ne(o,p,[]).push(h)}),h}_buildPlayer(e,n,r){return n.length>0?this.driver.animate(e.element,n,e.duration,e.delay,e.easing,r):new Lt(e.duration,e.delay)}},xr=class{constructor(e,n,r){this.namespaceId=e,this.triggerName=n,this.element=r,this._player=new Lt,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((n,r)=>{n.forEach(o=>Fl(e,r,void 0,o))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let n=this._player;n.triggerCallback&&e.onStart(()=>n.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,n){Ne(this._queuedCallbacks,e,[]).push(n)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let n=this._player;n.triggerCallback&&n.triggerCallback(e)}};function lC(t,e,n){let r=t.get(e);if(r){if(r.length){let o=r.indexOf(n);r.splice(o,1)}r.length==0&&t.delete(e)}return r}function cC(t){return t??null}function Ai(t){return t&&t.nodeType===1}function dC(t){return t=="start"||t=="done"}function Up(t,e){let n=t.style.display;return t.style.display=e??"none",n}function zp(t,e,n,r,o){let i=[];n.forEach(u=>i.push(Up(u)));let s=[];r.forEach((u,l)=>{let c=new Map;u.forEach(d=>{let f=e.computeStyle(l,d,o);c.set(d,f),(!f||f.length==0)&&(l[Ge]=uC,s.push(l))}),t.set(l,c)});let a=0;return n.forEach(u=>Up(u,i[a++])),s}function qp(t,e){let n=new Map;if(t.forEach(a=>n.set(a,[])),e.length==0)return n;let r=1,o=new Set(e),i=new Map;function s(a){if(!a)return r;let u=i.get(a);if(u)return u;let l=a.parentNode;return n.has(l)?u=l:o.has(l)?u=r:u=s(l),i.set(a,u),u}return e.forEach(a=>{let u=s(a);u!==r&&n.get(u).push(a)}),n}function ke(t,e){t.classList?.add(e)}function Hn(t,e){t.classList?.remove(e)}function fC(t,e,n){jt(n).onDone(()=>t.processLeaveNode(e))}function hC(t){let e=[];return om(t,e),e}function om(t,e){for(let n=0;n<t.length;n++){let r=t[n];r instanceof _r?om(r.players,e):e.push(r)}}function pC(t,e){let n=Object.keys(t),r=Object.keys(e);if(n.length!=r.length)return!1;for(let o=0;o<n.length;o++){let i=n[o];if(!e.hasOwnProperty(i)||t[i]!==e[i])return!1}return!0}function Gp(t,e,n){let r=n.get(t);if(!r)return!1;let o=e.get(t);return o?r.forEach(i=>o.add(i)):e.set(t,r),n.delete(t),!0}var Bi=class{constructor(e,n,r,o){this._driver=n,this._normalizer=r,this._triggerCache={},this.onRemovalComplete=(i,s)=>{},this._transitionEngine=new Nl(e.body,n,r,o),this._timelineEngine=new Tl(e.body,n,r),this._transitionEngine.onRemovalComplete=(i,s)=>this.onRemovalComplete(i,s)}registerTrigger(e,n,r,o,i){let s=e+"-"+o,a=this._triggerCache[s];if(!a){let u=[],l=[],c=Bl(this._driver,i,u,l);if(u.length)throw h_(o,u);l.length&&void 0,a=eC(o,c,this._normalizer),this._triggerCache[s]=a}this._transitionEngine.registerTrigger(n,o,a)}register(e,n){this._transitionEngine.register(e,n)}destroy(e,n){this._transitionEngine.destroy(e,n)}onInsert(e,n,r,o){this._transitionEngine.insertNode(e,n,r,o)}onRemove(e,n,r){this._transitionEngine.removeNode(e,n,r)}disableAnimations(e,n){this._transitionEngine.markElementAsDisabled(e,n)}process(e,n,r,o){if(r.charAt(0)=="@"){let[i,s]=Op(r),a=o;this._timelineEngine.command(i,n,s,a)}else this._transitionEngine.trigger(e,n,r,o)}listen(e,n,r,o,i){if(r.charAt(0)=="@"){let[s,a]=Op(r);return this._timelineEngine.listen(s,n,a,i)}return this._transitionEngine.listen(e,n,r,o,i)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function mC(t,e){let n=null,r=null;return Array.isArray(e)&&e.length?(n=pl(e[0]),e.length>1&&(r=pl(e[e.length-1]))):e instanceof Map&&(n=pl(e)),n||r?new Al(t,n,r):null}var Un=class Un{constructor(e,n,r){this._element=e,this._startStyles=n,this._endStyles=r,this._state=0;let o=Un.initialStylesByElement.get(e);o||Un.initialStylesByElement.set(e,o=new Map),this._initialStyles=o}start(){this._state<1&&(this._startStyles&&nt(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(nt(this._element,this._initialStyles),this._endStyles&&(nt(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(Un.initialStylesByElement.delete(this._element),this._startStyles&&(dn(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(dn(this._element,this._endStyles),this._endStyles=null),nt(this._element,this._initialStyles),this._state=3)}};Un.initialStylesByElement=new WeakMap;var Al=Un;function pl(t){let e=null;return t.forEach((n,r)=>{gC(r)&&(e=e||new Map,e.set(r,n))}),e}function gC(t){return t==="display"||t==="position"}var $i=class{constructor(e,n,r,o){this.element=e,this.keyframes=n,this.options=r,this._specialStyles=o,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=r.duration,this._delay=r.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let n=()=>this._onFinish();this.domPlayer.addEventListener("finish",n),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",n)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let n=[];return e.forEach(r=>{n.push(Object.fromEntries(r))}),n}_triggerWebAnimation(e,n,r){return e.animate(this._convertKeyframesToObject(n),r)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((r,o)=>{o!=="offset"&&e.set(o,this._finished?r:Vl(this.element,o))}),this.currentSnapshot=e}triggerCallback(e){let n=e==="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},Ol=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}matchesElement(e,n){return!1}containsElement(e,n){return Yp(e,n)}getParentElement(e){return kl(e)}query(e,n,r){return Zp(e,n,r)}computeStyle(e,n,r){return Vl(e,n)}animate(e,n,r,o,i,s=[]){let a=o==0?"both":"forwards",u={duration:r,delay:o,fill:a};i&&(u.easing=i);let l=new Map,c=s.filter(h=>h instanceof $i);k_(r,o)&&c.forEach(h=>{h.currentSnapshot.forEach((p,m)=>l.set(m,p))});let d=P_(n).map(h=>new Map(h));d=L_(e,d,l);let f=mC(e,d);return new $i(e,d,u,f)}};function lR(t,e,n){return t==="noop"?new Bi(e,new Jp,new gl,n):new Bi(e,new Ol,new El,n)}var Wp=class{constructor(e,n){this._driver=e;let r=[],o=[],i=Bl(e,n,r,o);if(r.length)throw d_(r);o.length&&void 0,this._animationAst=i}buildTimelines(e,n,r,o,i){let s=Array.isArray(n)?kp(n):n,a=Array.isArray(r)?kp(r):r,u=[];i=i||new zn;let l=Hl(this._driver,e,this._animationAst,Ll,Pi,s,a,o,i,u);if(u.length)throw f_(u);return l}},Oi="@",im="@.disabled",Hi=class{constructor(e,n,r,o){this.namespaceId=e,this.delegate=n,this.engine=r,this._onDestroy=o,this.\u0275type=0}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,n){return this.delegate.createElement(e,n)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,n){this.delegate.appendChild(e,n),this.engine.onInsert(this.namespaceId,n,e,!1)}insertBefore(e,n,r,o=!0){this.delegate.insertBefore(e,n,r),this.engine.onInsert(this.namespaceId,n,e,o)}removeChild(e,n,r){this.engine.onRemove(this.namespaceId,n,this.delegate)}selectRootElement(e,n){return this.delegate.selectRootElement(e,n)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,n,r,o){this.delegate.setAttribute(e,n,r,o)}removeAttribute(e,n,r){this.delegate.removeAttribute(e,n,r)}addClass(e,n){this.delegate.addClass(e,n)}removeClass(e,n){this.delegate.removeClass(e,n)}setStyle(e,n,r,o){this.delegate.setStyle(e,n,r,o)}removeStyle(e,n,r){this.delegate.removeStyle(e,n,r)}setProperty(e,n,r){n.charAt(0)==Oi&&n==im?this.disableAnimations(e,!!r):this.delegate.setProperty(e,n,r)}setValue(e,n){this.delegate.setValue(e,n)}listen(e,n,r){return this.delegate.listen(e,n,r)}disableAnimations(e,n){this.engine.disableAnimations(e,n)}},Pl=class extends Hi{constructor(e,n,r,o,i){super(n,r,o,i),this.factory=e,this.namespaceId=n}setProperty(e,n,r){n.charAt(0)==Oi?n.charAt(1)=="."&&n==im?(r=r===void 0?!0:!!r,this.disableAnimations(e,r)):this.engine.process(this.namespaceId,e,n.slice(1),r):this.delegate.setProperty(e,n,r)}listen(e,n,r){if(n.charAt(0)==Oi){let o=yC(e),i=n.slice(1),s="";return i.charAt(0)!=Oi&&([i,s]=vC(i)),this.engine.listen(this.namespaceId,o,i,s,a=>{let u=a._data||-1;this.factory.scheduleListenerCallback(u,r,a)})}return this.delegate.listen(e,n,r)}};function yC(t){switch(t){case"body":return document.body;case"document":return document;case"window":return window;default:return t}}function vC(t){let e=t.indexOf("."),n=t.substring(0,e),r=t.slice(e+1);return[n,r]}var Qp=class{constructor(e,n,r){this.delegate=e,this.engine=n,this._zone=r,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,n.onRemovalComplete=(o,i)=>{let s=i?.parentNode(o);s&&i.removeChild(s,o)}}createRenderer(e,n){let r="",o=this.delegate.createRenderer(e,n);if(!e||!n?.data?.animation){let l=this._rendererCache,c=l.get(o);if(!c){let d=()=>l.delete(o);c=new Hi(r,o,this.engine,d),l.set(o,c)}return c}let i=n.id,s=n.id+"-"+this._currentId;this._currentId++,this.engine.register(s,e);let a=l=>{Array.isArray(l)?l.forEach(a):this.engine.registerTrigger(i,s,e,l.name,l)};return n.data.animation.forEach(a),new Pl(this,s,o,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,n,r){if(e>=0&&e<this._microtaskId){this._zone.run(()=>n(r));return}let o=this._animationCallbacksBuffer;o.length==0&&queueMicrotask(()=>{this._zone.run(()=>{o.forEach(i=>{let[s,a]=i;s(a)}),this._animationCallbacksBuffer=[]})}),o.push([n,r])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}};export{ge as a,It as b,DC as c,EC as d,wC as e,pm as f,ee as g,Cm as h,O as i,ss as j,as as k,ye as l,Kn as m,Am as n,St as o,Om as p,st as q,Xn as r,fs as s,Ye as t,hs as u,ps as v,Bm as w,at as x,$m as y,Ie as z,Km as A,je as B,wn as C,Zm as D,Jm as E,gs as F,ig as G,qt as H,sg as I,jc as J,ag as K,Bc as L,er as M,bn as N,ys as O,vs as P,ug as Q,lg as R,cg as S,$c as T,hg as U,Hc as V,pg as W,Es as X,mg as Y,gg as Z,yg as _,vg as $,bs as aa,Dg as ba,Eg as ca,wg as da,bg as ea,Ig as fa,_g as ga,Cg as ha,g as ia,$e as ja,Od as ka,H as la,eu as ma,oP as na,B as oa,te as pa,Q as qa,iP as ra,Vd as sa,Bd as ta,or as ua,Nt as va,sP as wa,iu as xa,ft as ya,su as za,aP as Aa,nf as Ba,At as Ca,uP as Da,Cy as Ea,fu as Fa,lP as Ga,cP as Ha,dP as Ia,fP as Ja,hP as Ka,yv as La,Vn as Ma,nn as Na,Cu as Oa,kt as Pa,He as Qa,ea as Ra,pP as Sa,mP as Ta,xv as Ua,Nv as Va,gP as Wa,yP as Xa,oi as Ya,kv as Za,vP as _a,DP as $a,EP as ab,wP as bb,bP as cb,Uf as db,IP as eb,Kf as fb,_P as gb,CP as hb,dr as ib,SP as jb,z as kb,MP as lb,dt as mb,pa as nb,hi as ob,ya as pb,pi as qb,an as rb,Oe as sb,gt as tb,KE as ub,AP as vb,ow as wb,cw as xb,Ma as yb,fw as zb,Bu as Ab,xa as Bb,Ew as Cb,Ow as Db,Vh as Eb,Pw as Fb,OP as Gb,PP as Hb,FP as Ib,RP as Jb,kP as Kb,LP as Lb,jP as Mb,Wh as Nb,Qh as Ob,Gw as Pb,Kh as Qb,Yh as Rb,Kw as Sb,VP as Tb,Zw as Ub,Jw as Vb,ob as Wb,ib as Xb,BP as Yb,$P as Zb,HP as _b,ub as $b,UP as ac,zP as bc,qP as cc,GP as dc,WP as ec,QP as fc,db as gc,Jh as hc,fb as ic,hb as jc,KP as kc,pb as lc,YP as mc,ZP as nc,JP as oc,XP as pc,eF as qc,tF as rc,nF as sc,rF as tc,oF as uc,iF as vc,sF as wc,Ib as xc,_b as yc,aF as zc,Cb as Ac,Wu as Bc,Qu as Cc,uF as Dc,br as Ec,Ub as Fc,lF as Gc,Ku as Hc,Yu as Ic,cF as Jc,dF as Kc,Yb as Lc,Zb as Mc,fF as Nc,eI as Oc,Ju as Pc,PF as Qc,dp as Rc,wp as Sc,oI as Tc,FF as Uc,RF as Vc,kF as Wc,LF as Xc,Tp as Yc,jF as Zc,VF as _c,BF as $c,$F as ad,HF as bd,UF as cd,zF as dd,zI as ed,qF as fd,GF as gd,Dp as hd,KF as id,YF as jd,ZF as kd,ul as ld,JF as md,XF as nd,eR as od,tR as pd,nR as qd,kl as rd,T_ as sd,aR as td,Yp as ud,Zp as vd,Jp as wd,Fp as xd,ml as yd,gl as zd,P_ as Ad,uR as Bd,k_ as Cd,El as Dd,Bi as Ed,$i as Fd,Ol as Gd,lR as Hd,Wp as Id,Hi as Jd,Pl as Kd,Qp as Ld};
