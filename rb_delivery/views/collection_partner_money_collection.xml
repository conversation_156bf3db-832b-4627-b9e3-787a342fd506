<?xml version="1.0"?>
<odoo>
	<data>
		<record id="paperformat_multi_print_partner_collection_detailed" model="report.paperformat">
			<field name="name">Detailed Partner Collection</field>
			<field name="default" eval="True"/>
			<field name="format">A4</field>
			<field name="orientation">Portrait</field>
			<field name="margin_top">40</field>
			<field name="margin_bottom">18</field>
			<field name="margin_left">3</field>
			<field name="margin_right">3</field>
			<field name="header_line" eval="False"/>
			<field name="header_spacing">30</field>
			<field name="dpi">90</field>
		</record>
		<report id="report_rb_delivery_order_multi_print_partner_collection_detailed" 
		        string="Detailed Partner Collection" 
		        model="rb_delivery.multi_print_orders_money_collector" 
		        report_type="qweb-pdf" 
		        name="rb_delivery.multi_partner_collection_detailed" 
		        paperformat="paperformat_multi_print_partner_collection_detailed"
				groups="base.group_system"/>
		
		<template id="minimal_layout_inherit" inherit_id="web.minimal_layout">
			<xpath expr="//head" position="inside">
				<link rel="stylesheet" href="/rb_delivery/static/src/css/report.css"/>
			</xpath>
		</template>
		<template id="multi_partner_collection_detailed">
			<style type="text/css">
				body {
					color: #000 !important;
				}
				.background {
					background-color: blue;
				}
				.table_header_data {
					font-size: 12px !important;
				}
				.table_data {
					font-size: 13px !important;
				}
				.p {
					font-size: 22px;
					color: green;
				}
				table th {
					border: 1px solid black;
				}
			</style>
			<t t-call="web.basic_layout">
				<t t-foreach="docs" t-as="doc">
					<t t-if="not o" t-set="o" t-value="doc"/>
					<t t-if="not company">
						<t t-if="company_id">
							<t t-set="company" t-value="company_id"/>
						</t>
						<t t-elif="o and 'company_id' in o">
							<t t-set="company" t-value="o.company_id.sudo()"/>
						</t>
						<t t-else="else">
							<t t-set="company" t-value="res_company"/>
						</t>
					</t>
					
					<div class="header" style="font-size:13px !important">
						<div class="row">
							<div class="col-4 details_col">
								<div class="col-12">
									Business name: 
									<t t-if="doc.sudo().business_id">
										<span t-field="doc.sudo().business_id"/>
									</t>
									<t t-else="else">
										<span t-field="doc.sudo().business_id"/>
									</t>
								</div>
								<div class="col-12">
									Business mobile number:  
									<span t-field="doc.mobile_number"/>
								</div>
								<div class="col-12">
									Date: 
									<span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M')"/>
								</div>
								<div class="col-12">
									Address:  -
								</div>
							</div>
							<div class="col-4 title_barcode_col">
								<p class="text-center">Partner money collection</p>
								<div class="oe_center">
									<img style="width:200px;height:50px;display:block !important;margin:0 auto !important" t-attf-src="data:image/*;base64,{{doc.barcode}}"/>
									<h5 class="text-center" style="font-size:18px !important;font-weight:bold">
										<span t-field="doc.sequence"/>
									</h5>
								</div>
							</div>
							<div class="col-4 text-center logo_col">
								<img t-if="company.logo" t-att-src="image_data_uri(company.logo)" alt="Logo" style="max-height: 100px;"/>
							</div>
						</div>
					</div>
					<div class="footer">
						<div class="row">
							<div>
								The recipient's signature: ..............................
							</div>
						</div>
						<div class="row">
							<div>
								<small>
									<span>Page</span>
									<span class="page"/>
									of
									<span class="topage"/>
								</small>
							</div>
						</div>
					</div>
					<div class="page">
						<div class="image_bg"></div>
						<div class="oe_structure"></div>
						<t t-set="customer_payment_total" t-value="sum([float(order.customer_payment) if order.customer_payment else 0.0 for order in doc.order_ids])"/>
						<t t-set="total" t-value="0"/>
						<table class="table table-condensed collection-partner-money-collection" style="font-size:0.7em;width:100%;border:1px solid black">
							<thead>
								<tr>
									<th style="border:1px solid black;">#</th>
									<th style="border:1px solid black;">Barcode</th>
									<th style="border:1px solid black;">order state</th>
									<th style="border:1px solid black;">Customer Payment</th>
									<th style="border:1px solid black;">Total Amount</th>
									<th style="border:1px solid black;" t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">Delivery cost</th>
									<th style="border:1px solid black;">Total net value</th>
									<th style="border:1px solid black;">Sequence number</th>
									<th style="border:1px solid black;">Customer Full Address</th>
								</tr>
							</thead>
							<tbody class="sale_tbody">
								<t t-set="counter" t-value="0"/>
								<t t-foreach="doc.order_ids" t-as="order">
									<t t-set="counter" t-value="counter + 1"/>
									<tr style="page-break-inside: avoid;">
										<td>
											<t t-esc="counter"/>
										</td>
										<td style="padding: 2px">
											<img style="width:220px;height:70px;" t-attf-src="data:image/*;base64,{{order.barcode}}"/>
											<p style="margin: 0px !important" t-field="order.sequence"/>
										</td>
										<td>
											<t t-if="order.returned_reason">
												<span t-field="order.returned_reason"/>
											</t>
										</td>
										<td>
											<span t-field="order.customer_payment"/>
										</td>
										<td>
											<span t-field="order.copy_total_cost"/>
										</td>
										<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">
											<span t-field="order.delivery_cost"/>
										</td>
										<td>
											<span t-field="order.required_from_business" t-options='{"widget": "float", "precision": decimal_value}' />
										</td>
										<td style="padding: 0 ; margin: 0 ;">
											<span t-field="order.sequence" />
											<span t-field="order.reference_id" />
										</td>
										<td>
										<t t-if="order.address_tag">
											<span t-field="order.address_tag"/> - <span t-field="order.customer_address"/>
										</t>
										<t t-else="">
											<t t-if="order.customer_sub_area">
												<span t-field="order.customer_area"/> - <span t-field="order.customer_sub_area"/> - <span t-field="order.customer_address"/>
											</t>
											<t t-else="">
												<span t-field="order.customer_area"/> - <span t-field="order.customer_address"/>
											</t>
										</t>
									</td>
									</tr>
									<tr t-if="len(doc.order_ids) == counter" style="font-weight:bold;page-break-inside: avoid;">
										<td>Total</td>
										<td></td>
										<td></td>
										<td>
											<t t-esc="customer_payment_total"/>
										</td>
										<td>
											<t t-esc="'%.2f' % (total_amount if total_amount is not None else 0)"/>
										</td>
										<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">
											<t t-esc="delivery_cost_total"/>
										</td>
										<td>
											<t t-esc="'%.2f' % (required_from_business_total if required_from_business_total is not None else 0)" ></t>
										</td>
										<td></td>
										<td></td>
									</tr>
								</t>
							</tbody>
						</table>
						<div class="oe_structure"></div>
						<p style="page-break-after:always;"> </p>
					</div>
				</t>
			</t>
		</template>
	</data>
</odoo>