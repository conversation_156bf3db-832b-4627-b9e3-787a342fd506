<?xml version="1.0"?>
<odoo>
  <data noupdate="1">

    <!-- Find and remove the Discuss menu -->
    <!-- <record id="mail.menu_root_discuss" model="ir.ui.menu"> -->
      <!-- <field name="active" eval="False"/> -->
    <!-- </record> -->

    <!-- Find and remove the Discuss menu -->
    <!-- <record id="mail.menu_root_discuss" model="ir.ui.menu"> -->
      <!-- <field name="active" eval="False"/> -->
    <!-- </record> -->

    <!-- Main Menu  -->
    <menuitem id="rb_delivery_top_menu" name="Delivery" sequence="-100" />

    <!-- Settings  -->
    <menuitem id="rb_delivery_settings" name="Settings" sequence="2" />

    <menuitem id="menu_queue_job_root"
        name="Job Queue"
        web_icon="queue_job,static/description/icon.png"
        groups="queue_job.group_queue_job_manager"
        parent="rb_delivery_settings"/>

    <menuitem id="menu_queue"
        name="Queue"
        parent="menu_queue_job_root"/>

    <menuitem id="menu_queue_job"
        action="queue_job.action_queue_job"
        sequence="10"
        parent="menu_queue"/>

    <menuitem id="menu_queue_job_channel"
        action="queue_job.action_queue_job_channel"
        sequence="12"
        parent="menu_queue"/>

    <menuitem id="menu_queue_job_function"
        action="queue_job.action_queue_job_function"
        sequence="14"
        parent="menu_queue"/>

    <!-- Communications & Integrations  -->
    <menuitem id="rb_delivery_communication_integration" parent="rb_delivery_settings" name="Communications &amp; Integrations" sequence="3" />

    <!-- Company Configurations  -->
    <menuitem id="rb_delivery_company_configurations" parent="rb_delivery_settings" name="Company Configurations" sequence="4" />

    <!-- Dashboard -->
    <menuitem name="Dashboard" id="main_menu_rb_delivery_dashboard" parent="rb_delivery_top_menu" sequence="10" groups="role_accounting,role_business,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,base.group_system"/>

    <!-- My Dashboard -->
    <menuitem name="My Dashboard" id="main_menu_rb_delivery_my_dashboard" parent="main_menu_rb_delivery_dashboard" action="board.open_board_my_dash_action" sequence="1" groups="role_accounting,role_business,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_junior_accounting,role_super_manager,base.group_system"/>

    <menuitem id="menu_rb_delivery_orders" name="Orders" parent="rb_delivery_top_menu" sequence="1"/>

    <menuitem id="menu_rb_delivery_area_config" name="Area Configurations" parent="rb_delivery_settings" sequence="10"/>

    <!-- Active Order -->
    <record model="ir.actions.act_window" id="action_rb_delivery_order">
      <field name="name">Active Orders</field>
      <field name="res_model">rb_delivery.order</field>
      <field name="view_id" ref="view_tree_rb_delivery_order"></field>
      <field name="view_mode">tree,form,graph</field>
      <field name="context">{'search_default_order_month_date':0}</field>
      <!-- <field name="domain">[]</field> -->
      <field name="limit" eval="150"/>
      <field name="domain">[('state','!=','deleted'),('state','!=','completed'),('state','!=','delivered_completed'),('state','!=','completed_returned'),('state','!=','canceled'),('state','!=','cancelled_completed')]</field>
    </record>
    <menuitem id="menu_rb_delivery_order" name="Active Orders" parent="menu_rb_delivery_orders" sequence="15" action="action_rb_delivery_order" />

    <!--Financial Order -->
    <record model="ir.actions.act_window" id="action_rb_delivery_financial_order">
      <field name="name">Financial Orders</field>
      <field name="res_model">rb_delivery.order</field>
      <field name="view_id" ref="view_tree_rb_delivery_order"></field>
      <field name="view_mode">tree,form,graph</field>
      <field name="context">{'search_default_order_month_date':0}</field>
      <!-- <field name="domain">[]</field> -->
      <field name="limit" eval="150"/>
      <field name="domain">[]</field>
    </record>
    <menuitem id="menu_rb_delivery_financial_order" name="Financial Orders" parent="menu_rb_delivery_orders" sequence="15" action="action_rb_delivery_financial_order"  groups="role_access_manager"/>

    <!--Operation Order -->
    <record model="ir.actions.act_window" id="action_rb_delivery_opertation_order">
      <field name="name">Operation Orders</field>
      <field name="res_model">rb_delivery.order</field>
      <field name="view_id" ref="view_tree_rb_delivery_order"></field>
      <field name="view_mode">tree,form,graph</field>
      <field name="context">{'search_default_order_month_date':0}</field>
      <!-- <field name="domain">[]</field> -->
      <field name="limit" eval="150"/>
      <field name="domain">[]</field>
    </record>
    <menuitem id="menu_rb_delivery_operation_order" name="Operation Orders" parent="menu_rb_delivery_orders" sequence="15" action="action_rb_delivery_opertation_order" groups="role_access_manager"/>



  <!-- Collections -->
    <menuitem id="menu_rb_delivery_collection" name="Collections" parent="rb_delivery_top_menu" sequence="12"/>

    <!-- Money Collections -->
    <menuitem id="menu_rb_delivery_collections" name="Money Collections" parent="menu_rb_delivery_collection" sequence="1" groups="role_super_manager,base.group_system,role_business,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- Returned Collections -->
    <menuitem id="menu_rb_delivery_returned_collections" name="Returned Collections" parent="menu_rb_delivery_collection" sequence="2" groups="role_super_manager,base.group_system,role_business,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- Agent Returned Collections -->
    <menuitem id="menu_rb_delivery_agent_returned_collections" name="Agent Returned Collections" parent="menu_rb_delivery_collection" sequence="3" groups="role_super_manager,base.group_system,role_business,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- Run sheet Collections -->
    <menuitem id="menu_rb_delivery_runsheet_collections" name="Runsheet Collections" parent="menu_rb_delivery_collection" sequence="4" groups="role_super_manager,base.group_system,role_business,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- Agent Collections -->
    <menuitem id="menu_rb_delivery_agent_collections" name="Money Collections" parent="menu_rb_delivery_collection" sequence="5" groups="role_super_manager,base.group_system,role_business,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- Collection Report -->
    <act_window id="action_rb_delivery_multi_print_orders_money_collector_model" name="Active Money Collection" res_model="rb_delivery.multi_print_orders_money_collector" view_mode="tree,form" domain="	['|','&amp;',('state','!=','deleted'),('state','!=','completed'),('state','=',False)]"/>
    <menuitem id="menu_rb_delivery_multi_print_orders_money_collector" name="Active Money Collection" parent="menu_rb_delivery_collections" sequence="1" action="action_rb_delivery_multi_print_orders_money_collector_model" groups="role_super_manager,base.group_system,role_business,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- All Collection Report -->
    <act_window id="action_rb_delivery_all_multi_print_orders_money_collector_model" name="All Money Collection" res_model="rb_delivery.multi_print_orders_money_collector" view_mode="tree,form" />
    <menuitem id="menu_rb_delivery_all_multi_print_orders_money_collector" name="All Money Collection" parent="menu_rb_delivery_collections" sequence="2" action="action_rb_delivery_all_multi_print_orders_money_collector_model" groups="role_super_manager,base.group_system,role_business,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- Returned Collection Report -->
    <act_window id="action_rb_delivery_returned_money_collection_model" name="Returned collection" res_model="rb_delivery.returned_money_collection" view_mode="tree,form" domain="	['|','&amp;',('state','!=','deleted'),('state','!=','completed_returned'),('state','=',False)]"/>
    <menuitem id="menu_rb_delivery_returned_money_collection" name="Active Returned collection" parent="menu_rb_delivery_returned_collections" sequence="1" action="action_rb_delivery_returned_money_collection_model" groups="role_super_manager,base.group_system,role_business,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- All Returned Collection Report -->
    <act_window id="action_rb_delivery_all_returned_money_collection_model" name="Returned collection" res_model="rb_delivery.returned_money_collection" view_mode="tree,form" />
    <menuitem id="menu_rb_delivery_all_returned_money_collection" name="All Returned collection" parent="menu_rb_delivery_returned_collections" sequence="2" action="action_rb_delivery_all_returned_money_collection_model" groups="role_super_manager,base.group_system,role_business,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- Agent Returned Collection Report -->
    <act_window id="action_rb_delivery_agent_returned_collection_model" name="Agent Returned collection" res_model="rb_delivery.agent_returned_collection" view_mode="tree,form" domain="	['|','&amp;',('state','!=','deleted'),('state','!=','completed_returned'),('state','=',False)]"/>
    <menuitem id="menu_rb_delivery_agent_returned_collection" name="Active Agent Returned collection" parent="menu_rb_delivery_agent_returned_collections" sequence="1" action="action_rb_delivery_agent_returned_collection_model" groups="base.group_erp_manager"/>

    <!-- All Agent Returned Collection Report -->
    <act_window id="action_rb_delivery_all_agent_returned_collection_model" name="All Agent Returned collection" res_model="rb_delivery.agent_returned_collection" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_all_agent_returned_collection" name="All Agent Returned collection" parent="menu_rb_delivery_agent_returned_collections" sequence="2" action="action_rb_delivery_all_agent_returned_collection_model" groups="base.group_erp_manager"/>

    <!-- Agent Collection Report -->
    <act_window id="action_rb_delivery_agent_money_collection_model" name="Agent collection" res_model="rb_delivery.agent_money_collection" view_mode="tree,form" domain="	['|','&amp;',('state','!=','completed'),('state','!=','deleted'),('state','=',False)]"/>
    <menuitem id="menu_rb_delivery_agent_money_collection" name="Active Agent collection" parent="menu_rb_delivery_agent_collections" sequence="1" action="action_rb_delivery_agent_money_collection_model" groups="role_super_manager,base.group_system,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- All Agent Collection Report -->
    <act_window id="action_rb_delivery_all_agent_money_collection_model" name="All Agent collection" res_model="rb_delivery.agent_money_collection" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_all_agent_money_collection" name="All Agent collection" parent="menu_rb_delivery_agent_collections" sequence="2" action="action_rb_delivery_all_agent_money_collection_model" groups="role_super_manager,base.group_system,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <!-- Run sheet Report -->
    <act_window id="action_rb_delivery_runsheet" name="Run Sheet collection" res_model="rb_delivery.runsheet" view_mode="tree,form" domain="['|','&amp;',('state','!=','completed'),('state','!=','deleted'),('state','=',False)]"/>
    <menuitem id="menu_rb_delivery_runsheet" name="Active Run Sheet collection" parent="menu_rb_delivery_runsheet_collections" sequence="1" action="action_rb_delivery_runsheet" groups="role_super_manager,base.group_system,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_data_entry"/>

    <!-- All Run sheet Report -->
    <act_window id="action_rb_delivery_all_runsheet" name="All Run Sheet collections" res_model="rb_delivery.runsheet" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_all_runsheet" name="All Run Sheet collection" parent="menu_rb_delivery_runsheet_collections" sequence="2" action="action_rb_delivery_all_runsheet" groups="role_super_manager,base.group_system,role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_data_entry"/>

    <!-- Users -->
    <menuitem id="menu_rb_delivery_user" name="Users" parent="rb_delivery_top_menu" sequence="13" groups="role_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,role_data_entry,base.group_system,role_business" />

    <!-- Users -->
    <record model="ir.actions.act_window" id="action_rb_delivery_all_users">
      <field name="name">All Users</field>
      <field name="res_model">rb_delivery.user</field>
      <field name="view_mode">tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain">[]</field>
      <field name="context">{'create_role':'rb_delivery.role_business'}</field>
    </record>
    <menuitem id="menu_rb_delivery_all_users" name="All Users" parent="menu_rb_delivery_user" sequence="1" action="action_rb_delivery_all_users" groups="role_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,role_data_entry,role_call_center,base.group_system" />

    <!-- Employees -->
    <record model="ir.actions.act_window" id="action_rb_delivery_employees">
      <field name="name">Employees</field>
      <field name="res_model">rb_delivery.user</field>
      <field name="view_mode">tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain">[('role_code','!=','rb_delivery.role_driver'),('role_code','!=','rb_delivery.role_business')]</field>
      <field name="context">{'create_role':'rb_delivery.role_manager'}</field>
    </record>
    <menuitem id="menu_rb_delivery_all_employees" name="Employees" parent="menu_rb_delivery_user" sequence="2" action="action_rb_delivery_employees" groups="role_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,role_data_entry,role_call_center,base.group_system" />

    <!-- Business -->
    <record model="ir.actions.act_window" id="action_rb_delivery_business">
      <field name="name">Business</field>
      <field name="res_model">rb_delivery.user</field>
      <field name="view_mode">tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain">[('role_code','=','rb_delivery.role_business')]</field>
      <field name="context">{'create_role':'rb_delivery.role_business'}</field>
    </record>
    <menuitem id="menu_rb_delivery_business" name="Business" parent="menu_rb_delivery_user" sequence="2" action="action_rb_delivery_business" groups="role_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,role_data_entry,base.group_system,role_business,role_call_center" />


    <!-- Delivery company -->
    <record model="ir.actions.act_window" id="action_rb_delivery_delivery_company">
      <field name="name">Delivery Company</field>
      <field name="res_model">rb_delivery.user</field>
      <field name="view_mode">kanban,tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain">[('role_code','=','rb_delivery.role_delivery_company')]</field>
      <field name="context">{'create_role':'rb_delivery.role_delivery_company'}</field>

    </record>
    <menuitem id="menu_rb_delivery_delivery_company" name="Delivery Company" parent="rb_delivery.menu_rb_delivery_user" sequence="3" action="action_rb_delivery_delivery_company" groups="rb_delivery.role_accounting,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales,rb_delivery.role_super_manager,rb_delivery.role_data_entry,rb_delivery.role_call_center,base.group_system" />


    <!-- Drivers -->
    <record model="ir.actions.act_window" id="action_rb_delivery_driver">
      <field name="name">Drivers</field>
      <field name="res_model">rb_delivery.user</field>
      <field name="view_mode">tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain">[('role_code','=','rb_delivery.role_driver')]</field>
      <field name="context">{'create_role':'rb_delivery.role_driver'}</field>

    </record>
    <menuitem id="menu_rb_delivery_driver" name="Driver" parent="menu_rb_delivery_user" sequence="3" action="action_rb_delivery_driver" groups="role_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,role_data_entry,role_call_center,base.group_system" />

    <!-- Pricing -->
    <menuitem id="menu_rb_delivery_pricing_menue" name="Pricing" parent="rb_delivery_settings" sequence="14" groups="role_pricelist_manager,base.group_system"  />

    <!-- Price List -->
    <act_window id="action_rb_delivery_pricelist" name="Pricelist" res_model="rb_delivery.pricelist" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_pricelist" name="Pricelist" parent="menu_rb_delivery_pricing_menue" sequence="15" action="action_rb_delivery_pricelist" />

    <!-- Price List Item-->
    <act_window id="action_rb_delivery_pricelist_item" name="Pricelist Item" res_model="rb_delivery.pricelist_item" view_mode="tree,form" />
    <menuitem id="menu_rb_delivery_pricelist_item" name="Pricelist Item" parent="menu_rb_delivery_pricing_menue" sequence="15" action="action_rb_delivery_pricelist_item" groups="role_pricelist_manager,base.group_system" />

    <record id="wizard_message_action" model="ir.actions.act_window">
      <field name="name">Confirmation Message</field>
      <field name="res_model">display.dialog.box</field>
      <field name="view_type">form</field>
      <field name="view_mode">form</field>
      <field name="target">new</field>
    </record>

    <record id="wizard_runsheet_message_action" model="ir.actions.act_window">
      <field name="name">Confirmation Message</field>
      <field name="res_model">display.runsheet_dialog.box</field>
      <field name="view_type">form</field>
      <field name="view_mode">form</field>
      <field name="target">new</field>
    </record>

    <!-- Report -->
    <menuitem id="menu_rb_delivery_report" name="Reports" parent="rb_delivery_top_menu" sequence="19" groups="base.group_system,role_super_manager,role_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales"/>

    <record id="action_rb_delivery_business_executive_report" model="ir.actions.act_window">
      <field name="name">Business Executive Report</field>
      <field name="res_model">rb_delivery.order_logs</field>
      <field name="view_mode">pivot,graph</field>
      <field name="domain">[('field_id.name', '=', 'state_id'),('business_id', '!=', False)]</field>
      <field name="view_id" ref="view_pivot_rb_delivery_order_logs"/>
    </record>
    <menuitem id="menu_rb_delivery_business_executive_report" name="Business Executive Report" parent="menu_rb_delivery_report" sequence="17" action="action_rb_delivery_business_executive_report"/>

    <record id="action_rb_delivery_driver_executive_report" model="ir.actions.act_window">
      <field name="name">Driver Executive Report</field>
      <field name="res_model">rb_delivery.order_logs</field>
      <field name="view_mode">pivot,graph</field>
      <field name="domain">[('field_id.name', '=', 'state_id'),('driver_id', '!=', False)]</field>
      <field name="view_id" ref="view_pivot_rb_delivery_driver_order_logs"/>
    </record>
    <menuitem id="menu_rb_delivery_driver_executive_report" name="Driver Executive Report" parent="menu_rb_delivery_report" sequence="17" action="action_rb_delivery_driver_executive_report"/>
    
    <act_window id="action_rb_delivery_location" name="Location" context="{'group_by': ['user_id']}" res_model="rb_delivery.location" view_mode="tree"/>
    <menuitem id="menu_rb_delivery_location" name="Location" parent="menu_rb_delivery_report" sequence="17" action="action_rb_delivery_location" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,base.group_system"/>

    <!-- driver order location  -->
    <act_window id="action_rb_delivery_driver_order_location" name="Driver Orders Location" context="{'group_by': ['user_id']}" res_model="rb_delivery.driver_order_location" view_mode="tree"/>
    <menuitem id="menu_rb_delivery_driver_order_location" name="Driver Orders Location" parent="menu_rb_delivery_report" sequence="17" action="action_rb_delivery_driver_order_location" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system"/>

    <!-- Notifications -->
    <menuitem id="menu_rb_delivery_notification" name="Notifications" parent="rb_delivery_settings" sequence="20"  groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,base.group_system,role_call_center"/>

    <!-- Configuration -->
    <menuitem id="menu_rb_delivery_configuration" name="Configurations" parent="rb_delivery_settings" sequence="21" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,base.group_system"/>

    <!-- Filters -->
    <act_window id="action_rb_delivery_filters" name="Filters" res_model="ir.filters" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_filters" name="Filters" parent="menu_rb_delivery_configuration" sequence="1" action="action_rb_delivery_filters" groups="role_super_manager,base.group_system"/>

    <!-- Order Type -->
    <act_window id="action_rb_delivery_order_type" name="Order Type" res_model="rb_delivery.order_type" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_order_type" name="Order Type" parent="menu_rb_delivery_configuration" sequence="15" action="action_rb_delivery_order_type"/>

    <!-- Order Type -->
    <act_window id="action_rb_delivery_service" name="Service" res_model="rb_delivery.service" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_service" name="Service" parent="menu_rb_delivery_configuration" sequence="15" action="action_rb_delivery_service"/>

    <!-- Sms -->
    <act_window id="action_rb_delivery_sms" name="Sms" res_model="rb_delivery.sms" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_sms" name="Sms" parent="rb_delivery_communication_integration" sequence="1" action="action_rb_delivery_sms"/>

    <act_window id="action_rb_delivery_order_draft" name="Not-Imported Orders" res_model="rb_delivery.order_draft" view_mode="tree,form" context="{'search_default_failed_orders':1}"/>
    <menuitem id="menu_rb_delivery_order_draft" name="Not-Imported Orders" parent="menu_rb_delivery_orders" sequence="16" action="action_rb_delivery_order_draft" groups="base.group_erp_manager"/>

    <act_window id="action_rb_delivery_draft_create_order" name="Reimport Orders" src_model="rb_delivery.order_draft" res_model="rb_delivery.draft_create_order" view_mode="form" target="new" multi="True" />

    <!-- [Group] Assign -->
    <act_window id="action_rb_delivery_order_select_unarchive" name="Unarchive Orders" groups="base.group_system" src_model="rb_delivery.order" res_model="rb_delivery.order_select_unarchive" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_select_archive" name="Archive Orders" groups="base.group_system" src_model="rb_delivery.order" res_model="rb_delivery.order_select_archive" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_set_to_delete" name="Set to deleted" groups="base.group_system" src_model="rb_delivery.order" res_model="rb_delivery.set_to_delete" view_mode="form" target="new" multi="True"/>
    <act_window id="action_rb_delivery_multi_assign_to_agent" name="Assign Agent" src_model="rb_delivery.order" res_model="rb_delivery.multi_assign_to_agent" view_mode="form" target="new" multi="True" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_accounting,role_super_manager,role_data_entry,base.group_system" />
    <act_window id="action_rb_delivery_order_select_state" name="Select State" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_call_center,role_business,role_accounting,role_data_entry" src_model="rb_delivery.order" res_model="rb_delivery.select_state" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_multi_area" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_data_entry" name="Select Area" src_model="rb_delivery.order" res_model="rb_delivery.multi_area" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_multi_sub_area" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_data_entry" name="Select Sub Area" src_model="rb_delivery.order" res_model="rb_delivery.multi_sub_area" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_multi_select_reschedule_date" name="Select Reschedule Date" src_model="rb_delivery.order" res_model="rb_delivery.multi_select_reschedule_date" view_mode="form" target="new" multi="True" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_data_entry" />
    <act_window id="action_rb_delivery_pre_paid_collection" name="Create Prepaid Collection" groups="role_collection_manager,role_super_manager,base.group_system" src_model="rb_delivery.order" res_model="rb_delivery.create_paid_collection" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_refresh_address" name="Refresh Address Values" src_model="rb_delivery.order" res_model="rb_delivery.refresh_address_wizard" view_mode="form" target="new" multi="True" />

    <!-- [Group] Collection -->
    <act_window id="action_rb_delivery_multi_print_orders_money_collector" context="{'default_report_type': 'business'}" name="Create Money Collection" view_id='rb_delivery.view_form_rb_delivery_multi_print_orders_money_collector_two' src_model="rb_delivery.order" res_model="rb_delivery.create_money_collection" view_mode="form" target="new" multi="True" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_accounting,role_junior_accounting" />
    <act_window id="action_rb_delivery_multi_print_orders_money_collector_branch" context="{'default_report_type': 'branch'}" name="Create Branch Collection" view_id='rb_delivery.view_form_rb_delivery_multi_print_orders_money_collector_two' src_model="rb_delivery.order" res_model="rb_delivery.create_money_collection" view_mode="form" target="new" multi="True" groups="base.group_system" />
    <act_window id="action_rb_delivery_returned_money_collection" context="{'default_report_type': 'business'}" name="Create Returned Collection" view_id='rb_delivery.view_form_rb_delivery_returned_money_collection_two' src_model="rb_delivery.order" res_model="rb_delivery.create_returned_collection" view_mode="form" target="new" multi="True" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_accounting,role_junior_accounting" />
    <act_window id="action_rb_delivery_agent_money_collection" context="{'default_report_type': 'business'}" name="Create Agent Collection" view_id='rb_delivery.view_form_rb_delivery_agent_money_collection_two' src_model="rb_delivery.order" res_model="rb_delivery.create_agent_collection" view_mode="form" target="new" multi="True" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_accounting,role_junior_accounting" />
    <act_window id="action_rb_delivery_agent_returned_collection" context="{'default_report_type': 'business'}" name="Create Agent Returned collection" view_id='rb_delivery.view_form_rb_delivery_agent_returned_collection_two' src_model="rb_delivery.order" res_model="rb_delivery.agent_returned_collection" view_mode="form" target="new" multi="True" groups="base.group_system" />
    <act_window id="action_rb_delivery_runsheet_report" name="Create Run Sheet" view_id='rb_delivery.view_form_rb_delivery_runsheet_two' src_model="rb_delivery.order" res_model="rb_delivery.create_runsheet" view_mode="form" target="new" multi="True" groups="base.group_system" />

    <!-- [Group] Others -->
    <act_window id="action_rb_delivery_order_multi_refresh_pricelist" name="Refresh Pricelist" groups="base.group_system" src_model="rb_delivery.order" res_model="rb_delivery.multi_refresh_pricelist" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_create_replacement" name="Replacement" groups="role_super_manager,base.group_system" src_model="rb_delivery.order" res_model="rb_delivery.create_replacement" view_mode="form" target="new" multi="True"/>
    <act_window id="action_rb_delivery_order_create_returned" name="Returned" groups="base.group_system"  src_model="rb_delivery.order" res_model="rb_delivery.create_returned" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_multi_assign_to_business" name="Select Business" groups="role_configuration_manager,base.group_system" src_model="rb_delivery.order" res_model="rb_delivery.multi_assign_to_business" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_change_to_previous_status" name="Change To Previous Status" groups="role_super_manager" src_model="rb_delivery.order" res_model="rb_delivery.change_to_previous_status" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_change_to_previous_agent" name="Change To Previous Agent" groups="role_super_manager" src_model="rb_delivery.order" res_model="rb_delivery.change_to_previous_agent" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_notify_business" name="Notify business" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_data_entry" src_model="rb_delivery.order" res_model="rb_delivery.notify_business" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_notify_orders" name="Notify Recepients" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_data_entry" src_model="rb_delivery.order" res_model="rb_delivery.notify_orders" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_select_from_to_area" name="Select Area" groups="role_configuration_manager" src_model="rb_delivery.pricelist_item" res_model="rb_delivery.select_from_to_area" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_select_country" name="Select Country" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_data_entry" src_model="rb_delivery.area" res_model="rb_delivery.select_country" view_mode="form" target="new" multi="True" />

    <!--collections-->
    <act_window id="action_rb_delivery_order_select_returned_collection_state" name="Select State" groups="role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_call_center,role_business" src_model="rb_delivery.returned_money_collection" res_model="rb_delivery.select_returned_collection_state" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_select_agent_money_collection_state" name="Select State" groups="role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_call_center" src_model="rb_delivery.agent_money_collection" res_model="rb_delivery.select_agent_money_collection_state" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_select_agent_returned_money_collection_state" name="Select State" groups="role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_call_center" src_model="rb_delivery.agent_returned_collection" res_model="rb_delivery.select_agent_returned_money_collection_state" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_select_money_collection_state" name="Select State" groups="role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_call_center,role_business" src_model="rb_delivery.multi_print_orders_money_collector" res_model="rb_delivery.select_money_collection_state" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_money_collection_select_archive" name="Archive Collections" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.multi_print_orders_money_collector" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':True,'model':'rb_delivery.multi_print_orders_money_collector'}" />
    <act_window id="action_rb_delivery_money_collection_select_unarchive" name="Unarchive Collections" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.multi_print_orders_money_collector" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':False,'model':'rb_delivery.multi_print_orders_money_collector'}"/>
    <act_window id="action_rb_delivery_money_collection_download_collections" name="Download Collections" src_model="rb_delivery.multi_print_orders_money_collector" res_model="rb_delivery.money_collection_download_all_attachments" view_mode="form" target="new" multi="True"/>
    <act_window id="action_rb_delivery_order_select_runsheet_state" name="Select State" groups="role_accounting,role_junior_accounting,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_call_center" src_model="rb_delivery.runsheet" res_model="rb_delivery.select_runsheet_state" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_multi_area" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_accounting" name="Select Area" src_model="rb_delivery.order" res_model="rb_delivery.multi_area" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_returned_collection_multi_assign_to_agent" name="Assign Agent" src_model="rb_delivery.returned_money_collection" res_model="rb_delivery.returned_collection_change_agent_wizard" view_mode="form" target="new" multi="True" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_accounting,role_junior_accounting" />
    <act_window id="action_rb_delivery_runsheet_collection_multi_assign_to_agent" name="Assign Agent" src_model="rb_delivery.runsheet" res_model="rb_delivery.runsheet_collection_change_agent_wizard" view_mode="form" target="new" multi="True" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_data_entry" />
    <act_window id="action_rb_delivery_money_collection_multi_assign_to_agent" name="Assign Agent" src_model="rb_delivery.multi_print_orders_money_collector" res_model="rb_delivery.money_collection_change_agent_wizard" view_mode="form" target="new" multi="True" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system,role_accounting,role_junior_accounting" />
    <act_window id="action_rb_delivery_order_money_collection_detach_orders" name="Detach Orders" groups="role_collection_manager" src_model="rb_delivery.multi_print_orders_money_collector" res_model="rb_delivery.collection_detach_order" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_agent_money_collection_detach_orders" name="Detach Orders" groups="role_collection_manager" src_model="rb_delivery.agent_money_collection" res_model="rb_delivery.agent_collection_detach_order" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_returned_agent_money_collection_detach_orders" name="Detach Orders" groups="role_collection_manager" src_model="rb_delivery.agent_returned_collection" res_model="rb_delivery.agent_returned_collection_detach_order" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_returned_money_collection_detach_orders" name="Detach Orders" groups="role_returned_collection_manager" src_model="rb_delivery.returned_money_collection" res_model="rb_delivery.returned_collection_detach_order" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_returned_money_collection_send_emails" name="Send Email" groups="role_super_manager,role_accounting" src_model="rb_delivery.returned_money_collection" res_model="rb_delivery.returned_collection_send_email" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_money_collection_send_emails" name="Send Email" groups="role_super_manager,role_accounting" src_model="rb_delivery.multi_print_orders_money_collector" res_model="rb_delivery.money_collection_send_email" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_order_runsheet_collection_detach_orders" name="Detach Orders" groups="role_collection_manager" src_model="rb_delivery.runsheet" res_model="rb_delivery.runsheet_detach_order" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_runsheet_select_archive" name="Archive Runsheet" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.runsheet" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':True,'model':'rb_delivery.runsheet'}" />
    <act_window id="action_rb_delivery_runsheet_select_unarchive" name="Unarchive Runsheet" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.runsheet" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':False,'model':'rb_delivery.runsheet'}"/>
    <act_window id="action_rb_delivery_returned_money_collection_select_archive" name="Archive Returned Collections" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.returned_money_collection" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':True,'model':'rb_delivery.returned_money_collection'}" />
    <act_window id="action_rb_delivery_returned_money_collection_select_unarchive" name="Unarchive Returned Collections" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.returned_money_collection" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':False,'model':'rb_delivery.returned_money_collection'}"/>
    <act_window id="action_rb_delivery_agent_money_collection_select_archive" name="Archive Agent Collections" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.agent_money_collection" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':True,'model':'rb_delivery.agent_money_collection'}" />
    <act_window id="action_rb_delivery_agent_money_collection_select_unarchive" name="Unarchive Agent Collections" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.agent_money_collection" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':False,'model':'rb_delivery.agent_money_collection'}"/>
    <act_window id="action_rb_delivery_agent_returned_money_collection_select_archive" name="Archive Agent Returned Collections" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.agent_returned_collection" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':True,'model':'rb_delivery.agent_returned_collection'}" />
    <act_window id="action_rb_delivery_agent_returned_money_collection_select_unarchive" name="Unarchive Agent Returned Collections" groups="role_collection_archiver,base.group_system" src_model="rb_delivery.agent_returned_collection" res_model="rb_delivery.money_collection_select_archive" view_mode="form" target="new" multi="True" context="{'archive':False,'model':'rb_delivery.agent_returned_collection'}"/>

    <!-- deprecated -->
    <act_window id="action_rb_delivery_order_money_collection_compute_areas" name="Compute Areas" groups="base.group_system" src_model="rb_delivery.multi_print_orders_money_collector" res_model="rb_delivery.multi_compute_area" view_mode="form" target="new" multi="True" />


    <!-- Notification Center -->
    <act_window id="action_rb_delivery_notification_center" name="Notification Center" res_model="rb_delivery.notification_center" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_notification_center" name="Notification Center" parent="menu_rb_delivery_notification" sequence="15" action="action_rb_delivery_notification_center" />

    <!-- Chat Notification -->
    <act_window id="action_rb_delivery_chat_notification" name="Chat Notification" res_model="rb_delivery.chat_notification" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_chat_notification" name="Chat Notification" parent="menu_rb_delivery_notification" sequence="15" action="action_rb_delivery_chat_notification" />

    <!-- Notification Builder -->
    <act_window id="action_rb_delivery_action" name="Notification Builder" res_model="rb_delivery.action" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_action" name="Notification Builder" parent="menu_rb_delivery_notification" sequence="15" action="action_rb_delivery_action" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,base.group_system"/>

    <!-- Notification Type -->
    <act_window id="action_rb_delivery_notification_type" name="Notification Type" res_model="rb_delivery.notification_type" view_mode="tree"/>
    <menuitem id="menu_rb_delivery_notification_type" name="Notification Type" parent="menu_rb_delivery_notification" sequence="15" action="action_rb_delivery_notification_type" />

    <!-- Notification Sound -->
    <act_window id="action_rb_delivery_notification_sound" name="Notification Sound" res_model="rb_delivery.notification_sound" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_notification_sound" name="Notification Sound" parent="menu_rb_delivery_notification" sequence="15" action="action_rb_delivery_notification_sound" />

    <!-- Announcements -->
    <act_window id="action_announcement" name="Announcements" res_model="rb_delivery.announcement" view_mode="tree,form"/>
    <menuitem id="menu_announcement_root" name="Announcements" parent="menu_rb_delivery_notification" sequence="15" action="action_announcement" />

    <!-- Area -->
    <act_window id="action_rb_delivery_area" name="Area" res_model="rb_delivery.area" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_area" name="Area" parent="menu_rb_delivery_area_config" sequence="15" action="action_rb_delivery_area" />

    <!-- Sub Area -->
    <act_window id="action_rb_delivery_sub_area" name="Sub Area" res_model="rb_delivery.sub_area" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_sub_area" name="Sub Area" parent="menu_rb_delivery_area_config" sequence="15" action="action_rb_delivery_sub_area" />

    <!-- Address Tags -->
    <act_window id="action_rb_delivery_address_tags" name="Address Tags" res_model="rb_delivery.address_tags" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_address_tags" name="Address Tags" parent="menu_rb_delivery_area_config" sequence="17" action="action_rb_delivery_address_tags" />

    <!-- Area Map -->
    <act_window id="action_rb_delivery_area_map" name="Area Map" res_model="rb_delivery.area_map" view_mode="tree,kanban"/>
    <menuitem id="menu_rb_delivery_area_map" name="Area Map" parent="menu_rb_delivery_area_config" sequence="15" action="action_rb_delivery_area_map" />

    <!-- Country -->
    <act_window id="action_rb_delivery_country" name="Country" res_model="rb_delivery.country" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_country" name="Country" parent="menu_rb_delivery_area_config" sequence="15" action="action_rb_delivery_country" />

    <!-- Business Work Category -->
    <act_window id="action_rb_delivery_business_work_category" name="Business Work Category" res_model="rb_delivery.business_work_category" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_business_work_category" name="Business Work Category" parent="menu_rb_delivery_configuration" sequence="16" action="action_rb_delivery_business_work_category" />

    <act_window id="action_rb_delivery_quick_access_buttons" name="Quick Actions" res_model="rb_delivery.quick_access_buttons" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_quick_access_buttons" name="Quick Actions" parent="menu_rb_delivery_configuration" sequence="20" action="action_rb_delivery_quick_access_buttons"/>

    <!-- User -->
    <act_window id="action_rb_delivery_user_select_confirm" name="Confirm Users" groups="role_super_manager,base.group_system" src_model="rb_delivery.user" res_model="rb_delivery.select_confirm" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_user_select_reconfirm" name="Reconfirm Users" groups="role_super_manager,base.group_system" src_model="rb_delivery.user" res_model="rb_delivery.select_reconfirm" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_user_select_archive" name="Archive Users" groups="role_super_manager,base.group_system" src_model="rb_delivery.user" res_model="rb_delivery.select_archive" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_user_select_unarchive" name="Unarchive Users" groups="role_super_manager,base.group_system" src_model="rb_delivery.user" res_model="rb_delivery.select_unarchive" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_user_select_pricelist" name="Select Pricelist" groups="role_pricelist_manager,base.group_system" src_model="rb_delivery.user" res_model="rb_delivery.select_pricelist" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_user_select_previous_pricelist" name="Select Previous Pricelist" groups="role_pricelist_manager,base.group_system" src_model="rb_delivery.user" res_model="rb_delivery.select_previous_pricelist" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_notify_users" name="Notify users" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system" src_model="rb_delivery.user" res_model="rb_delivery.notify_users" view_mode="form" target="new" multi="True" />

    <!-- Order Configuration -->
    <menuitem id="menu_rb_delivery_order_history_configuration" name="Order History" parent="menu_rb_delivery_configuration" sequence="16"/>


    <!-- Zone  -->
    <record model="ir.actions.act_window" id="action_area_zone">
      <field name="name">Zone</field>
      <field name="res_model">rb_delivery.area_zone</field>
      <field name="view_mode">tree,form</field>
      <field name="context">{}</field>
    </record>

    <menuitem id="menu_area_zone" name="Zone" parent="rb_delivery.menu_rb_delivery_area_config" sequence="15" action="action_area_zone" />

    <!-- Billing -->
    <menuitem id="menu_rb_delivery_rubik_billing_menu" name="Billing" sequence="15" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager,base.group_system"/>

    <!-- Billing Settings -->
    <act_window id="action_rb_delivery_billing_setting" name="Billing Setting" res_model="rb_delivery.billing_setting" view_mode="tree"/>
    <menuitem id="menu_rb_delivery_billing_setting" name="Billing Setting" parent="menu_rb_delivery_rubik_billing_menu" sequence="15" action="action_rb_delivery_billing_setting" groups="base.group_system"/>

    <!-- Billing Settings -->
    <act_window id="action_rb_delivery_billing" name="Billing" res_model="rb_delivery.billing" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_billing" name="Billing" parent="menu_rb_delivery_rubik_billing_menu" sequence="15" action="action_rb_delivery_billing" groups="role_super_manager,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_accounting,base.group_system"/>

    <!-- Register template -->
    <record model="ir.actions.act_window" id="action_rb_delivery_email_template">
      <field name="name">Register Email Template</field>
      <field name="res_model">mail.template</field>
      <field name="view_mode">tree,form</field>
      <field name="limit" eval="1"/>
      <field name="create">false</field>
      <field name="domain">[('model_id','=','rb_delivery.action')]</field>
    </record>
      <menuitem id="menu_rb_delivery_email_template" name="Register Email Template" parent="rb_delivery_communication_integration" sequence="2" action="action_rb_delivery_email_template" />

    <!-- reject reason -->
    <act_window id="action_rb_delivery_reject_reason" name="Reject reasons" res_model="rb_delivery.reject_reason" view_mode="tree" view_type="form"/>
    <menuitem id="menu_rb_delivery_reject_reason" name="Reject reasons" parent="menu_rb_delivery_configuration" sequence="15" action="action_rb_delivery_reject_reason" />

    <!-- payment method -->
    <act_window id="action_rb_delivery_payment_type" name="Payment types" res_model="rb_delivery.payment_type" view_mode="tree,form" view_type="form"/>
    <menuitem id="menu_rb_delivery_payment_type" name="Payment types" parent="menu_rb_delivery_configuration" sequence="15" action="action_rb_delivery_payment_type" />

    <!-- description tags -->
    <act_window id="action_rb_delivery_description_tags" name="Description Tags" res_model="rb_delivery.description_tags" view_mode="tree" view_type="form"/>
    <menuitem id="menu_rb_delivery_description_tags" name="Description Tags" parent="menu_rb_delivery_configuration" sequence="15" action="action_rb_delivery_description_tags" />

    <!-- Whatsapp Message -->
    <act_window id="action_rb_delivery_whatsapp_message" name="Messages" res_model="rb_delivery.whatsapp_message" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_whatsapp_message" name="Messages" parent="rb_delivery_communication_integration" sequence="3" action="action_rb_delivery_whatsapp_message"/>

   <act_window id="action_company_user" name="Company" res_model="res.company" view_mode="tree,form"/>
   <menuitem id="menu_company_user" name="Company" parent="rb_delivery_company_configurations" sequence="15" action="action_company_user"  groups="role_super_manager,base.group_system"/>


    <!-- All Orders -->
    <record model="ir.actions.act_window" id="action_rb_delivery_all_orders_report">
      <field name="name">All Orders</field>
      <field name="res_model">rb_delivery.order</field>
      <field name="view_id" ref="view_tree_rb_delivery_order"></field>
      <field name="view_mode">tree,form,graph</field>
    </record>

    <menuitem id="menu_rb_delivery_all_orders_report" name="All Orders" parent="menu_rb_delivery_orders" sequence="30" action="action_rb_delivery_all_orders_report"/>

        <!-- Follow up Orders -->
    <record model="ir.actions.act_window" id="action_rb_delivery_follow_up_orders_reports">
      <field name="name">Follow up orders</field>
      <field name="res_model">rb_delivery.follow_up_order</field>
      <field name="view_id" ref="view_tree_rb_delivery_follow_up_order"></field>
      <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_rb_delivery_follow_up_orders" name="Follow up orders" parent="menu_rb_delivery_orders" sequence="30" action="action_rb_delivery_follow_up_orders_reports" groups="base.group_system,role_super_manager,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager"/>



    <!-- Onboarding errors -->
    <act_window id="action_rb_delivery_onboarding_error" name="Onboarding errors" res_model="rb_delivery.onboarding_error" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_onboarding_error"  name="Onboarding errors" parent="menu_rb_delivery_report" sequence="33" action="action_rb_delivery_onboarding_error" groups="base.group_system,role_super_manager,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager"/>

    <!-- mobile_scan_logs -->
    <act_window id="action_rb_delivery_mobile_scan_logs" name="Mobile Scan Logs" res_model="rb_delivery.mobile_scan_logs" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_scan_logs"  name="Mobile Scan Logs" parent="menu_rb_delivery_report" sequence="34" action="action_rb_delivery_mobile_scan_logs" groups="base.group_system,role_super_manager,role_manager"/>

    <!-- scan_logs -->
    <act_window id="action_rb_delivery_scan_logs" name="Scan Logs" res_model="rb_delivery.scan_logs" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_scan_logs"  name="Scan Logs" parent="menu_rb_delivery_report" sequence="34" action="action_rb_delivery_scan_logs" groups="base.group_system,role_super_manager,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager"/>




    <!-- Delayed Orders Report-->
    <record model="ir.actions.act_window" id="action_rb_delivery_delayed_report">
      <field name="name">Delayed Orders</field>
      <field name="res_model">rb_delivery.order</field>
      <field name="view_id" ref="view_tree_rb_delivery_order"></field>
      <field name="view_mode">tree,form</field>
      <field name="context">{'group_by':['write_date:day']}</field>
      <field name="domain">['|',('state','=','in_progress'),('state','=','in_branch'),('write_date','&lt;=',(datetime.date.today()-relativedelta(days=3)).strftime('%Y-%m-%d'))]</field>
    </record>

    <menuitem id="menu_rb_delivery_delayed_report" name="Delayed Orders" parent="menu_rb_delivery_report" sequence="30" action="action_rb_delivery_delayed_report" groups="base.group_system,role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_sales,role_super_manager"/>

    <!-- Company Configuration-->
    <menuitem id="menu_rb_delivery_extrconf" name="Olivery Configuration" groups="base.group_system,rb_delivery.role_configuration_manager" sequence="15"/> 

    <!-- Error logs -->
    <act_window id="action_rb_delivery_error_log" name="Error logs" res_model="rb_delivery.error_log" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_error_log" name="Error log" parent="menu_rb_delivery_extrconf" sequence="15" action="action_rb_delivery_error_log" />

    <!-- Onesignal -->
    <act_window id="action_rb_delivery_one_signal" name="One Signal" res_model="rb_delivery.one_signal" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_one_signal" name="One Signal" parent="menu_rb_delivery_extrconf" sequence="15" action="action_rb_delivery_one_signal" />

    <!-- Security -->
    <menuitem id="menu_rb_delivery_security_top_menu" name="Security" parent="menu_rb_delivery_extrconf" sequence="15" />

    <record model="ir.actions.act_window" id="action_rules_order">
      <field name="name">Rules</field>
      <field name="res_model">ir.rule</field>
      <field name="view_mode">tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain">[('model_id','=','rb_delivery.order')]</field>
    </record>
   <menuitem id="menu_rules_order" name="Rules" parent="menu_rb_delivery_security_top_menu" sequence="15" action="action_rules_order"  groups="base.group_system"/>

   <record model="ir.actions.act_window" id="action_menu_items">
      <field name="name">Menu Items</field>
      <field name="res_model">ir.ui.menu</field>
      <field name="view_mode">tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain">[('parent_id','ilike','Olivery')]</field>
    </record>
   <menuitem id="menu_menu_items" name="Menu Items" parent="menu_rb_delivery_security_top_menu" sequence="15" action="action_menu_items"  groups="base.group_system,rb_delivery.role_configuration_manager"/>

   <act_window id="action_rb_delivery_status" name="Status" res_model="rb_delivery.status" view_mode="tree,form"/>
   <menuitem id="menu_rb_delivery_status" name="Status" parent="menu_rb_delivery_security_top_menu" sequence="10" action="action_rb_delivery_status" />

   <record model="ir.actions.act_window" id="action_olivery_order_status">
      <field name="name">Olivery Order Status</field>
      <field name="res_model">rb_delivery.status</field>
      <field name="view_mode">tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain">['|',('status_type','=',False),('status_type','=','olivery_order')]</field>
    </record>
   <menuitem id="menu_olivery_order_status" name="Olivery Order Status" parent="menu_rb_delivery_security_top_menu" sequence="15" action="action_olivery_order_status"  groups="base.group_system,rb_delivery.role_configuration_manager"/>

   <record model="ir.actions.act_window" id="action_olivery_collection_status">
      <field name="name">Olivery Collection Status</field>
      <field name="res_model">rb_delivery.status</field>
      <field name="view_mode">tree,form,graph</field>
      <field name="context">{'group_by': ['collection_type']}</field>
      <field name="limit" eval="150"/>
      <field name="domain">[('status_type','=','olivery_collection')]</field>
    </record>
   <menuitem id="menu_olivery_collection_status" name="Olivery Collection Status" parent="menu_rb_delivery_security_top_menu" sequence="15" action="action_olivery_collection_status"  groups="base.group_system,rb_delivery.role_configuration_manager"/>

   <act_window id="action_rb_delivery_status_field_security" name="Status Field Security" res_model="rb_delivery.status_field_security" view_mode="tree,form"/>
   <menuitem id="menu_rb_delivery_status_field_security" name="Status Field Security" parent="menu_rb_delivery_security_top_menu" sequence="20" action="action_rb_delivery_status_field_security" />

    <!-- Client Configurations -->
    <menuitem id="menu_rb_delivery_client_configuration_top_menu" name="Client Configuration" parent="menu_rb_delivery_extrconf" sequence="15" />

    <act_window id="action_rb_delivery_client_configuration" context="{'group_by': ['configuration_type_id']}" name="Client Configuration" res_model="rb_delivery.client_configuration" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_client_configuration" name="Client Configuration" parent="menu_rb_delivery_client_configuration_top_menu" sequence="1" action="action_rb_delivery_client_configuration" />

    <!-- Client Configurations type-->
    <act_window id="action_rb_delivery_client_configuration_type" name="Client Configuration Type" res_model="rb_delivery.client_configuration_type" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_client_configuration_type" name="Client Configuration Type" parent="menu_rb_delivery_client_configuration_top_menu" sequence="2" action="action_rb_delivery_client_configuration_type" />

    <!-- Mobile configuration -->
    <menuitem id="menu_rb_delivery_mobile_configuration_top_menu" name="Mobile Configuration" parent="menu_rb_delivery_extrconf" sequence="15" />


    <!-- Mobile Nav Creator -->
    <act_window id="action_rb_delivery_mobile_nav_creator"  name="Mobile Nav Creator" res_model="rb_delivery.mobile_nav_creator" view_mode="tree,form" />
    <menuitem id="menu_rb_delivery_mobile_nav_creator" name="Mobile Nav Creator" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="2" action="action_rb_delivery_mobile_nav_creator" />


    <!-- Mobile Form Creator -->
    <act_window id="action_rb_delivery_mobile_form_creator" context="{'group_by': ['group_id']}"  name="Mobile Form Creator" res_model="rb_delivery.mobile_form_creator" view_mode="tree,form" domain="[('form_name', '!=', 'quick_order_form')]"/>
    <menuitem id="menu_rb_delivery_mobile_form_creator" name="Mobile Form Creator" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="2" action="action_rb_delivery_mobile_form_creator" />

    <!-- Mobile Card Creator -->
    <act_window id="action_rb_delivery_mobile_card_creator" context="{'group_by': ['group_id']}" name="Mobile Card Creator" res_model="rb_delivery.mobile_card_creator" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_card_creator" name="Mobile Card Creator" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="2" action="action_rb_delivery_mobile_card_creator" />


    <act_window id="action_rb_delivery_mobile_sort_and_distribute" context="{'group_by': ['group_id']}" name="Mobile Sort and distribute" res_model="rb_delivery.mobile_sort_and_distribute" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_sort_and_distribute" name="Mobile Sort and distribute" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="2" action="action_rb_delivery_mobile_sort_and_distribute" />


    <act_window id="action_rb_delivery_icons_model" name="Olivery Icons" res_model="rb_delivery.icons" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_icons" name="Olivery Icons" parent="menu_rb_delivery_extrconf" sequence="15" action="action_rb_delivery_icons_model" />

    <!-- Quick Order Form Creator -->
    <record id="action_rb_delivery_quick_order_form_creator" model="ir.actions.act_window">
      <field name="name">Quick Order Form Creator</field>
      <field name="res_model">rb_delivery.mobile_form_creator</field>
      <field name="view_mode">tree,form</field>
      <field name="domain">[('form_name', '=', 'quick_order_form')]</field> <!-- Replace field_name and value with your specific criteria -->
  </record>

  <menuitem id="menu_rb_delivery_quick_order_form_creator"
            name="Quick Order Form Creator"
            parent="menu_rb_delivery_extrconf"
            sequence="15"
            action="action_rb_delivery_quick_order_form_creator"/>

    <!-- Mobile Form Inputs -->
    <act_window id="action_rb_delivery_mobile_form_inputs" name="Mobile Form Inputs" res_model="rb_delivery.mobile_form_input" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_form_inputs" name="Mobile Form Inputs" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="10" action="action_rb_delivery_mobile_form_inputs" />

    <!-- Mobile Card Items -->
    <act_window id="action_rb_delivery_mobile_card_items" name="Mobile Card Items" res_model="rb_delivery.mobile_card_item" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_card_items" name="Mobile Card Items" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="10" action="action_rb_delivery_mobile_card_items" />

    <!-- Mobile Card Model Functions -->
    <act_window id="action_rb_delivery_mobile_card_model_functions" name="Mobile Card Model Functions" res_model="rb_delivery.card_model_functions" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_card_model_functions" name="Mobile Card Model Functions" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="10" action="action_rb_delivery_mobile_card_model_functions" />

    <!-- Mobile Action Creator -->
    <act_window id="action_rb_delivery_mobile_action_creator" context="{'group_by': ['group_id']}"  name="Mobile Action Creator" res_model="rb_delivery.mobile_action_creator" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_action_creator" name="Mobile Action Creator" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="7" action="action_rb_delivery_mobile_action_creator" />

    <!-- Mobile setting Creator -->
    <act_window id="action_rb_delivery_mobile_setting_creator" context="{'group_by': ['group_id']}"  name="Mobile Setting Creator" res_model="rb_delivery.mobile_setting_creator" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_setting_creator" name="Mobile Setting Creator" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="8" action="action_rb_delivery_mobile_setting_creator" />

    <!-- Mobile setting Creator -->
    <act_window id="action_rb_delivery_mobile_setting_item" name="Mobile Setting Item" res_model="rb_delivery.mobile_setting_item" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_setting_item" name="Mobile Setting Item" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="9" action="action_rb_delivery_mobile_setting_item" />

    <!-- Mobile Compute Field -->
    <act_window id="action_rb_delivery_mobile_compute_functions" name="Mobile Compute Fields Function" res_model="rb_delivery.mobile_compute_functions" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_compute_functions" name="Mobile Compute Fields Function" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="10" action="action_rb_delivery_mobile_compute_functions" />

    <!-- Mobile default Search -->
    <act_window id="action_rb_delivery_mobile_default_search" name="Mobile Default Search" res_model="rb_delivery.mobile_default_search" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_default_search" name="Mobile Default Search" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="3" action="action_rb_delivery_mobile_default_search" />

    <!-- Mobile default Print -->
    <act_window id="action_rb_delivery_mobile_default_print" name="Mobile Default Print" res_model="rb_delivery.mobile_default_print" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_default_print" name="Mobile Default Print" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="3" action="action_rb_delivery_mobile_default_print" />

    <!-- Mobile filter  -->
    <act_window id="action_rb_delivery_mobile_filter_fields" name="Mobile Filter Fields" res_model="rb_delivery.mobile_filter_fields" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_filter_fields" name="Mobile Filter Fields" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="4" action="action_rb_delivery_mobile_filter_fields" />

    <!-- Mobile Models Filter -->
    <act_window id="action_rb_delivery_mobile_models_filter" name="Mobile Models Filter" res_model="rb_delivery.mobile_models_filter" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_models_filter" name="Mobile Models Filter" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="11" action="action_rb_delivery_mobile_models_filter" />

    <!-- Create Order Button Creator-->
    <act_window id="action_rb_delivery_create_order_button_creator" context="{'group_by': ['group_id']}" name="Create Order Button Creator" res_model="rb_delivery.create_order_button_creator" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_create_order_button_creator" name="Create Order Button Creator" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="11" action="action_rb_delivery_create_order_button_creator" />

    <!-- Create Order Button -->
    <act_window id="action_rb_delivery_create_order_button_action" name="Create Order Button Actions" res_model="rb_delivery.create_order_button_action" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_create_order_button_action" name="Create Order Button Actions" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="12" action="action_rb_delivery_create_order_button_action" />

    <!-- Collection sheet creator-->
    <act_window id="action_rb_delivery_mobile_collection_sheet_creator" context="{'group_by': ['group_id']}"   name="Collection Sheet Creator" res_model="rb_delivery.mobile_collection_sheet_creator" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_collection_sheet_creator" name="Collection Sheet Creator" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="13" action="action_rb_delivery_mobile_collection_sheet_creator" />

    <!-- Collection Sheet Item -->
    <act_window id="action_rb_delivery_mobile_collection_item" name="Collection Sheet Item" res_model="rb_delivery.mobile_collection_item" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_mobile_collection_item" name="Collection Sheet Item" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="14" action="action_rb_delivery_mobile_collection_item" />

    <!-- Status Menu configuration -->
    <menuitem id="menu_rb_delivery_status_configuration" name="Status" parent="menu_rb_delivery_extrconf" sequence="16"/>

    <act_window id="action_rb_delivery_status_action" name="Status Action" res_model="rb_delivery.status_action" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_status_action" name="Status Action" parent="menu_rb_delivery_status_configuration" sequence="20" action="action_rb_delivery_status_action" />

    <act_window id="action_rb_delivery_order_action" name="Order Action" res_model="rb_delivery.order_action" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_order_action" name="Order Action" parent="menu_rb_delivery_status_configuration" sequence="20" action="action_rb_delivery_order_action" />

    <act_window id="action_rb_delivery_order_logs" name="Order Logs" res_model="rb_delivery.order_logs" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_order_logs" name="Order Logs" parent="menu_rb_delivery_report" sequence="20" action="action_rb_delivery_order_logs" />

    <record id="action_rb_delivery_order_cost_logs" model="ir.actions.act_window">
      <field name="name">Order Cost Logs</field>
      <field name="res_model">rb_delivery.order_logs</field>
      <field name="view_mode">tree,form</field>
      <field name="domain" eval="[('field_id','in',[ref('rb_delivery.field_rb_delivery_order__cost'), ref('rb_delivery.field_rb_delivery_order__copy_total_cost'), ref('rb_delivery.field_rb_delivery_order__customer_payment'), ref('rb_delivery.field_rb_delivery_order__extra_cost')])]"/>
    </record>
    <menuitem id="menu_rb_delivery_order_cost_logs" name="Order Cost Logs" parent="menu_rb_delivery_report" sequence="20" action="action_rb_delivery_order_cost_logs" />

    <act_window id="action_rb_delivery_status_related_field" name="Status Related Fields" res_model="rb_delivery.status_related_field" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_status_related_field" name="Status Related Fields" parent="menu_rb_delivery_status_configuration" sequence="20" action="action_rb_delivery_status_related_field" />

    <act_window id="action_rb_delivery_status_pre_action" name="Pre Status Action" res_model="rb_delivery.status_pre_action" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_status_pre_action" name="Status Pre Action" parent="menu_rb_delivery_status_configuration" sequence="20" action="action_rb_delivery_status_pre_action" />

    <act_window id="action_rb_delivery_status_mobile_action" name="Status Mobile Action" res_model="rb_delivery.status_mobile_action" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_status_mobile_action" name="Status Mobile Action" parent="menu_rb_delivery_status_configuration" sequence="20" action="action_rb_delivery_status_mobile_action" />

    <!-- general Configuration -->
    <act_window id="action_rb_delivery_general_configuration" name="Company General Configuration" res_model="rb_delivery.general_configuration" view_mode="tree,form" view_type="form"/>
    <menuitem id="menu_rb_delivery_general_configuration" name="Company General Configuration" parent="rb_delivery_company_configurations" sequence="15" action="action_rb_delivery_general_configuration" />


    <act_window id="action_rb_delivery_otp_checker" name="OTP Checker configurations" res_model="rb_delivery.otp_status_checker" view_mode="tree,form" view_type="form"/>
    <menuitem id="menu_rb_delivery_otp_checker" name="OTP Checker configurations" parent="rb_delivery_settings" sequence="15" action="action_rb_delivery_otp_checker" />

    <act_window id="action_rb_delivery_security_matrix" name="Authority Matrix" res_model="rb_delivery.security_matrix" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_security_matrix" name="Authority Matrix" parent="rb_delivery_settings" sequence="20" action="action_rb_delivery_security_matrix" />

    <menuitem id="menu_rb_delivery_rb_delivery_delayed_orders_monitor" name="Delayed Orders Monitor" parent="rb_delivery_settings" sequence="23" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system"  />

    <act_window id="action_rb_delivery_delayed_orders_timer_monitor" name="Delayed Orders Monitor" res_model="rb_delivery.delayed_orders_timer_monitor" view_mode="tree,form" />
    <menuitem id="menu_rb_delivery_rb_delivery_delayed_orders_timer_monitor" name="Delayed Orders Monitor" parent="menu_rb_delivery_rb_delivery_delayed_orders_monitor" sequence="1" action="action_rb_delivery_delayed_orders_timer_monitor" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system"/>

    <act_window id="action_rb_delivery_delayed_orders_logs" name="Delayed Orders Logs" res_model="rb_delivery.delayed_orders_logs" view_mode="tree,form" />
    <menuitem id="menu_rb_delivery_rb_delivery_delayed_orders_logs" name="Delayed Orders Logs" parent="menu_rb_delivery_rb_delivery_delayed_orders_monitor" sequence="2" action="action_rb_delivery_delayed_orders_logs" groups="role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,role_super_manager,base.group_system"/>
    <!-- Website Menues-->
    <record id="order_tracking_menu" model="website.menu">
      <field name="name">Tracking Order</field>
      <field name="url">/order_tracking</field>
      <field name="parent_id" ref="website.main_menu" />
      <field name="sequence" type="int">5</field>
    </record>

    <record id="olivery_sign_up_form_menu" model="website.menu">
      <field name="name">Sign up</field>
      <field name="url">/olivery/sign_up/form</field>
      <field name="parent_id" ref="website.main_menu" />
      <field name="sequence" type="int">5</field>
    </record>


     <!-- Order Detail -->
     <act_window id="action_rb_delivery_mobile_order_detail" context="{'group_by': ['group_id']}" name="Mobile Order Detail" res_model="rb_delivery.mobile_order_detail" view_mode="tree,form"/>

     <menuitem id="menu_rb_delivery_mobile_order_detail" name="Mobile Order Detail" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="2" action="action_rb_delivery_mobile_order_detail" />

     <act_window id="action_rb_delivery_mobile_order_detail_button" name="Mobile Order Detail" res_model="rb_delivery.mobile_order_detail_button" view_mode="tree,form"/>

     <menuitem id="menu_rb_delivery_mobile_order_detail_button" name="Mobile Order Detail buttons" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="2" action="action_rb_delivery_mobile_order_detail_button" />

     <act_window id="action_rb_delivery_mobile_order_detail_card" name="Mobile Order Detail" res_model="rb_delivery.mobile_order_detail_card" view_mode="tree,form"/>

     <menuitem id="menu_rb_delivery_mobile_order_detail_card" name="Mobile Order Detail cards" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="2" action="action_rb_delivery_mobile_order_detail_card" />

     <act_window id="action_rb_delivery_mobile_order_detail_card_item" name="Mobile Order Detail" res_model="rb_delivery.mobile_order_detail_card_item" view_mode="tree,form"/>
     
     <menuitem id="menu_rb_delivery_mobile_order_detail_card_item" name="Mobile Order Detail card items" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="2" action="action_rb_delivery_mobile_order_detail_card_item" />

        <!-- Olivery Apps -->
    <record model="ir.actions.act_window" id="action_rb_delivery_app_market">
      <field name="name">Olivery Apps</field>
      <field name="res_model">ir.module.module</field>
      <field name="view_mode">kanban,tree,form</field>
      <field name="limit" eval="150"/>
      <field name="domain">['|','|',('shortdesc','ilike','olivery'),('shortdesc','ilike','rb_delivery'),('shortdesc','ilike','storex')]</field>
    </record>
    <menuitem id="menu_rb_delivery_app_market" name="Olivery Apps" sequence="15" action="action_rb_delivery_app_market" groups="base.group_system,role_configuration_manager"/>

    <!-- District -->

    <act_window id="action_rb_delivery_district" name="District" res_model="rb_delivery.district" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_district" name="District" parent="menu_rb_delivery_area_config" sequence="15" action="action_rb_delivery_district" />

    <act_window id="action_rb_delivery_control_fields" name="Control Fields" res_model="rb_delivery.control_fields" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_control_fields" name="Control Fields" parent="rb_delivery.menu_rb_delivery_extrconf" sequence="15" action="action_rb_delivery_control_fields" />

    <record model="ir.actions.act_window" id="action_rb_delivery_migrations">
      <field name="name">Migrations</field>
      <field name="res_model">rb_delivery.migration</field>
      <field name="view_mode">tree,form</field>
      <field name="limit" eval="150"/>
    </record>
    <menuitem id="menu_rb_delivery_migrations" name="Migrations" parent="menu_rb_delivery_extrconf" sequence="4" action="action_rb_delivery_migrations" />

    <record id="action_recomputation_job" model="ir.actions.act_window">
        <field name="name">Recomputation Jobs</field>
        <field name="res_model">rb_delivery.recomputation_job</field>
        <field name="view_mode">tree,form</field>
    </record>
    
    <menuitem id="menu_recomputation_job_root" name="Recomputation Jobs" parent="base.menu_administration"/>
    <menuitem id="menu_recomputation_job" parent="menu_recomputation_job_root" name="Jobs" action="action_recomputation_job"/>



    <act_window id="action_rb_delivery_archive_order" name="Archive Order" res_model="rb_delivery.archive_order" view_mode="tree" view_type="form"/>
    <menuitem id="menu_rb_delivery_archive_order" name="Archive Order" parent="rb_delivery.menu_rb_delivery_report" sequence="15" action="action_rb_delivery_archive_order" />


    <act_window id="action_rb_delivery_group_by_card_configurations" name="Mobile Group by card" res_model="rb_delivery.group_by_card_configurations" view_mode="tree"/>
    <menuitem id="menu_rb_delivery_group_by_card_configurations" name="Mobile Group by card" parent="menu_rb_delivery_mobile_configuration_top_menu" sequence="2" action="action_rb_delivery_group_by_card_configurations" />
    
    <act_window id="action_rb_delivery_zone_assignment" name="Zone Assignment Tool" res_model="rb_delivery.zone_assignment" view_mode="kanban"/>
    <menuitem id="menu_rb_delivery_zone_assignment" name="Zone Assignment Tool" parent="rb_delivery.menu_rb_delivery_report" sequence="50" action="action_rb_delivery_zone_assignment" groups="base.group_system,role_super_manager,role_manager"/>
    

  </data>

  <data noupdate="1">
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.actions.act_window'), ('name', '=', 'action_rb_delivery_order_returned_money_collection_detach_orders')]"/>
        </function>
        <value eval="{'noupdate': False}" />
    </function>
     <record id="action_rb_delivery_order_returned_money_collection_detach_orders" model="ir.actions.act_window">
            <field name="groups_id" eval="[(6,0,[ref('rb_delivery.role_returned_collection_manager')])]" />
        </record>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.actions.act_window'), ('name', '=', 'action_rb_delivery_order_returned_money_collection_detach_orders')]"/>
        </function>
        <value eval="{'noupdate': True}" />
    </function>
  </data>
<data noupdate="1">
  <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.actions.act_window'), ('name', '=', 'action_area_zone')]"/>
        </function>
        <value eval="{'noupdate': False}" />
    </function>
     <record id="action_area_zone" model="ir.actions.act_window">
            <field name="context">{}</field>
        </record>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.actions.act_window'), ('name', '=', 'action_area_zone')]"/>
        </function>
        <value eval="{'noupdate': True}" />
    </function>
  </data>
  <data noupdate="1">
  <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.actions.act_window'), ('name', '=', 'action_rb_delivery_business_executive_report')]"/>
        </function>
        <value eval="{'noupdate': False}" />
    </function>
     <record id="action_rb_delivery_business_executive_report" model="ir.actions.act_window">
            <field name="domain">[('field_id.name', '=', 'state_id'),('business_id', '!=', False)]</field>
        </record>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.actions.act_window'), ('name', '=', 'action_rb_delivery_business_executive_report')]"/>
        </function>
        <value eval="{'noupdate': True}" />
    </function>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.actions.act_window'), ('name', '=', 'action_rb_delivery_driver_executive_report')]"/>
        </function>
        <value eval="{'noupdate': False}" />
    </function>
     <record id="action_rb_delivery_driver_executive_report" model="ir.actions.act_window">
            <field name="domain">[('field_id.name', '=', 'state_id'),('driver_id', '!=', False)]</field>
        </record>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.actions.act_window'), ('name', '=', 'action_rb_delivery_driver_executive_report')]"/>
        </function>
        <value eval="{'noupdate': True}" />
    </function>
  </data>
</odoo>
