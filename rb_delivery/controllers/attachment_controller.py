# -*- coding: utf-8 -*-
from openerp import http, _
from openerp.http import request
import base64
import mimetypes
import logging
import os

_logger = logging.getLogger(__name__)

class attachment_controller(http.Controller):

    @http.route('/rb_delivery/attachment/download/<int:attachment_id>', type='http', auth='user')
    def download_attachment(self, attachment_id, **_):
        try:
            attachment = request.env['rb_delivery.order_attachment'].browse(attachment_id)

            if not attachment.exists():
                return request.not_found()

            attachment_data = attachment.attachment
            if not attachment_data:
                return request.not_found()

            file_data = base64.b64decode(attachment_data)

            file_name = attachment.name or f"attachment_{attachment.id}"

            file_extension = attachment.file_type or self._get_file_extension(file_data)
            if file_extension and '.' not in file_name:
                file_name = f"{file_name}.{file_extension}"

            content_type = self._get_content_type(file_name)

            return request.make_response(
                file_data,
                headers=[
                    ('Content-Type', content_type),
                    ('Content-Disposition', f'attachment; filename="{file_name}"'),
                    ('Content-Length', len(file_data)),
                ]
            )

        except Exception as e:
            _logger.error(f"Error downloading attachment: {str(e)}")
            return request.not_found()

    def _get_file_extension(self, file_data):
        if file_data.startswith(b'\xFF\xD8\xFF'):
            return 'jpg'
        elif file_data.startswith(b'\x89PNG\r\n\x1A\n'):
            return 'png'
        elif file_data.startswith(b'GIF87a') or file_data.startswith(b'GIF89a'):
            return 'gif'
        elif file_data.startswith(b'%PDF'):
            return 'pdf'
        elif file_data.startswith(b'PK\x03\x04'):
            return 'zip'

        return None

    def _get_content_type(self, file_name):
        content_type, _ = mimetypes.guess_type(file_name)
        if not content_type:
            content_type = 'application/octet-stream'

        return content_type
