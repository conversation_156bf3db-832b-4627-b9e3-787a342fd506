<odoo>
    <data noupdate="1">
   <record id="check_inclusive_delivery_action" model="rb_delivery.mobile_compute_functions">
        <field name="name">check_inclusive_delivery_business</field>
        <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
        <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__paid'),ref('rb_delivery.field_rb_delivery_order__assign_to_business')])]" />
   </record>
   <record id="check_address_tag_assigned" model="rb_delivery.mobile_compute_functions">
     <field name="name">check_address_tag_assigned</field>
     <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
     <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__address_tag')])]" />
   </record>
   <record id="compute_delivery_fee" model="rb_delivery.mobile_compute_functions">
    <field name="name">compute_delivery_fee</field>
    <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
    <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__customer_area'),ref('rb_delivery.field_rb_delivery_order__business_alt_area'),ref('rb_delivery.field_rb_delivery_order__show_alt_address'),ref('rb_delivery.field_rb_delivery_order__customer_sub_area'),ref('rb_delivery.field_rb_delivery_order__order_type_id'),ref('rb_delivery.field_rb_delivery_order__assign_to_business'),ref('rb_delivery.field_rb_delivery_order__extra_cost'),ref('rb_delivery.field_rb_delivery_order__discount')])]" />
   </record>
   <record id="compute_service_fee" model="rb_delivery.mobile_compute_functions">
    <field name="name">compute_service_fee</field>
    <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
    <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__service'),ref('rb_delivery.field_rb_delivery_order__default_service'),ref('rb_delivery.field_rb_delivery_order__extra_service_fee_on_sender'),ref('rb_delivery.field_rb_delivery_order__extra_service_fee_on_customer')])]" />
   </record>
   <record id="compute_money_collection_cost" model="rb_delivery.mobile_compute_functions">
      <field name="name">compute_money_collection_cost</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__assign_to_business'),ref('rb_delivery.field_rb_delivery_order__delivery_cost_on_sender'),ref('rb_delivery.field_rb_delivery_order__cost'),ref('rb_delivery.field_rb_delivery_order__delivery_cost'),ref('rb_delivery.field_rb_delivery_order__inclusive_delivery'),ref('rb_delivery.field_rb_delivery_order__copy_total_cost'),ref('rb_delivery.field_rb_delivery_order__customer_payment'),ref('rb_delivery.field_rb_delivery_order__paid'),ref('rb_delivery.field_rb_delivery_order__service'),ref('rb_delivery.field_rb_delivery_order__returned_discount'),ref('rb_delivery.field_rb_delivery_order__delivery_cost_on_customer'),ref('rb_delivery.field_rb_delivery_order__extra_service_fee_on_sender'),ref('rb_delivery.field_rb_delivery_order__extra_service_fee_on_customer')])]"/>
   </record>
   <record id="compute_required_from_business" model="rb_delivery.mobile_compute_functions">
      <field name="name">compute_required_from_business</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__paid'),ref('rb_delivery.field_rb_delivery_order__money_collection_cost'),ref('rb_delivery.field_rb_delivery_order__delivery_cost_on_sender'),ref('rb_delivery.field_rb_delivery_order__copy_total_cost'),ref('rb_delivery.field_rb_delivery_order__inclusive_delivery'),ref('rb_delivery.field_rb_delivery_order__copy_total_cost'),ref('rb_delivery.field_rb_delivery_order__inclusive_delivery'),ref('rb_delivery.field_rb_delivery_order__cost'),ref('rb_delivery.field_rb_delivery_order__delivery_cost'),ref('rb_delivery.field_rb_delivery_order__service'),ref('rb_delivery.field_rb_delivery_order__default_service'),ref('rb_delivery.field_rb_delivery_order__extra_service_fee_on_sender'),ref('rb_delivery.field_rb_delivery_order__extra_service_fee_on_customer') ])]"/>
   </record>
   <record id="compute_visibility_for_alternative_address" model="rb_delivery.mobile_compute_functions">
    <field name="name">compute_visibility_for_alternative_address</field>
    <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
    <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__show_alt_address')])]" />
   </record>
   <record id="compute_visibility_for_online_payment" model="rb_delivery.mobile_compute_functions">
      <field name="name">compute_visibility_for_online_payment</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__delivery_cost_on_sender')])]" />
   </record>
   <record id="compute_visibility_for_delivery_cost_on_customer_and_delivery_cost_on_sender" model="rb_delivery.mobile_compute_functions">
      <field name="name">compute_visibility_for_delivery_cost_on_customer_and_delivery_cost_on_sender</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__paid')])]" />
     </record>


     <record id="compute_receiver_is_business" model="rb_delivery.mobile_compute_functions">
         <field name="name">compute_receiver_is_business</field>
         <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
         <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__receiver_is_business')])]" />
     </record>

   <record id="compute_reference_id" model="rb_delivery.mobile_compute_functions">
      <field name="name">compute_reference_id</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__assign_to_business'),ref('rb_delivery.field_rb_delivery_order__reference_id')])]"/>
   </record>

   <record id="compute_check_if_has_sub_business" model="rb_delivery.mobile_compute_functions">
      <field name="name">compute_check_if_has_sub_business</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__assign_to_business')])]"/>
   </record>

   <record id="compute_default_order_type" model="rb_delivery.mobile_compute_functions">
      <field name="name">compute_default_order_type</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__assign_to_business')])]"/>
   </record>
   
   <record id="compute_show_if_business_inclusive" model="rb_delivery.mobile_compute_functions">
      <field name="name">show_if_business_inclusive</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__assign_to_business')])]"/>
   </record>
   <record id="compute_show_if_business_not_inclusive" model="rb_delivery.mobile_compute_functions">
      <field name="name">show_if_business_not_inclusive</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__assign_to_business')])]"/>
   </record>
   <record id="compute_partial_delivered_swtich_core" model="rb_delivery.mobile_compute_functions">
      <field name="name">compute_partial_delivered_swtich_core</field>
      <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
      <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__order_type_id')])]" />
  </record>
   </data>
</odoo>