# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

class rb_delivery_area(models.Model):

    _name = 'rb_delivery.area'
    _order = "sequence, name ASC"
    _inherit = 'mail.thread'
    _description = "Area Model"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    @api.one
    def _compute_has_sub_area(self):
        has_sub_area = False
        sub_areas = self.env['rb_delivery.sub_area'].search([('parent_id', '=', self.id)])
        if len(sub_areas) > 0 :
            has_sub_area = True
        self.has_sub_area = has_sub_area

    @api.one
    def compute_show_country(self):
        show_country = self.env['rb_delivery.client_configuration'].get_param('show_country')
        self.show_country = show_country

    def default_show_country(self):
        show_country = self.env['rb_delivery.client_configuration'].get_param('show_country')
        return show_country

    name = fields.Char('Name', required=True, translate=True,track_visibility="on_change")

    code = fields.Char('Code', required=False,track_visibility="on_change")

    arabic_name = fields.Char('Arabic Name', required=False,track_visibility="on_change")

    parent_id = fields.Many2one('rb_delivery.area', 'Area Group', ondelete='restrict',track_visibility="on_change")

    show_country = fields.Boolean('Show country',readonly=True,compute="compute_show_country", default=default_show_country)

    country_id = fields.Many2one('rb_delivery.country', 'Country', ondelete='restrict',track_visibility="on_change")

    show_in_create = fields.Boolean('Show in create order form', default=True,track_visibility="on_change")

    show_in_register = fields.Boolean('Show in register user form', default=True,track_visibility="on_change")

    drivers = fields.Many2many('rb_delivery.user',string='State Drivers', domain=_get_driver_users,track_visibility="on_change")

    sequence = fields.Integer(string="Sequence", track_visibility=False)

    has_sub_area = fields.Boolean('Has sub area', compute="_compute_has_sub_area",readonly=True)

    active = fields.Boolean('Active',default=True)

    is_default = fields.Boolean('Selected by default')

    english_name = fields.Char("English Name", track_visibility="on_change")

    zone_id = fields.Many2one(
        comodel_name='rb_delivery.area_zone',
        string='Zone',
        relation='rb_area_zone_area_rel',
        column1='area_id',
        column2='zone_id',
        track_visibility='on_change'
    )


    # ----------------------------------------------------------------------
    # Constraints
    # ----------------------------------------------------------------------

    _sql_constraints = [
        ('name', 'unique(name)', 'Name already exists!'),
        ('arabic_name', 'unique(arabic_name)', 'Arabic Name already exists!'),
        ('code', 'unique(code)', 'Code already exists!')
        ]

    def copy(self, default=None):
        default = dict(default or {})
        default.update(
            { 
                'name': self.name + '_copy',
                'code': self.code + '_copy' if self.code else "",
                'arabic_name' : self.arabic_name + '_copy' if self.arabic_name else ""
            }
        )

        return super(rb_delivery_area, self).copy(default)

    @api.model
    def create(self,values):
        if values.get('name'):
            values['name'] = values.get('name').strip()
            if self.env.user.lang == 'ar_SY':
                values['arabic_name'] = values['name']
        if 'code' not in values or ('code' in values and not values['code']):
            code = self.env['ir.sequence'].next_by_code('rb_delivery.area')
            values['code'] = code
        res =super(rb_delivery_area, self).create(values)
        if values.get('arabic_name') or values.get('name'):
            res.translate_name_value(values)
        return res

    @api.one
    def write(self,values):
        if values.get('name'):
            values['name'] = values.get('name').strip()
            if self.env.user.lang == 'ar_SY':
                values['arabic_name'] = values['name']
        old_zone_id = self.zone_id.id if self.zone_id else None
        new_zone_id = values.get('zone_id')
        res =super(rb_delivery_area, self).write(values)
        if values.get('arabic_name') or values.get('name'):
            self.translate_name_value(values)
        if old_zone_id != new_zone_id:
            domain = [
                ('customer_area', '=', self.id),
                '|', ('customer_sub_area', '=', False), ('customer_sub_area.zone_id', '=', False)
            ]
            self.env['rb_delivery.utility'].update_orders_zone(new_zone_id, domain)
        return res
    
    def translate_name_value(self,values):
        domain=[('id','!=',self.id)]
        if values.get('arabic_name'):
            if values.get('name'):
                domain.append('|')
            domain = domain+[('name', '=', values['arabic_name'])]
        if values.get('name'):
            domain = domain+[('arabic_name', '=', values['name'])]
        existing_areas = self.env['rb_delivery.area'].with_context(lang="en_US").search_read(domain,['name'],limit=1)
        if len(existing_areas):
            area_value = values.get('arabic_name',values.get('name')) if existing_areas[0]['name'] == values.get('arabic_name') else values.get('name',values.get('arabic_name'))
            self.env['rb_delivery.error_log'].raise_olivery_error(250,self.id,{'area_value':area_value,'name':_('arabic name') if area_value==values.get('arabic_name') else _('name')})
        if not values.get('arabic_name'):
            return
        area_name = self.name
        if values.get('name'):
            area_name = values['name']
        translated_title = self.env['ir.translation'].search([('name', '=', 'rb_delivery.area,name'),('res_id','=',self.id)],limit=1)
        if translated_title:
            translated_title.write({'value':values['arabic_name']})
        else:
            self.env['ir.translation'].create({'name':'rb_delivery.area,name','lang':'ar_SY','module':'rb_delivery','type':'model','res_id':self.id,'source':area_name,'value':values['arabic_name']})

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        if args is None:
            args = []
        if str(name).isnumeric():
            recs = self.search([('id', operator, name)] + args, limit=limit)
        else:
            recs = self.search([('name', operator, name)] + args, limit=limit)
        if not recs.ids:
            return super(rb_delivery_area, self).name_search(name=name, args=args,operator=operator,limit=limit)
        return recs.name_get()
