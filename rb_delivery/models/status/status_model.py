# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

class rb_delivery_status(models.Model):

    _name = 'rb_delivery.status'
    _order = "sequence ASC"
    _inherit = 'mail.thread'
    _description = "Status Model"

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    MODEL_COLLECTION_TYPE_MAP = {
        'rb_delivery.multi_print_orders_money_collector': 'collection',
        'rb_delivery.returned_money_collection': 'returned_collection',
        'rb_delivery.agent_money_collection': 'agent_collection',
        'rb_delivery.agent_returned_collection': 'agent_returned_collection',
        'rb_delivery.runsheet_collection': 'runsheet_collection',
    }

    MODEL_STATUS_TYPE_MAP = {
        'rb_delivery.order': 'olivery_order',
        'rb_delivery.multi_print_orders_money_collector': 'olivery_collection',
        'rb_delivery.returned_money_collection': 'olivery_collection',
        'rb_delivery.agent_money_collection': 'olivery_collection',
        'rb_delivery.agent_returned_collection': 'olivery_collection',
        'rb_delivery.runsheet_collection': 'olivery_collection',
    }

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]

    @api.model
    def default_order_type(self):
        order_type = self.env['rb_delivery.order_type'].search([('default','=',True)])
        if len(order_type) != 0:
            return order_type[0].id if order_type else None
        
    @api.model
    def get_model_status_type(self, model_name):
        return self.MODEL_STATUS_TYPE_MAP.get(model_name, 'olivery_order')
    
    @api.model
    def get_model_collection_type(self, model_name):
        return self.MODEL_COLLECTION_TYPE_MAP.get(model_name, 'olivery_collection')

    #inherit module [olivery_billing]
    #inherit module [olivery_connect_finance]
    #inherit module [olivery_pickup_request]
    #inherit module [olivery_branch_collection]
    @api.model
    def get_collection_type(self):
        collection_types = [('collection','Collection'),('returned_collection','Returned Collection'),('agent_collection','Agent Collection'),('agent_returned_collection','Agent Returned Collection'),('runsheet_collection','Runsheet Collection')]
        return collection_types

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    name = fields.Char('Name', required=True,track_visibility="on_change")

    title = fields.Char('Title',translate=True,track_visibility="on_change",required=True)

    title_ar = fields.Char('Title Arabic',track_visibility="on_change")

    default = fields.Boolean('Default',track_visibility="on_change")

    colour_code = fields.Char('Colour code',track_visibility="on_change")

    secondary_colour_code = fields.Char('Secondary Colour code',track_visibility="on_change")

    status_type = fields.Selection([('olivery_order','Olivery Order'),('storex_order','Storex Order'),('olivery_collection','Olivery Collection')], 'Status Type',default='olivery_order',track_visibility="on_change",copy=False)

    collection_type = fields.Selection(selection='get_collection_type', string='Collection Type',track_visibility="on_change",copy=False)

    related_order_status = fields.Char('Related order status',track_visibility="on_change",copy=False)

    description = fields.Char('Description',track_visibility="on_change")

    partner_status = fields.Char('Partner status',track_visibility="on_change")

    default_related_order = fields.Boolean('Default related order status',track_visibility="on_change")

    next_state_ids = fields.Many2many(comodel_name="rb_delivery.status", relation="state_next_state", column1="state", culomn2="next_state", string="Next States",track_visibility="on_change")

    lock_status = fields.Boolean('Lock Status',default=False,track_visibility="on_change")

    pass_lock_allowed_group_ids= fields.Many2many('res.groups','pass_lock_allowed_group_status_status', string="Pass Lock Allowed Group", domain=get_groups,track_visibility="on_change")

    group_ids = fields.Many2many('res.groups', string="Role", domain=get_groups,track_visibility="on_change")

    status_action_ids = fields.Many2many('rb_delivery.status_action',string="Status Actions",track_visibility="on_change")

    status_pre_action_ids = fields.Many2many('rb_delivery.status_pre_action',string="Status Pre Actions",track_visibility="on_change")

    status_action_on_create_ids = fields.Many2many('rb_delivery.status_action',relation = 'status_action_on_create_status_action',string="Status Actions On Create",track_visibility="on_change")

    status_mobile_action_ids = fields.Many2many('rb_delivery.status_mobile_action',string="Status Mobile Actions",track_visibility="on_change")

    required_status_mobile_action_ids = fields.Many2many('rb_delivery.status_mobile_action',relation = 'required_mobile_status_action',string="Mobile Required Actions",track_visibility="on_change")

    role_action_status_ids = fields.Many2many('res.groups','role_action_status_status', string="Role action status", domain=get_groups,track_visibility="on_change")

    role_segment_status_ids = fields.Many2many('res.groups', 'role_segment_status_status',string="Role segment status",domain=get_groups,track_visibility="on_change")

    role_can_not_edit_status_ids = fields.Many2many('res.groups', 'role_edit_status_status',string="Role can not edit orders in this status",domain=get_groups,track_visibility="on_change")

    sequence = fields.Integer(string="Sequence",track_visibility=False)

    order_type = fields.Many2one(
        'rb_delivery.order_type', string="Default Order Type", default=default_order_type,track_visibility="on_change")

    status_action_optional_related_fields = fields.Many2many('rb_delivery.status_related_field',relation = 'optional_fields_status_related_field',string="Optional Related Fields",track_visibility="on_change")

    status_action_required_aditional_fields = fields.Many2many('rb_delivery.status_related_field',relation = 'required_fields_status_related_field',string="Required Related Fields",track_visibility="on_change")

    form_ids = fields.Many2many(
        'rb_delivery.mobile_form_creator',
        'form_status_rel',
        'status_id',
        'form_id',
        string='Forms',
        domain=[('form_name','=','status_action_form')]
    )

    web_color = fields.Selection([
        ('danger','Red'),
        ('info','Blue'),
        ('muted','Gray'),
        ('primary','purple'),
        ('success','Green'),
        ('warning','Yellow')
    ],track_visibility="on_change")

    auto_filled_field_ids = fields.Many2many(
        comodel_name = 'rb_delivery.auto_filled_fields',
        string = 'Auto filled fields',
        relation = 'auto_filled_fields_status',
        column1 = 'auto_filled_field_id',
        column2 = 'status_id')


    active = fields.Boolean('Active', default=True,track_visibility="on_change")

    show_in_order_track = fields.Boolean('Show in order tracking')

    icon_name = fields.Char('Icon Name',track_visibility="on_change")

    business_status_name = fields.Char('Business Status',track_visibility="on_change",translate=True)

    business_status_color = fields.Char('Business Status Color',track_visibility="on_change")

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        if args is None:
            args = []
        recs = self.search(['|',('name', operator, name),('title', operator, name)] + args, limit=limit)

        if not recs.ids:
            return super(rb_delivery_status, self).name_search(name=name, args=args,operator=operator,limit=limit)
        return recs.name_get()

    @api.onchange('status_action_ids')
    def change_status_action_ids(self):
        if self.status_action_ids:
            for status_action in self.status_action_ids:
                if status_action.name == 'prevent_change_status_if_not_in_collection':
                    return {
                        'warning':{
                            'title': _('Warning'),
                            'message': _('Make sure to add status in configuration prevent_detach_order_from_collection'),
                        },
                    }
                elif status_action.name == 'prevent_change_status_if_not_in_agent_collection':
                    return {
                        'warning':{
                            'title': _('Warning'),
                            'message': _('Make sure to add status in configuration prevent_detach_order_from_agent_collection'),
                        },
                    }

    @api.one
    def write(self, values):
        if 'default' in values:
            status_type = values.get('status_type',self.status_type)
            domain = [('default','=',True),('status_type','=',status_type),('id','!=',self.id)]
            if status_type == 'olivery_collection':
                collection_type = values.get('collection_type',self.collection_type)
                domain.append(('collection_type','=',collection_type))
            default_statuses = self.env['rb_delivery.status'].search(domain)
            if len(default_statuses) != 0 and values['default'] == True:
                for default_status in default_statuses:
                    default_status.write({'default':False})
            
        if 'group_ids' in values and values['group_ids']:
            self.env['ir.rule'].clear_cache()
        if 'title_ar' in values and values['title_ar']:
            translated_title = self.env['ir.translation'].search([('name', '=', 'rb_delivery.status,title'),('res_id','=',self.id)])
            if translated_title:
                translated_title.write({'value':values['title_ar']})
            else:
                self.env['ir.translation'].create({'name':'rb_delivery.status,title','lang':'ar_SY','module':'rb_delivery','type':'model','res_id':self.id,'source':self.title,'value':values['title_ar']})

        if (values.get('name') and values['name'] != self.name ):
            status_domain = [('name','=',values['name'])]
            status_type = values.get('status_type') if values.get('status_type') else self.status_type
            if status_type:
                status_domain.append(('status_type','=',status_type))
            if status_type == 'olivery_collection':
                collection_type = values.get('collection_type') if values.get('collection_type') else self.collection_type
                if collection_type:
                    status_domain.append(('collection_type','=',collection_type))
            status_rec = self.env['rb_delivery.status'].search(status_domain)
            if status_rec:
                self.env['rb_delivery.error_log'].raise_olivery_error(462,self.id,{'status_name': values.get('name')})
        if ('name' in values and values['name'] and values['name'] != self.name ) or ('active' in values and not values['active']):
            if self.status_type == 'olivery_order' or self.status_type == False:
                orders = self.env['rb_delivery.order'].search([('state','=',self.name)])
                orders_type = self.env['rb_delivery.order_type'].sudo().search([('state','=',self.name)])
                if len(orders)>0:
                    self.env['rb_delivery.error_log'].raise_olivery_error(450,self.id,{'order_sequence': ', '.join([str(order.sequence) for order in orders]), 'status': self.name})
                if len(orders_type) > 0:
                    self.env['rb_delivery.error_log'].raise_olivery_error(339,self.id,{'orders_type': ', '.join([str(order_type.name) for order_type in orders_type]), 'status': self.name})
                    #raise ValidationError(_("You can't change the status name or deactivate status while it's used in orders"))
            elif self.status_type == 'olivery_collection':
                if self.collection_type == 'returned_collection':
                    collections = self.env['rb_delivery.returned_money_collection'].search([('state','=',self.name)])
                    if len(collections)>0:
                        self.env['rb_delivery.error_log'].raise_olivery_error(451,self.id,{})
                        #raise ValidationError(_("You can't change the status name or deactivate status while it's used in returned collections"))
                elif self.collection_type == 'collection':
                    collections = self.env['rb_delivery.multi_print_orders_money_collector'].search([('state','=',self.name)])
                    if len(collections)>0:
                        self.env['rb_delivery.error_log'].raise_olivery_error(452,self.id,{'collection_sequence': ', '.join([str(collection.sequence) for collection in collections]), 'status': self.name})
                        #raise ValidationError(_("You can't change the status name or deactivate status while it's used in collections"))
                elif self.collection_type == 'agent_collection':
                    collections = self.env['rb_delivery.agent_money_collection'].search([('state','=',self.name)])
                    if len(collections)>0:
                        self.env['rb_delivery.error_log'].raise_olivery_error(453,self.id,{})
                        #raise ValidationError(_("You can't change the status name or deactivate status while it's used in agent collections"))
                elif self.collection_type == 'agent_returned_collection':
                    collections = self.env['rb_delivery.agent_returned_collection'].search([('state','=',self.name)])
                    if len(collections)>0:
                        self.env['rb_delivery.error_log'].raise_olivery_error(454,self.id,{'collection_sequence': ', '.join([str(collection.sequence) for collection in collections])})
                        #raise ValidationError(_("You can't change the status name or deactivate status while it's used in agent returned collections"))

        self.log_changes(values)

        status = super(rb_delivery_status, self).write(values)
        if 'role_action_status_ids' in values or 'role_segment_status_ids' in values or 'group_ids' in values:
            self.check_allowed_roles()

        return status

    def log_changes(self, values):
        messages = []

        for field in values:
            field_definition = self._fields[field]
            model_name = field_definition.comodel_name
            field_label = field_definition.string

            if isinstance(values[field], list) and len(values[field]):
                if len(values[field][0]) > 2:
                    ids = values[field][0][2]

                    old_status_names = [status.name for status in self[field]]
                    new_status_names = self.get_status_names(model_name, ids)

                    message = f'{field_label} changed from {old_status_names or None} to {new_status_names or None} <br />'
                    messages.append(message)

        messages_str = ''.join(messages)
        if len(messages_str):
            self.message_post(body=messages_str)


    def get_status_names(self, model_name, ids):
        status_records = self.env[model_name].browse(ids)
        return [status.name for status in status_records]

    @api.model
    def create(self,values):
        if (values.get('name') and values['name'] != self.name ):
            status_domain = [('name','=',values['name'])]
            status_type = values.get('status_type') if values.get('status_type') else False
            if status_type:
                status_domain.append(('status_type','=',status_type))
            if status_type == 'olivery_collection':
                collection_type = values.get('collection_type') if values.get('collection_type') else False
                if collection_type:
                    status_domain.append(('collection_type','=',collection_type))
            status_rec = self.env['rb_delivery.status'].search(status_domain)
            if status_rec:
                self.env['rb_delivery.error_log'].raise_olivery_error(462,self.id,{'status_name': values.get('name')})
        
        if 'default' in values:
            status_type = values.get('status_type',False)
            domain = [('default','=',True),('status_type','=',status_type)]
            if status_type == 'olivery_collection':
                collection_type = values.get('collection_type',False)
                domain.append(('collection_type','=',collection_type))
            default_statuses = self.env['rb_delivery.status'].search(domain)
            if len(default_statuses) != 0 :
                if values['default'] == True:
                    for default_status in default_statuses:
                        default_status.write({'default':False})
            else:
                values['default'] = True

        res =super(rb_delivery_status, self).create(values)
        if 'title_ar' in values and values['title_ar']:
            translated_title = self.env['ir.translation'].search([('name', '=', 'rb_delivery.status,title'),('res_id','=',res.id)])
            if translated_title:
                translated_title.write({'value':values['title_ar']})
            else:
                self.env['ir.translation'].create({'name':'rb_delivery.status,title','lang':'ar_SY','module':'rb_delivery','type':'model','res_id':res.id,'source':values['title'],'value':values['title_ar']})
        return res

    def copy(self, default=None):
        default = dict(default or {})
        default.update(
            { 'name': self.name + '_copy'})

        return super(rb_delivery_status, self).copy(default)

    @api.multi
    def unlink(self):
        if self.status_type == 'olivery_order' or self.status_type == False:
            orders = self.env['rb_delivery.order'].search([('state','=',self.name)])
            if len(orders)>0:
                self.env['rb_delivery.error_log'].raise_olivery_error(455,self.id,{'name': self.name, 'order_sequence': ', '.join([str(order.sequence) for order in orders])})
                #raise ValidationError(_("You can't delete the status while it's used in orders for status : %s") % self.name)
        elif self.status_type == 'olivery_collection':
            if self.collection_type == 'returned_collection':
                collections = self.env['rb_delivery.returned_money_collection'].search([('state','=',self.name)])
                if len(collections)>0:
                    self.env['rb_delivery.error_log'].raise_olivery_error(456,self.id,{'collection_sequence': ', '.join([str(collection.sequence) for collection in collections]), 'status': self.name})
                    #raise ValidationError(_("You can't delete the status while it's used in returned collections "))
            elif self.collection_type == 'collection':
                collections = self.env['rb_delivery.multi_print_orders_money_collector'].search([('state','=',self.name)])
                if len(collections)>0:
                    self.env['rb_delivery.error_log'].raise_olivery_error(457,self.id,{'collection_sequence':', '.join([str(collection.sequence) for collection in collections])})
                    #raise ValidationError(_("You can't delete the status while it's used in collections"))
            elif self.collection_type == 'agent_collection':
                collections = self.env['rb_delivery.agent_money_collection'].search([('state','=',self.name)])
                if len(collections)>0:
                    self.env['rb_delivery.error_log'].raise_olivery_error(458,self.id,{'collection_sequence': ', '.join([str(collection.sequence) for collection in collections]), 'status': self.name})
                    #raise ValidationError(_("You can't delete the status while it's used in agent collections"))
            elif self.collection_type == 'agent_returned_collection':
                collections = self.env['rb_delivery.agent_returned_collection'].search([('state','=',self.name)])
                if len(collections)>0:
                    self.env['rb_delivery.error_log'].raise_olivery_error(459,self.id,{'collection_sequence': ', '.join([str(collection.sequence) for collection in collections]), 'status': self.name})
                    #raise ValidationError(_("You can't delete the status while it's used in agent returned collections"))
        return super(rb_delivery_status, self).unlink()

    @api.multi
    def name_get(self):
        delivery_user=False
        if self._context and 'uid' in self._context and self._context.get('uid'):
            delivery_user = self.env['rb_delivery.user'].search([('user_id', '=', self._context.get('uid'))])
        result = []
        for status in self:
            name = status.title
            business_status_name=status.business_status_name
            if business_status_name and delivery_user and delivery_user.role_code and delivery_user.role_code == "rb_delivery.role_business":
                result.append((status.id, business_status_name))
            else:
                result.append((status.id, name))
        return result

    def change_status_type(self):
        statuses = self.env['rb_delivery.status'].search([('status_type','=',False)])
        if len(statuses) > 0:
            for state in statuses:
                state.sudo().write({'status_type':'olivery_order'})

    @api.onchange("status_type")
    def change_collection_type(self):
        if self.status_type != 'olivery_collection':
            self.write({'collection_type':''})


    def check_allowed_roles(self):
        roles_have_change_with_no_access = [value for value in self.role_action_status_ids.ids if value not in self.group_ids.ids]
        roles_show_segment_with_no_access = [value for value in self.role_segment_status_ids.ids if value not in self.group_ids.ids]
        role_names = self.group_ids.mapped('name')
        if len(roles_have_change_with_no_access)>0:
            role_names = []
            for role_id in roles_have_change_with_no_access:
                role_names.append(self.env['res.groups'].browse(role_id).name)
            self.env['rb_delivery.error_log'].raise_olivery_error(460,self.id,{'user_role': role_names, 'status': self.title})
            #raise ValidationError(_('Roles who can change to this status must have access to it \n So you should go to \"who can access status\" and add the role their first'))
        if len(roles_show_segment_with_no_access)>0:
            role_names = []
            for role_id in roles_show_segment_with_no_access:
                role_names.append(self.env['res.groups'].browse(role_id).name)
            self.env['rb_delivery.error_log'].raise_olivery_error(461,self.id,{'user_role': role_names, 'status': self.title})
            #raise ValidationError(_('Roles to show in segments for this status must have access to it \n So you should go to \"who can access status\" and add the role their first'))

    @api.model
    def get_next_statuses(self,current_statuses,status_type,collection_type = False):
        rb_delivery_user = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
        is_admin = self.env.user._is_admin()
        all_next_statuses = []
        domain = [('status_type','=',status_type)]
        if collection_type :
            domain.append(('collection_type','=',collection_type))
        if rb_delivery_user and not is_admin:
            group_id = rb_delivery_user.group_id.id
            state_values = self.env['rb_delivery.status'].sudo().search(
                domain + [('name', 'in', current_statuses)]
            )
            status_map = {state.name: state for state in state_values}
            for state in current_statuses:
                statuses_list = []
                if state in status_map:
                    next_statuses = status_map[state].next_state_ids.sorted('sequence').read(['role_action_status_ids', 'pass_lock_allowed_group_ids', 'title', 'name', 'sequence'])
                    for state_value in next_statuses:
                        role_who_access = state_value['role_action_status_ids'] + state_value['pass_lock_allowed_group_ids']
                        if group_id in role_who_access:
                            statuses_list.append((state_value['name'],state_value['title'],state_value['sequence']))
                if statuses_list:
                    all_next_statuses.append(set(statuses_list))

        elif is_admin:
            next_statuses = self.env['rb_delivery.status'].sudo().search(domain, order="sequence ASC")
            statuses_list = [(state.name, state.title, state.sequence) for state in next_statuses]
            all_next_statuses.append(set(statuses_list))
        if all_next_statuses:
            common_statuses = set.intersection(*all_next_statuses)
            return sorted(list(common_statuses), key=lambda x: x[2])
        else:
            return []

    @api.model
    def get_order_next_ststus(self,order_ids):
        orders=self.env['rb_delivery.order'].search_read([('id','in',order_ids)],['state'],order="sequence ASC")
        orders_states = list({obj['state'] for obj in orders})
        next_status= self.get_next_statuses(orders_states,'olivery_order')
        return next_status

    @api.model
    def get_collection_next_status(self,collection_ids,collection_type,model_name):
        collections=self.env[model_name].search_read([('id','in',collection_ids)],['state'])
        collection_states = list({obj['state'] for obj in collections})
        next_status= self.get_next_statuses(collection_states,'olivery_collection',collection_type)
        return next_status
