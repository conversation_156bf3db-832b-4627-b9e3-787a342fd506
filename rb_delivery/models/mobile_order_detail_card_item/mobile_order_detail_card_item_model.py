# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json
class rb_delivery_mobile_order_detail_card_item(models.Model):
    
    _name = 'rb_delivery.mobile_order_detail_card_item'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    order_detail_card = fields.Many2one('rb_delivery.mobile_order_detail_card')

    item_label = fields.Char(required=True)

    item_title = fields.Char()

    item_icon = fields.Char()

    item_icon_selction = fields.Many2one('rb_delivery.icons')

    item_field = fields.Many2one('ir.model.fields')

    unique_field_name = fields.Char(compute = "_compute_unique_field_name", store=True)
    
    @api.onchange('item_icon_selction')
    def item_icon_selection(self):
        if self.item_icon_selction:
            self.item_icon = self.item_icon_selction.name

    @api.multi
    @api.depends('order_detail_card','item_field','item_label')
    def _compute_unique_field_name(self):
        for rec in self:
            if rec.order_detail_card and rec.order_detail_card.name:
                if rec.item_field:
                    rec.unique_field_name = rec.order_detail_card.name + '_' + rec.item_field.name
                elif rec.item_label:
                    rec.unique_field_name = rec.order_detail_card.name + '_' + rec.item_label.replace(" ", "_").lower()
 