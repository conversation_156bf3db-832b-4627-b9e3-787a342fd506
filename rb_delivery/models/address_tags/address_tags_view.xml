<odoo>
  <data>



    <record id="view_tree_rb_delivery_address_tage" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_address_tags</field>
      <field name="model">rb_delivery.address_tags</field>

      <field name="arch" type="xml">
        <tree>
          <field name="sequence" widget="handle"/>
          <field name="name"/>
          <field name="area_id"/>
          <field name="sub_area_id" domain="[('show_in_create', '=', True),'|',('parent_id', '=',area_id),('area_parent_id', '=',area_id)]"/>
          <field name="address"/>
        </tree>
      </field>

    </record>

    <record id="view_form_rb_delivery_address_tage" model="ir.ui.view">
      <field name="name">view_form_rb_delivery_address_tage</field>
      <field name="model">rb_delivery.address_tags</field>

      <field name="arch" type="xml">
        <form>
          <sheet>
            
            <div class="oe_button_box o_full" name="button_box" style="margin-top:1vh">
              <button attrs="{'invisible':['|',('create_date','=',False),('values_changed','=',False)]}" type="object" name="go_to_orders" class="btn btn-sm oe_stat_button o_form_invisible">
                <div class="fa fa-fw fa-list-alt o_button_icon"/>
                  <div class="o_form_field o_stat_info" data-original-title="" title="">
                    <span class="o_stat_text">Orders to refresh</span>
                </div>
              </button>
            </div>
            <group name="group_top">
              <field name="create_date" invisible='1'/>
              <field name="values_changed" invisible='1'/>
              <field name="name"/>
              <field name="area_id"/>
              <field name="sub_area_id" domain="[('show_in_create', '=', True),'|',('parent_id', '=',area_id),('area_parent_id', '=',area_id)]"/>
              <field name="address"/>
              <field name="sequence"/>
              <span attrs="{'invisible':[('values_changed', '=', False)]}" style="color:#D9534F; height: 40px;width:320px">
                  Values of the full address has been changed!  Please go to orders that needs refresh and select the orders then do refresh address values.
              </span>
            </group>
          </sheet>
          <!-- History and communication: -->
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>
        
      </field>

    </record>


  </data>
</odoo>
