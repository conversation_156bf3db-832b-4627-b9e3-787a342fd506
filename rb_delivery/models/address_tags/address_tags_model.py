# -*- coding: utf-8 -*-
from openerp import models, fields, api,_

class rb_delivery_address_tags(models.Model):

    _name = 'rb_delivery.address_tags'
    _description = "Address Tags Model"
    _inherit = "mail.thread"
    _order = "sequence desc, name asc"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    name = fields.Text('Name',compute="_compute_tag_name",store=True)

    area_id = fields.Many2one('rb_delivery.area','Area',track_visibility="on_change",required=True)

    sub_area_id = fields.Many2one('rb_delivery.sub_area','Sub Area',track_visibility="on_change",required=True)

    address = fields.Char('Address',track_visibility="on_change")
    
    sequence = fields.Integer(string="Sequence", track_visibility=False, default=10)

    values_changed = fields.Boolean('Values Changed',compute='_check_orders_need_to_refresh')

    @api.multi
    def write(self,values):
        if values.get('name'):
            values['name'] = values.get('name').strip()
        address_tags = super(rb_delivery_address_tags,self).write(values)
        if values.get('area_id') or values.get('sub_area_id'):
            self.check_address_tag_duplicate()
            self.check_orders_using_this_tag(values)
        return address_tags

    @api.model
    def create(self,values):
        if values.get('name'):
            values['name'] = values.get('name').strip()
        address_tags = super(rb_delivery_address_tags,self).create(values)
        if values.get('area_id') or values.get('sub_area_id'):
            address_tags.check_address_tag_duplicate()
        return address_tags

    def check_address_tag_duplicate(self):
        for rec in self:
            address_tag_count = rec.search_count([('sub_area_id', '=', rec.sub_area_id.id),('area_id', '=', rec.area_id.id), ('address', '=', rec.address), ('id', '!=', rec.id)])
            if address_tag_count:
                self.env['rb_delivery.error_log'].raise_olivery_error(546,rec.id,{})


    def check_orders_using_this_tag(self,values):
        for rec in self:
            domain = self.get_orders_domain(rec)
            orders = self.env['rb_delivery.order'].sudo().search(domain)
            if len(orders):
                message = _('Values of the full address has been changed!')
                what_to_do_next = _('Please go to orders that needs refresh and select the orders then do refresh address values.')
                data = {'uid':self._uid,'message':message,'records':orders,'values':{'is_address_tag_values_changed':True},'update':True}
                self.env['rb_delivery.utility'].olivery_sudo(data)
                for order in orders:
                    onboarding_error_data = {'message':message,'what_to_do_next':what_to_do_next,'error_code':'address_tag_01','record_id':order.id,'model_name':'rb_delivery.order'}
                    self.env['rb_delivery.onboarding_error'].create_onboarding_error(onboarding_error_data)

    def get_orders_domain(self,rec):
        default_status_active_order = self.env['rb_delivery.client_configuration'].get_param('default_status_active_order')
        domain = [('state_id', 'in', default_status_active_order),('address_tag','=',rec.id),('customer_sub_area','!=',rec.area_id.id),('customer_sub_area','!=',rec.sub_area_id.id)]
        return domain

    def _check_orders_need_to_refresh(self):
        for rec in self:
            domain = self.get_orders_domain(rec)
            orders = self.env['rb_delivery.order'].sudo().search_count(domain)
            if orders:
                self.values_changed = True
            else:
                self.values_changed = False


    def go_to_orders(self):
        tree_view_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        domain = self.get_orders_domain(self)
        return {
            'type': 'ir.actions.act_window',
            'name': 'Orders',
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(tree_view_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}


    @api.depends('area_id','sub_area_id','address')
    def _compute_tag_name(self):
        for rec in self:
            area_name = rec.area_id.name if rec.area_id else ''
            sub_area_name = rec.sub_area_id.name if rec.sub_area_id else ''
            address = rec.address or ''
            rec.name = " - ".join(filter(None, [area_name, sub_area_name, address]))

    @api.model
    def create_missing_address_tags(self,sub_areas=False):
        if not sub_areas:
            sub_areas = self.env['rb_delivery.sub_area'].search([])
        for sub_area in sub_areas:
            if not self.search_count([('sub_area_id', '=', sub_area.id),('area_id', '=', sub_area.parent_id.id), ('address', '=', False)]):
                self.create({
                    'area_id': sub_area.parent_id.id,
                    'sub_area_id': sub_area.id
                })
