<odoo>
  <data>
    <!-- Form View -->
    <record id="view_form_rb_delivery_mobile_filter_fields" model="ir.ui.view">
      <field name="name">view_form_rb_delivery_mobile_filter_fields</field>
      <field name="model">rb_delivery.mobile_filter_fields</field>
      <field name="arch" type="xml">
        <form create="false">
          <sheet>
            <group name="group_top">
                <field name="model"/>
                <field name="model_name" invisible="1"/>
                <field name="fields_ids" widget="many2many_tags"/>
                <field name="filter_type"/>
                <field name="group_id"></field>
            </group>
          </sheet>
        </form>
      </field>
    </record>

    <!-- Tree View -->
    <record id="view_tree_rb_delivery_mobile_filter_fields" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_mobile_filter_fields</field>
      <field name="model">rb_delivery.mobile_filter_fields</field>
      <field name="arch" type="xml">
        <tree>
            <field name="group_id"/>
            <field name="model"/>
            <field name="fields_ids"/>
            <field name="filter_type"/>
        </tree>
      </field>
    </record>

  </data>
</odoo>
