from openerp import models, fields, api, _

class rb_delivery_mobile_filter_fields(models.Model):

    _name = 'rb_delivery.mobile_filter_fields'

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]

    model = fields.Many2one('ir.model', string="Model", required=True)

    model_name = fields.Char(related='model.model', readonly=True, store=False) 

    fields_ids = fields.Many2many('ir.model.fields', string="Fields", domain="[('model', '=', model_name),('store', '=', True)]")

    filter_type = fields.Selection([('search_filter','Search Filter'),('group_by_filter','Group By Filter')],string='Filter Type',default='search_filter')

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups)

    @api.model
    def get_filter_fields(self,model,filter_type):
        group = False
        rb_delivery_user = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
        if rb_delivery_user.group_id:
            group = rb_delivery_user.group_id.id
        fields_dict = {}
        record = self.env['rb_delivery.mobile_filter_fields'].sudo().search([('model_name','=',model),('filter_type','=',filter_type),('group_id','=',group)],limit=1)
        for field in record.fields_ids:
            fields_dict[field.name] = {
                'name' : field.name,
                'description' : field.field_description
            }
        return fields_dict
