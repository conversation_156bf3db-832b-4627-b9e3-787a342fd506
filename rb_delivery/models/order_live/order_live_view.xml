<odoo>
  <data>
    <!-- <record id="view_form_rb_delivery_order_live" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_order_live</field>
      <field name="model">rb_delivery.order_live</field>

      <field name="arch" type="xml">
        <form>

          <header>
          </header>

          <sheet>

            <group name="group_top">
              <group name="group_left">
                <field name="order_sequence" string="Order sequence" />
              </group>
            </group>

          </sheet>
        </form>

      </field>
    </record>
 -->
    <record id="view_tree_rb_delivery_order_live" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_order_live</field>
      <field name="model">rb_delivery.order_live</field>

      <field name="arch" type="xml">
        <tree>
          <field name="order_sequence"/>
          <field name="order_assign_to_business"/>
          <field name="order_state"/>
          <field name="order_customer_name" />
          <field name="order_customer_mobile"/>
          <field name="order_assign_to_agent"/>
          <field name="order_customer_area"/>
          <field name="order_order_type_id"/>
          <field name="create_uid" options="{'no_open': True}"/>
          <field name="create_date"/>
          <button name="open_order" class="text-right" icon="fa-eye fa-2x" type="object"/>
          <!-- <field name="total_cost" sum="Total Cost"/> -->
        </tree>
      </field>

    </record>

  </data>
</odoo>
