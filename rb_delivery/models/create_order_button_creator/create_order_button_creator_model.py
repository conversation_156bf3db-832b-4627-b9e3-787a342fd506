from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json

class rb_delivery_create_order_button_creator(models.Model):

    _name = 'rb_delivery.create_order_button_creator'


    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]
    

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups)

    button_icon_selction = fields.Many2one('rb_delivery.icons')

    button_icon = fields.Char('Button Icon')

    action_id = fields.Many2one('rb_delivery.create_order_button_action',string='Action')

    @api.onchange('button_icon_selction')
    def button_icon_selection(self):
        if self.button_icon_selction:
            self.button_icon = self.button_icon_selction.name
    
    @api.onchange('action_id')
    def action_id_selection(self):
        if self.action_id and self.action_id.technical_name and self.action_id.technical_name != 'createOrder':
            self.button_icon = 'scanOrder'
            self.button_icon_selction = False
        elif self.action_id and self.action_id.technical_name and self.action_id.technical_name == 'createOrder':
            self.button_icon = 'createOrder'
            self.button_icon_selction = False