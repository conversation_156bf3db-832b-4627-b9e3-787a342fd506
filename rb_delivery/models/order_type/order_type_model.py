# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError,Warning

class rb_delivery_order_type(models.Model):

    _name = 'rb_delivery.order_type'
    _inherit = 'mail.thread'
    _description = "Order Type Model"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        return status.name if status.name else None

    @api.model
    def get_status(self):
     status_list=[]
     next_statuses=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])
     for status in next_statuses:
         status_list.append((status.name,status.title))
     return status_list

    name = fields.Char('Name', required=True,track_visibility="on_change")

    code = fields.Char('Code', required=True,track_visibility="on_change")

    active = fields.Boolean("Active", default=True,track_visibility="on_change")

    default = fields.Boolean('Default',track_visibility="on_change")

    state = fields.Selection(selection='get_status', track_visibility="on_change",string="Related Order State",default=get_default_status)

    clone_status = fields.Many2one('rb_delivery.status', track_visibility="on_change",string="Clone On Status",domain=['|',('status_type','=',False),('status_type','=','olivery_order')])

    order_type_color = fields.Char('Order Type Color', default='#FFFFFF', track_visibility="on_change")
    
    order_type_secondary_color = fields.Char('Order Type Secondary Color', default='#FFFFFF', track_visibility="on_change")

    cloned_order_status = fields.Many2one('rb_delivery.status', track_visibility="on_change",string="Cloned Order Status",domain=['|',('status_type','=',False),('status_type','=','olivery_order')])

    _sql_constraints = [
        ('code', 'unique(code)', 'Code # already exist !!')]


    def toggle_active(self):
        self.active = not self.active

    @api.model
    def create(self, values):
        if values.get('name'):
            values['name'] = values.get('name').strip()
        order_types = self.env['rb_delivery.order_type'].search([('default','=',True)])
        order_type = super(rb_delivery_order_type, self).create(values)
        if 'default' in values and values['default'] == True:
            if len(order_types) != 0 :
                order_types[0].write({'default':False})
            else:
                order_type.default = True
        else:
            if len(order_types) == 0 :
                order_type.default = True

        return order_type

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        if args is None:
            args = []
        if name.isnumeric():
            recs = self.search(['|',('id', operator, name),('code', operator, name)] + args, limit=limit)
        else:
            recs = self.search(['|',('name', operator, name),('code', operator, name)] + args, limit=limit)


        if not recs.ids:
            return super(rb_delivery_order_type, self).name_search(name=name, args=args,operator=operator,limit=limit)
        return recs.name_get()

    @api.one
    def write(self, values):
        if values.get('name'):
            values['name'] = values.get('name').strip()
        order_types = self.env['rb_delivery.order_type'].search([('default','=',True)])
        if 'default' in values and values['default'] and values['default'] == True :
            if len(order_types) != 0 :
                for order_type in order_types:
                    order_type.write({'default':False})

        if 'active' in values and values['active'] == False:
            if self.default:
                self.env['rb_delivery.error_log'].raise_olivery_error(260,self.id,{'name': self.name})
                #raise ValidationError(_('this type is a default you cant remove it'))

        return super(rb_delivery_order_type, self).write(values)

    @api.onchange('default')
    def _default_order_type(self):
        order_types = self.env['rb_delivery.order_type'].search([('default','=',True)])
        if len(order_types) != 0 and order_types[0].code == self.code:
            self.env['rb_delivery.error_log'].raise_olivery_error(260,self.id,{'name': self.name})
            #raise ValidationError(_('this type is a default you cant remove it'))
        else :
            if self.default:
                return {
                    'warning':{
                        'title': _('Warning'),
                        'message': _('This action will make this type to default'),
                    },
                }
    @api.one
    def unlink(self):
        if self.default:
            self.env['rb_delivery.error_log'].raise_olivery_error(260,self.id,{'name': self.name})
            #raise ValidationError(_('You can not delete order type default'))
        return super(rb_delivery_order_type, self).unlink()

