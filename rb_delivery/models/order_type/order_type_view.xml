<odoo>
  <data>
    <record id="view_form_rb_delivery_order_type" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_order_type</field>
      <field name="model">rb_delivery.order_type</field>

      <field name="arch" type="xml">
        <form>

          <header>
            <!-- Buttons and status widget -->
          </header>

          <sheet>

            <div class="oe_button_box o_full" name="button_box" style="margin-top:1vh">
              <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                <field name="active" widget="boolean_button" options='{"terminology": "archive"}' groups="base.group_system,rb_delivery.role_super_manager"/>
              </button>
              
            </div>

            <div class="oe_title">
              <label for="name" string="Name" class="oe_edit_only"/>
                <h1>
                  <field name="name"/>
                </h1>
            </div>

            <group name="group_top">
              <group name="group_left">
                <field name="code" string="Code" />
                <field name="default" string="Default"/>
                <field name="state"/>
                <field name="order_type_color"/>
                <field name="order_type_secondary_color"/>
                <field name="clone_status"/>
                <field name="cloned_order_status"/>
              </group>
            </group>
            <!-- <group name="group_top">
              <group name="group_left">
                <field name="maximum_mark" placeholder="Maximum Mark"/>
                <field name="minimum_mark" placeholder="Minimum Mark"/>
              </group>
            </group>
            <group name="group_bottom">
              <field name="desc" string="Description" />
            </group>

            <notebook>
              <page string="Classes">
                <field name="class_ids" string="Classes">
                <tree>
                <field name="name"/>
                </tree>
                </field>
              </page>
            </notebook> -->

          </sheet>
          <!-- History and communication: -->
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
        </form>

      </field>
    </record>


    <record id="view_tree_rb_delivery_order_type" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_order_type</field>
      <field name="model">rb_delivery.order_type</field>

      <field name="arch" type="xml">
        <tree>
          <field name="code"/>
          <field name="name"/>
          <field name="default" string="Default"/>
          <field name="state"/>
          <field name="clone_status"/>
          <field name="cloned_order_status"/>
        </tree>
      </field>

    </record>

    <!-- <record id="normal_order" model="rb_delivery.order_type">
      <field name="name">Normal</field>
      <field name="code">10</field>
    </record> -->

  </data>
</odoo>
