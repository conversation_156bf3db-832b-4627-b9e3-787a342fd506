<odoo>
    <data>
        <record id="view_recomputation_job_form" model="ir.ui.view">
            <field name="name">rb_delivery_recomputation_job_form</field>
            <field name="model">rb_delivery.recomputation_job</field>
            <field name="arch" type="xml">
                <form string="Recomputation Job">
                    <header>
                        <button name="open_recomputation_wizard" string="Recompute Records" type="object" class="oe_highlight"/>
                    </header>
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="model_id"/>
                            <field name="field_id" domain="[('model_id', '=', model_id)]"/>
                            <field name="record_limit"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="view_recomputation_job_tree" model="ir.ui.view">
            <field name="name">rb_delivery_recomputation_job_tree</field>
            <field name="model">rb_delivery.recomputation_job</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="model_id"/>
                    <field name="field_id"/>
                    <field name="record_limit"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>
        <record id="view_recomputation_job_wizard_form" model="ir.ui.view">
            <field name="name">recomputation.job.wizard.form</field>
            <field name="model">rb_delivery.recomputation_job_wizard</field>
            <field name="arch" type="xml">
                <form string="Recompute Records">
                    <sheet>
                        <group>
                            <field name="record_limit"/>
                        </group>
                        <footer>
                            <button string="Apply" type="object" name="apply_recomputation" class="btn-primary"/>
                            <button string="Cancel" class="btn-secondary" special="cancel"/>
                        </footer>
                    </sheet>
                </form>
            </field>
        </record>
    </data>
</odoo>
