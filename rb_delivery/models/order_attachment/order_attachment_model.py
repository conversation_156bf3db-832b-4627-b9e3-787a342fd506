# -*- coding: utf-8 -*-
from openerp import models, fields, api, _
import os
import base64
import logging

_logger = logging.getLogger(__name__)

class delivery_order_attachment(models.Model):

    _name = 'rb_delivery.order_attachment'
    _order = "create_date DESC"
    _inherit = "mail.thread"

    name = fields.Char('Name')

    attachment = fields.Binary('Attachment')

    file_type = fields.Char('File Type', compute='_compute_file_type', store=True)

    mimetype = fields.Char('MIME Type', compute='_compute_file_type', store=True)

    order_id = fields.Many2one(comodel_name='rb_delivery.order', string='Order', track_visibility="on_change")

    attachment_reference = fields.Char('Attachment Reference')

    file_size = fields.Integer('File Size', compute='_compute_file_size', store=True)

    @api.depends('name', 'attachment')
    def _compute_file_type(self):
        if 'active_test' in self._context and 'module' in self._context and 'force_recompute' not in self._context:
            return
        for record in self:
            try:
                record.file_type = 'bin'
                record.mimetype = 'application/octet-stream'

                if record.name and '.' in record.name:
                    ext = os.path.splitext(record.name)[1].lower().lstrip('.')
                    record.file_type = ext

                    import mimetypes
                    mime, _ = mimetypes.guess_type(record.name)
                    if mime:
                        record.mimetype = mime

                elif record.attachment:
                    try:
                        data = base64.b64decode(record.attachment)
                        if data.startswith(b'\xff\xd8\xff'):
                            record.file_type = 'jpg'
                            record.mimetype = 'image/jpeg'
                        elif data.startswith(b'\x89PNG\r\n\x1a\n'):
                            record.file_type = 'png'
                            record.mimetype = 'image/png'
                        elif data.startswith(b'GIF87a') or data.startswith(b'GIF89a'):
                            record.file_type = 'gif'
                            record.mimetype = 'image/gif'
                        elif data.startswith(b'%PDF'):
                            record.file_type = 'pdf'
                            record.mimetype = 'application/pdf'
                        elif data.startswith(b'PK\x03\x04'):
                            if record.name and record.name.endswith('.xlsx'):
                                record.file_type = 'xlsx'
                                record.mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            elif record.name and record.name.endswith('.docx'):
                                record.file_type = 'docx'
                                record.mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                            elif record.name and record.name.endswith('.pptx'):
                                record.file_type = 'pptx'
                                record.mimetype = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
                            else:
                                record.file_type = 'zip'
                                record.mimetype = 'application/zip'
                    except Exception as e:
                        _logger.error(f"Error detecting file type: {str(e)}")
            except Exception as e:
                _logger.error(f"Error in _compute_file_type: {str(e)}")
                record.file_type = 'bin'
                record.mimetype = 'application/octet-stream'

    @api.depends('attachment')
    def _compute_file_size(self):
        if 'active_test' in self._context and 'module' in self._context and 'force_recompute' not in self._context:
            return
        for record in self:
            try:
                if record.attachment:
                    data = base64.b64decode(record.attachment)
                    record.file_size = len(data)
                else:
                    record.file_size = 0
            except Exception as e:
                _logger.error(f"Error computing file size: {str(e)}")
                record.file_size = 0

    def get_download_url(self):
        url = '/rb_delivery/attachment/download/%s' % self.id
        return {
            'type': 'ir.actions.act_url',
            'url': url,
            'target': 'new',
        }

    def get_file_icon(self):
        if not self.file_type:
            return 'fa-file-o'

        file_type = self.file_type.lower()

        if file_type in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']:
            return 'fa-file-image-o'

        if file_type == 'pdf':
            return 'fa-file-pdf-o'

        if file_type in ['xls', 'xlsx', 'csv']:
            return 'fa-file-excel-o'

        if file_type in ['doc', 'docx', 'rtf', 'txt']:
            return 'fa-file-word-o'

        if file_type in ['ppt', 'pptx']:
            return 'fa-file-powerpoint-o'

        if file_type in ['zip', 'rar', 'tar', 'gz', '7z']:
            return 'fa-file-archive-o'

        return 'fa-file-o'

    def get_human_file_size(self):
        """Get a human-readable file size (e.g., '2.5 MB')"""
        if not self.file_size:
            return '0 B'

        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024 or unit == 'GB':
                if unit == 'B':
                    return f"{size} {unit}"
                return f"{size:.1f} {unit}"
            size /= 1024

    @api.model
    def create(self, values):
        if 'name' not in values and 'datas_fname' in values:
            values['name'] = values['datas_fname']

        if 'name' not in values and 'order_id' in values and values['order_id']:
            try:
                order = self.env['rb_delivery.order'].browse([values['order_id']])
                if order.exists():
                    values['name'] = order.name or f"attachment_{values['order_id']}"

                    file_ext = self._context.get('file_extension')
                    if file_ext and '.' not in values['name']:
                        values['name'] = f"{values['name']}.{file_ext}"
            except Exception as e:
                _logger.error(f"Error setting name from order: {str(e)}")
                values['name'] = f"attachment_{values['order_id']}"

        if 'name' not in values or not values['name']:
            values['name'] = f"attachment_{self.env.user.id}_{fields.Datetime.now()}"

        order_attachment = super(delivery_order_attachment, self).create(values)
        return order_attachment

    def get_download_url(self):
        """Return a URL to download the attachment"""
        return {
            'type': 'ir.actions.act_url',
            'url': '/rb_delivery/attachment/download/%s' % self.id,
            'target': 'new',
        }
