<odoo>
  <data>

      <record id="view_form_rb_delivery_follow_up_order" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_follow_up_order</field>
      <field name="model">rb_delivery.follow_up_order</field>

      <field name="arch" type="xml">
        <form>

          <header>
            <!-- Buttons and status widget -->
          </header>

          <sheet>

          <div class="oe_button_box o_full" name="button_box" style="margin-top:1vh">
              <button type="object" name="get_order" class="btn btn-sm oe_stat_button o_form_invisible">
                <div class="fa fa-fw fa-list-alt o_button_icon"/>
                  <div class="o_form_field o_stat_info" data-original-title="" title="">
                    <span class="o_stat_text">Order</span>
                </div>
              </button>
          </div>


            <group name="group_top">
              <group >
                <field name="name"/>
                <field name="follow_up_sequence" attrs="{'readonly': [('id', '!=', False)]}"/>
                <field name="note"/>
                <field name="order_id" attrs="{'readonly': [('id', '!=', False)]}"/>
                <field name="business"/>
              </group>
            </group>

          </sheet>
          <!-- History and communication: -->
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
        </form>

      </field>
    </record>

    <record id="view_tree_rb_delivery_follow_up_order" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_follow_up_order</field>
      <field name="model">rb_delivery.follow_up_order</field>
      <field name="arch" type="xml">
        <tree>
          <field name="follow_up_sequence" attrs="{'readonly': [('id', '!=', False)]}"/>
          <field name="name"/>
          <field name="business"/>
          <field name="note"/> 
          <button name="get_order" type="object" string="Order"/>
        </tree>
      </field>
    </record>

            <record id="view_search_rb_delivery_follow_up_order" model="ir.ui.view">
            <field name="name">view_search_rb_delivery_follow_up_order</field>
            <field name="model">rb_delivery.follow_up_order</field>

            <field name="arch" type="xml">

                <search>
                    <group name="search_group">
                        <field name="follow_up_sequence" string="Sequence" />
                        <field name="name" string="Name" />
                        <field name="order_id" string="Order" />
                        <field name="business" string="Business" />
                    </group>
                    <group string="Groups" name="order_group_by_group">
                        <filter name="group_by_business" string="By business" icon="terp-partner" context="{'group_by':'business'}"/>
                        <filter name="group_by_order" string="By Order" icon="terp-partner" context="{'group_by':'order_id'}"/>
                    </group>

                </search>

            </field>

        </record>

  </data>
</odoo>