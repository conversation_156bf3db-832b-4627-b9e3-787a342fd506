# -*- coding: utf-8 -*-

import logging
from openerp import models, fields, api,_
from datetime import datetime
from dateutil.relativedelta import relativedelta
import pytz
import re
_logger = logging.getLogger(__name__)

import re

class olivery_order_logs(models.Model):

    _name = 'rb_delivery.order_logs'
    _order = 'create_date DESC'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------
    order_status = fields.Selection(string='Order Status',selection='_get_statuses',readonly=True)

    field_id = fields.Many2one("ir.model.fields",string="Field Name",readonly=True)

    order_id = fields.Many2one('rb_delivery.order',"Order",readonly=True)

    business_id = fields.Many2one('rb_delivery.user','Business',compute="compute_business",store=True)

    driver_id = fields.Many2one('rb_delivery.user','Driver',compute="compute_driver",store=True)

    old_value = fields.Char('Old Value',readonly=True,translate=False)

    new_value = fields.Char('New Value',readonly=True,translate=False)

    old_value_id = fields.Integer('Old Value Id')

    new_value_id = fields.Integer('New Value Id')

    is_message =fields.Boolean('Is Message' ,defualt=False)

    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    @api.depends('order_id.assign_to_business')
    @api.multi
    def compute_business(self):
        if 'active_test' in self._context and 'module' in self._context and 'force_recompute' not in self._context:
            return
        for rec in self:
            if not rec.sudo().order_id or not rec.sudo().order_id.assign_to_business:
                continue
            rec.business_id = rec.sudo().order_id.assign_to_business.id

    @api.depends('order_id.assign_to_agent')
    @api.multi
    def compute_driver(self):
        if 'active_test' in self._context and 'module' in self._context and 'force_recompute' not in self._context:
            return
        for rec in self:
            if not rec.sudo().order_id or not rec.sudo().order_id.assign_to_agent:
                continue
            rec.driver_id = rec.sudo().order_id.assign_to_agent.id

    def _get_statuses(self):
        status_list=[]
        status_records=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])
        status_list = list(map(lambda status: (status.name, status.title), status_records))
        return status_list

    def clear_old_order_logs(self):
        timezone = self._context.get('tz') or self.env.user.tz or 'Asia/Hebron'
        date = datetime.now(pytz.timezone(timezone))
        fmt = "%Y-%m-%d"
        date = date - relativedelta(months=6)
        date = datetime.strftime(date,fmt) + ' 00:00:00'
        records = self.env['rb_delivery.order_logs'].sudo().search([('create_date','<',date)])
        batch_list = [records[i:i + 1000] for i in range(0, len(records), 1000)]
        for batch in batch_list:
            self.with_delay(channel="root.clean",max_retries=2).delete_order_logs(batch)

        return True

    def delete_order_logs(self,records):
        records.unlink()
        return True

    @api.multi
    def go_to_order(self):
        return {
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'rb_delivery.order',
            'type': 'ir.actions.act_window',
            'res_id': self.order_id.id
        }


    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------