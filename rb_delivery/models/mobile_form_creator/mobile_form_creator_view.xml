<odoo>
  <data>
    <record id="view_form_rb_delivery_mobile_form_creator" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_mobile_form_creator</field>
      <field name="model">rb_delivery.mobile_form_creator</field>

      <field name="arch" type="xml">
        <form>

          <header>
            <!-- Buttons and status widget -->
          </header>
          <sheet>
            <!-- <div class="oe_title">
                <label for="name" string="Name" class="oe_edit_only"/>
                    <h1>
                        <field name="name"/>
                    </h1>
            </div> -->

            <group name="group_top">
                <field name="is_custom"/>
                <field name="form_name_custom" attrs="{'invisible':[['is_custom','=',False]]}"/>
                <field name="form_name" attrs="{'invisible':[['is_custom','=',True]],'required':[['is_custom','=',False]]}"/>
                <field name="is_isolated" invisible="1"/>
                <field name="isolated_input" invisible="1"/>
                <field name="isolated_input_field" invisible="1"/>
                <field name="model_relation" invisible="1"/>
                <field name="show_toolbar"/>
                <field name="is_action_form"/>
                <field name="skip_success_message"/>
                <field name="group_id" attrs="{'invisible':['|',['is_public_form','=',True],['is_isolated','=',True]],'required':[['is_isolated','=',False]]}"/>
                <field name="is_public_form" attrs="{'invisible':['|',['form_name','=','status_action_form'],['is_isolated','=',True]]}"/>
                <field name="is_collection" attrs="{'invisible':[['is_isolated','=',True]]}"/>
                <field name="collection_type" attrs="{'invisible': ['|','|', ['is_collection', '!=', True], ['form_name','!=','status_action_form'],['is_isolated','=',True]]}"/>
                <field name="model" attrs="{'invisible':[['isolated_input_field','!=',False]]}"/>
                <field name="status_ids" widget="many2many_tags" attrs="{'invisible':[['form_name','!=','status_action_form']]}"/>
                <field name="status_action_domain" attrs="{'invisible':[['form_name','!=','status_action_form']]}" widget="domain" options="{'model': 'model_relation','in_dialog': true}" />
                <field name="form_inputs">
                  <tree>
                    <field name="field"/>
                    <field name="domain"/>
                    <field name="required"/>
                    <field name="invisible"/>
                    <field name="is_location" />
                    <field name="is_signature"/>
                    <field name="sequence" widget="handle"/>
                  </tree>
                </field>                
            </group>
          </sheet>
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>

      </field>
    </record>


    <record id="view_tree_rb_delivery_mobile_form_creator" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_mobile_form_creator</field>
      <field name="model">rb_delivery.mobile_form_creator</field>

      <field name="arch" type="xml">
        <tree>
          <field name="form_name"/>
          <field name="group_id"/>
          <field name="name"/>
        </tree>
      </field>

    </record>

  </data>
</odoo>