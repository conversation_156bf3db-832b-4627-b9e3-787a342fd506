<odoo>
    <data>
        <record id="view_form_rb_delivery_otp_status_checker" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_otp_status_checker</field>
            <field name="model">rb_delivery.otp_status_checker</field>

            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group name="group_top">
                            <field name="possible_status_ids" invisible="1" />
                            <field name="is_collection" />
                            <field name="otp_statuses" widget="many2many_tags" />
                            <field name="groups" widget="many2many_tags"/>
                            <field name="send_through" />
                            <field name="email_title" attrs="{'invisible':[('send_through','!=','email')]}"/>
                            <field name="message" />  
                        </group>
                        <group name="group_top" attrs="{'invisible':[('is_collection', '=', True)]}">
                            <div>
                               Placeholders you can use:
                               <br />
                               {otp_code} for: OTP Code
                               <br/>
                               {order.sequence} for: Order Number
                               <br/>
                               {order.customer_name} for: Name of the Receiver
                               <br/>
                               {order.commercial_name} for: Name of the Sender
                               <br/>
                               Example of use:
                               <br/>
                               OTP Code for order number {order.sequence} for {order.customer_name} is {otp_code}
                               </div>
                           </group>
                        <group name="group_top" attrs="{'invisible':[('is_collection', '=', False)]}">
                            <div>
                               Placeholders you can use:
                               <br />
                               {otp_code} for: OTP Code
                               <br/>
                               {collection.sequence} for: Order Number
                               <br/>
                               {collection.business_name} for: Name of the Sender
                               <br/>
                               {collection.mobile_number} for: Mobile of the Sender
                               <br/>
                               Example of use:
                               <br/>
                               OTP Code for order number {collection.sequence} for {collection.business_name} is {otp_code}
                               </div>
                           </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers" />
                        <field name="message_ids" widget="mail_thread" />
                    </div>
                </form>
            </field>
        </record>

        <record id="view_tree_rb_delivery_otp_status_checker" model="ir.ui.view">
            <field name="name">view_tree_rb_delivery_otp_status_checker</field>
            <field name="model">rb_delivery.otp_status_checker</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="otp_statuses" widget="many2many_tags"/>
                    <field name="groups" />
                    <field name="send_through" />
                    <field name="email_title" attrs="{'invisible':[('send_through','!=','email')]}"/>
                    <field name="message"/>  
                </tree>
            </field>
        </record>
    </data>
</odoo>