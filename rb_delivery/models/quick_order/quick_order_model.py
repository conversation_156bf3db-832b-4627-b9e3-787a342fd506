# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from urllib.parse import urlparse, urlunparse
import json

class rb_delivery_quick_order(models.TransientModel):

    _name = 'rb_delivery.quick_order'
    _description = "Quick Order"

    customer_name = fields.Char('Customer Name',track_visibility="on_change")
    counter = fields.Integer(string='Counter', readonly=True)

    def _show_description_tags(self):
        show_description_tags = self.env['rb_delivery.client_configuration'].get_param('show_description_tags_in_quick_order')
        return show_description_tags
    

    def _show_returned_order(self):
        show_returned_order = self.env['rb_delivery.client_configuration'].get_param('show_returned_check_in_quick_order')
        return show_returned_order
    
    def _show_agent(self):
        show_agent = self.env['rb_delivery.client_configuration'].get_param('show_agent_in_quick_order')
        return show_agent
    

    def _show_customer_second_mobile(self):
        show_customer_second_mobile = self.env['rb_delivery.client_configuration'].get_param('show_customer_second_mobile_in_quick_order')
        return show_customer_second_mobile
    
    def _default_business(self):
        user = self.env['res.users'].sudo().search([('id', '=', self._uid)])
        is_business = user.sudo().has_group('rb_delivery.role_business')
        if is_business:
            del_user = self.env['rb_delivery.user'].sudo().search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False
    
    def _get_default_area(self):
        default_area = self.env['rb_delivery.area'].search([('is_default', '=', True)])
        return default_area.id
    
    customer_name = fields.Char('Customer Name',track_visibility="on_change")

    reference_id = fields.Char('Reference ID',track_visibility="on_change")

    note = fields.Char('Note',track_visibility="on_change")

    customer_sub_area = fields.Many2one('rb_delivery.sub_area',string='Receiver Sub Area', domain=[('show_in_create', '=', True), ('active', '=', True)],track_visibility="on_change", ondelete='restrict')
    
    assign_to_business = fields.Many2one('rb_delivery.user', 'Sender',default=_default_business,track_visibility="on_change",domain="[('role_code','=','rb_delivery.role_business')]",required = True)

    customer_area = fields.Many2one('rb_delivery.area', default=_get_default_area ,domain=[('show_in_create', '=', True), ('active', '=', True)], string='Receiver Area', track_visibility="on_change", ondelete='restrict', index=True)

    customer_address = fields.Char('Receiver Address',track_visibility="on_change")

    delivery_fee = fields.Float('Delivery Cost',compute="compute_delivery_fee")

    customer_mobile = fields.Char('Receiver Mobile',track_visibility="on_change")

    customer_second_mobile = fields.Char('Receiver Second Mobile',track_visibility="on_change")

    cost = fields.Float('Cost',track_visibility="on_change" )

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order', 
        string = 'Orders',
        relation = 'print_order_quick_order',
        column1 = 'quick_order_id',
        column2 = 'order_id')

    temp_barcode = fields.Text("Temp BarCode")    

    seq_exist = fields.Char('AWB # Exist',track_visibility="on_change")

    agent = fields.Many2one( 'rb_delivery.user', 'Agent', track_visibility="on_change",domain="[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]")
    
    description_tags = fields.Many2many(
        comodel_name = 'rb_delivery.description_tags',
        string = 'Description Tags',
        relation = 'description_tags_quick_order_table',
        column1 = 'description_tag_id',
        column2 = 'quick_order_id',track_visibility="on_change",copy=False)
    
    show_description_tags = fields.Boolean('Show description tags',default=_show_description_tags)

    returned_order = fields.Boolean('Returned order',track_visibility="on_change")

    show_returned_order = fields.Boolean('show returned order',track_visibility="on_change", default=_show_returned_order)

    show_agent = fields.Boolean('show agent',track_visibility="on_change", default=_show_agent)

    show_customer_second_mobile = fields.Boolean('show customer second mobile',track_visibility="on_change", default=_show_customer_second_mobile)

    extra_cost = fields.Float('Extra Cost', track_visibility="on_change",copy=False)


    @api.depends('customer_area','customer_sub_area','assign_to_business')
    def compute_delivery_fee(self):
        if self.assign_to_business and self.customer_area:
            order_type_id = self.env['rb_delivery.order_type'].search([['default','=',True]]).id
            get_price_values = {
                'sender_id':self.assign_to_business.id,
                'to_area_id':self.customer_area.id,
                'sub_area_id':self.customer_sub_area.id,
                'order_type_id':order_type_id
            }
            self.delivery_fee = self.env['rb_delivery.pricelist'].get_price(get_price_values)
        else:
            self.delivery_fee=0

    
    # ----------------------------------------------------------------------
    # Constraints
    # ----------------------------------------------------------------------

    @api.onchange('customer_area')
    def _domain_sub_area(self):
        ids=[]
        if self.customer_area:
            sub_areas = self.env['rb_delivery.sub_area'].search(['|',('parent_id', '=', self.customer_area.id),('area_parent_id', '=', self.customer_area.id)])
            for sub_area in sub_areas:
                ids.append(sub_area.id)
        if len(ids)>0 or self.customer_area:
            return {'domain': {'customer_sub_area': [('id', 'in', ids)]}}
        else:
            return {'domain': {'customer_sub_area': []}} 
    
    @api.onchange('customer_sub_area')
    def _compute_customer_sub_area(self):
        if self.customer_sub_area.parent_id.id :
            self.customer_area = self.customer_sub_area.parent_id.id 
    
    def submit(self):
        vals = {}
        if self.assign_to_business:
            if self.cost and self.assign_to_business.inclusive_delivery:
                vals['copy_total_cost'] = self.cost
            else:
                vals['cost'] = self.cost    
            vals['assign_to_business'] = self.assign_to_business.id
            if self.assign_to_business.order_type:
                vals['order_type_id'] = self.assign_to_business.order_type.id
        if self.customer_area:
            vals['customer_area'] = self.customer_area.id
        if self.extra_cost:
            vals['extra_cost'] = self.extra_cost
        if self.customer_address:
            vals['customer_address'] = self.customer_address
        if self.customer_name:
            vals['customer_name'] = self.customer_name       
        if self.reference_id:
            vals['reference_id'] = self.reference_id  
        if self.customer_mobile :
            vals['customer_mobile'] = self.customer_mobile        
        if self.customer_sub_area:
            vals['customer_sub_area'] = self.customer_sub_area.id
        if self.customer_second_mobile:
            vals['second_mobile_number'] = self.customer_second_mobile  
        if self.agent:
            vals['assign_to_agent'] = self.agent.id 
        if self.note:
            vals['note'] = self.note  
        if self.description_tags:
            vals['description_tags'] = [(6,0,self.description_tags.ids)]
        status=self.env['rb_delivery.status'].search([('default','=',True),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        if status:
            vals['state'] = status.name
            
        try:
            order = self.env['rb_delivery.order'].create(vals)
        except Exception as e:
            raise ValidationError(str(e))
        
        if order:
            self.order_ids = [(4,order.id)]
            if self.returned_order and order.id:
                    order.create_returned_clone_order(order)
            if self.temp_barcode:
                self.temp_barcode = self.temp_barcode + ','+ str(order.id) 
            else:
                self.temp_barcode = str(order.id)    
            discharged_fields_ids = self.env['rb_delivery.client_configuration'].get_param('field_to_be_empty_after_create_quick_order') 
            if discharged_fields_ids and len(discharged_fields_ids) > 0 :
                discharged_fields = self.env['ir.model.fields'].search([('id', 'in',discharged_fields_ids)])
                for field in discharged_fields:
                    self[field.name] = False
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': self._name,
            'res_id': self.id,
            'view_mode': 'form',
            'view_type': 'form',
            'views': [(False, 'form')],
            'target': 'new',
            'flags': {'form': {'action_buttons': True, 'options': {'mode': 'edit'}}}}
    
    def print_runsheet(self):
        reportname = 'rb_delivery.dist?docids=' + self.temp_barcode + '&report_type=qweb-pdf&model_name=rb_delivery.order'
        
        return {
        'type': 'ir.actions.report',
        'report_name': reportname,
        'model': 'rb_delivery.quick_order',
        'report_type': 'qweb-pdf',
        'context': {'active_ids': [self.id]},
}


    def print_waybill_a4(self):
        reportname = 'rb_delivery.order_detail_a4?docids=' + self.temp_barcode + '&report_type=qweb-pdf&model_name=rb_delivery.order'
        return {
                'type': 'ir.actions.report',
                'report_type': 'qweb-pdf',
                'report_name': reportname,
                'report_file': 'rb_delivery.order_detail_a4',}

    def print_waybill_a5(self):
        reportname = 'rb_delivery.order_detail?docids=' + self.temp_barcode + '&report_type=qweb-pdf&model_name=rb_delivery.order'
        return {
                'type': 'ir.actions.report',
                'report_type': 'qweb-pdf',
                'report_name': reportname,
                'report_file': 'rb_delivery.order_detail',}  
    def get_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        ids = [order.id for order in self.order_ids]
        domain = [('id', 'in', ids)]
        action_id = self.env.ref('rb_delivery.action_rb_delivery_order').id
        url = f"/web#action={action_id}&model=rb_delivery.order&view_type=list&"
        return {
            'url': url,
            'type': 'ir.actions.act_window',
            'name': 'Orders',
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'domain': domain,
            'target': 'current',
            'flags': {'form': {'action_buttons': True, 'options': {'mode': 'edit'}}},
        }

                          

    @api.onchange('reference_id')
    def _change_reference(self):
        if self.reference_id:
            order = self.env['rb_delivery.order'].search([('reference_id','=',self.reference_id)],limit=1)
            if len(order) != 0:
                if self._origin.id:
                    if self._origin.id != order.id:
                        self.seq_exist = _("Ref ID exists saving will update existing order details")
                    else:
                        self.seq_exist = ""   
                else:        
                    self.seq_exist = _("Ref ID exists saving will update existing order details")
            else:
                self.seq_exist = ""            
                
    @api.model
    def fields_get(self, allfields=None, attributes=None):
        fields = super(rb_delivery_quick_order, self).fields_get(allfields, attributes)

        model_id = self.env['ir.model'].sudo().search([('model','=',"rb_delivery.order")]).id
        control_fields = self.env['rb_delivery.control_fields'].sudo().search([('model_id','=', model_id)])

        for control_field in control_fields:
            if control_field.required:
                for field in control_field.field_ids:
                    if field.name in fields:
                        fields[field.name]['required'] = True    
            else:
                for field in control_field.field_ids:
                    if field.name in fields:
                        fields[field.name]['required'] = False 

        return fields