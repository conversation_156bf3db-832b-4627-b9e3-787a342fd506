# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import AccessError, UserError, RedirectWarning, ValidationError, Warning
from odoo.exceptions import UserError
from openerp import SUPERUSER_ID
import base64
import qrcode
from io import BytesIO

class rb_delivery_agent_returned_collection(models.Model):
    _name = 'rb_delivery.agent_returned_collection'
    _inherit = 'mail.thread'
    _order = "sequence DESC"
    _description = "Agent Returned Collection Model"
    name = fields.Char('Note')

    @api.model
    def _default_is_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_fee:
            default_is_block_delivery_fee = True
        else :
            default_is_block_delivery_fee = False
        return default_is_block_delivery_fee

    @api.one
    def _compute_is_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_fee:
            is_block_delivery_fee = True
        else :
            is_block_delivery_fee = False
        self.is_block_delivery_fee = is_block_delivery_fee

    @api.one
    def check_user(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        self.is_collection_manager = user.has_group('rb_delivery.role_collection_manager')

    def default_is_collection_manager(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_collection_manager = user.has_group('rb_delivery.role_collection_manager')
        return is_collection_manager

    @api.model
    def _default_show_note(self):
        default_show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        return default_show_note

    @api.one
    def _compute_show_note(self):
        show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        self.show_note = show_note

    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','agent_returned_collection'),('status_type','=','olivery_collection')],limit=1)
        return status.name if fields else None

    def default_allow_edit_collection_orders(self):
        if self._uid == 1 or self._uid == 2:
            allow_edit_collection_orders = True
        else:
            user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
            allow_edit_collection_orders = user.allow_edit_collection_orders
        return allow_edit_collection_orders

    @api.one
    def compute_allow_edit_collection_orders(self):
        if self._uid == 1 or self._uid == 2:
            self.allow_edit_collection_orders = True
        else:
            user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
            self.allow_edit_collection_orders = user.allow_edit_collection_orders

    def compute_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        self.use_qr_code = use_qr_code

    def default_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        return use_qr_code


    report_type= fields.Selection([
        ('branch','Branch'),
        ('business','Business')
    ])

    sequence = fields.Char('Sequence', readonly=True,copy=False, track_visibility=False)

    barcode = fields.Binary('Barcode', compute="create_barcode")

    business_id = fields.Many2one('rb_delivery.user', 'Business Name')

    mobile_number = fields.Char(related='business_id.mobile_number', readonly="True", store="True")

    address = fields.Char(related='business_id.address', readonly="True", store="True")

    area_id = fields.Many2one('rb_delivery.area',related='business_id.area_id', readonly="True", store="True")

    #deprecated
    driver_id = fields.Many2one('rb_delivery.user', 'Driver',compute="get_agent")

    #new computed field computed and stored to be able to group by
    agent_id = fields.Many2one('rb_delivery.user', 'Agent',compute="get_agent",store=True)

    user_sequence = fields.Char(related='agent_id.user_sequence', track_visibility=False, readonly=True)

    agent_mobile_number = fields.Char(related='agent_id.mobile_number', string="Agent mobile", readonly="True", store="True")

    total_cost = fields.Float('Total Net Value')

    total_delivery_cost = fields.Float('Total Delivery Fee')

    total_ammount = fields.Float('Expected total amount')

    total_money_collection_cost = fields.Float("Total COD Value")

    final_cost = fields.Float('Final Cost')

    payment_type = fields.Many2one('rb_delivery.payment_type', 'Payment Method',track_visibility="on_change")

    payment_detail = fields.Char(string='Payment Detail',track_visibility="on_change")

    state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",default=get_default_status)

    previous_status = fields.Char('Previous Status', track_visibility=False)

    state_id = fields.Many2one('rb_delivery.status', 'Status ID',compute="compute_status_id",store=True)

    status_color = fields.Char(related='state_id.colour_code', track_visibility="on_change")

    secondary_status_color = fields.Char(related='state_id.secondary_colour_code', track_visibility="on_change")

    previous_status_title = fields.Char('Previous Status Name',  track_visibility="on_change", readonly=True)

    show_note = fields.Boolean('Show Note', default=_default_show_note, compute="_compute_show_note", readonly=True)

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'print_order_agent_returned_item',
        column1 = 'print_agent_returned_id',
        column2 = 'order_id')

    active = fields.Boolean('Active', default=True , track_visibility="on_change")

    is_block_delivery_fee = fields.Boolean('Is Block Delivery Fee', default=_default_is_block_delivery_fee, compute="_compute_is_block_delivery_fee", readonly=True)

    order_count = fields.Integer(string="Order count")

    is_collection_manager = fields.Boolean('Is Collection Manager', compute="check_user", default=default_is_collection_manager)

    stuck_orders = fields.Integer('Stuck orders',readonly=True)

    rejected_orders = fields.Integer('Rejected orders',readonly=True)

    replacement_orders = fields.Integer('Replacement orders',readonly=True)

    partial_rejected_orders = fields.Integer('Partial Rejected orders',readonly=True)

    cancelled_orders = fields.Integer('Cancelled orders',readonly=True)

    reschedule_orders = fields.Integer('Reschedule orders',readonly=True)

    allow_edit_collection_orders = fields.Boolean('Allow edit collections orders', compute="compute_allow_edit_collection_orders", default=default_allow_edit_collection_orders)

    qr_code_image = fields.Binary('QR Code', compute="create_qr_code")

    use_qr_code = fields.Boolean('Use QR code', compute="compute_use_qr_code", default=default_use_qr_code)

    @api.multi
    @api.depends('state')
    def compute_status_id(self):
        for rec in self:
            if rec.state:
                state_id = self.env['rb_delivery.status'].search([('name','=',rec.state),('status_type','=','olivery_collection'),('collection_type','=','agent_returned_collection')],limit=1)
                rec.state_id = state_id.id

    @api.one
    def get_agent(self):
        if len(self.order_ids)>0:
            self.agent_id = self.order_ids[0].assign_to_agent.id
        else:
            self.agent_id = []

    @api.model
    def get_status(self):
        fields=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','agent_returned_collection')])
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','agent_returned_collection')])

        for status in next_statuses:
            status_list.append((status.name,status.title))
        return status_list

    def check_order(self,order_ids):
        default_state = ''
        order_collection_ids = []
        for order in self.order_ids:
            order_collection_ids.append(order.id)
        if len(self.order_ids) > 0:
            default_state = self.order_ids[0].state

        agent_status = self.env['rb_delivery.client_configuration'].get_param('agent_returned_collection_status')
        is_allowed_to_edit = self.env['rb_delivery.client_configuration'].get_param('ability_to_update_returned_agent_collection_on_different_status')

        for order_id in order_ids:
            if order_id not in order_collection_ids:
                order = self.env['rb_delivery.order'].search([('id','=',order_id)])
                collections = self.env['rb_delivery.agent_returned_collection'].search([('driver_id','=',order.assign_to_agent.id)])
                for collection in collections:
                    if order in collection.order_ids:
                        self.env['rb_delivery.error_log'].raise_olivery_error(320,self.id,{'order_sequance': order.sequence,'collection_sequence':collection.sequence})
                        #raise ValidationError(_("Order already exists in Agent Returned Collection %s ") %(collection.sequence))
                if not is_allowed_to_edit and default_state != '' and order.state != default_state:
                    order_status = self.env['rb_delivery.status'].search([('name','=',default_state),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                    self.env['rb_delivery.error_log'].raise_olivery_error(321,self.id,{'order_status':order_status.title, 'order_sequance': order.sequence})
                    #raise ValidationError(_('Order state should be '+order_status.title))
                elif is_allowed_to_edit or default_state == '':
                    default_collection_status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','agent_returned_collection'),('status_type','=','olivery_collection')],limit=1)
                    agent_states = []
                    message = ''
                    if not agent_status:
                        return
                    for state_id in agent_status:
                        state = self.env['rb_delivery.status'].search([('id','=',state_id)])
                        agent_states.append(state.name)
                        message = message +' ' +state.title +'/'
                    if order.state and order.state not in agent_states:
                        self.env['rb_delivery.error_log'].raise_olivery_error(321,self.id,{'order_status':message, 'order_sequance': order.sequence})
                        #raise ValidationError(_('Order state should be in ')+message)
                    else:
                        if default_collection_status and default_collection_status.default_related_order and default_collection_status.related_order_status:
                            order_status = self.env['rb_delivery.status'].search([('name','=',default_collection_status.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                            if order_status and order_status.name and order_status.name != order.state:
                                data = {'uid':self._uid,'message':_("Orders status updated from update returned agent money collection."),'records':order,'values':{'state':order_status.name},'update':True}
                                self.env['rb_delivery.utility'].olivery_sudo(data)

                    self.active = True
                    self.state =  default_collection_status.name
                if not order.assign_to_agent:
                    self.env['rb_delivery.error_log'].raise_olivery_error(323,self.id,{'order_sequance': order.sequence})
                    #raise ValidationError(_('Order should have an agent'))

                if self.driver_id and order.assign_to_agent.id != self.driver_id.id:
                    self.env['rb_delivery.error_log'].raise_olivery_error(324,self.id,{'order_sequance': order.sequence, 'driver_name':self.driver_id.username})
                    #raise ValidationError(_('Order agent should be ')+self.driver_id.username)

    # inherit module[olivery_branch_collection]
    @api.one
    def write(self, values):
        if 'state' in values and values['state']:
            self.authorize_change_status(values['state'])
            self.env['rb_delivery.action'].notify_for_action_type('for_collection',state_name=values['state'],collection_type='agent_returned_collection')
            if values['state'] == 'deleted':
                for order in self.order_ids:
                    order.write({'agent_returned_collection_id':False})
                self.active = False
            values['previous_status'] = self.state
        else:
            if self.state == 'completed_returned':
                if self._uid == 1 or self._uid == 2 or 'active' in values  or 'message_main_attachment_id' in values and len(values)==1 or self.env.user.has_group('rb_delivery.role_configuration_manager'):
                        pass
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(325,self.id,{})
                    #raise ValidationError(_("You can't edit the collection when the status is Completed Returned"))
        if 'order_ids' in values and values['order_ids'] and len(values['order_ids'])>0 and values['order_ids'][0] and len(values['order_ids'][0])>1:
            all_current_orders_in_collection = values['order_ids'][0][2]

            self.check_order(all_current_orders_in_collection)
            old_sequences = []
            new_sequences = []
            new_ids = values['order_ids'][0][2]
            removed_ids = [order_id for order_id in self.order_ids.ids if order_id not in new_ids]
            orders = self.env['rb_delivery.order'].sudo().browse(removed_ids).mapped('sequence')
            if len(orders) > 0:
                self.message_post(body=_("Orders %s were removed from returned agent collection %s by %s") % (', '.join(orders), self.sequence, self.env.user.name))
            added_ids = [order_id for order_id in new_ids if order_id not in self.order_ids.ids]
            orders = self.env['rb_delivery.order'].sudo().browse(added_ids).mapped('sequence')
            if len(orders) > 0:
                self.message_post(body=_("Orders %s were added to returned agent collection %s by %s") % (', '.join(orders), self.sequence, self.env.user.name))
            if len(all_current_orders_in_collection)==0:
                self.active = False
                for order in self.order_ids:
                    order.write({'agent_returned_collection_id':False})
            else:
                values['order_count'] = len(all_current_orders_in_collection)
                values_to_be_reflected = self.env['rb_delivery.utility'].reflect_changes_to_collections(all_current_orders_in_collection,'returned_agent_money_collection')
                if values_to_be_reflected:
                    values.update(values_to_be_reflected)
                for order in self.order_ids:
                    new_sequences.append(order.sequence)
                    old_sequences.append(order.sequence)
                    if order.id not in all_current_orders_in_collection:
                        order.write({'agent_returned_collection_id':False})
                        index = new_sequences.index(order.sequence)
                        del new_sequences[index]

                rejected_orders_count = 0
                stuck_orders_count = 0
                replacement_orders_count = 0
                cancelled_orders_count = 0
                partial_rejected_orders_count  = 0
                reschedule_orders_count = 0

                for order_id in all_current_orders_in_collection:
                    order = self.env['rb_delivery.order'].search([('id','=',order_id)])
                    new_sequences.append(order.sequence)
                    order.write({'agent_returned_collection_id':self.id})

                    if order.state == 'rejected':
                        rejected_orders_count = rejected_orders_count + 1

                    elif order.state == 'stuck':
                        stuck_orders_count = stuck_orders_count + 1

                    elif order.state == 'replacement':
                        replacement_orders_count = replacement_orders_count + 1

                    elif order.state == 'canceled':
                        cancelled_orders_count = cancelled_orders_count + 1

                    elif order.state == 'rejected_partial':
                        partial_rejected_orders_count = partial_rejected_orders_count + 1

                    elif order.state == 'reschedule':
                        reschedule_orders_count = reschedule_orders_count + 1

                values['rejected_orders'] = rejected_orders_count
                values['stuck_orders'] = stuck_orders_count
                values['replacement_orders'] = replacement_orders_count
                values['cancelled_orders'] = cancelled_orders_count
                values['partial_rejected_orders'] = partial_rejected_orders_count
                values['reschedule_orders'] = reschedule_orders_count



            user = self.env['res.users'].sudo().search([('id','=',self._uid)])
            self.message_post(body=_("Orders were changed from %s to %s by %s") % (old_sequences,new_sequences,user.name))
        return super(rb_delivery_agent_returned_collection, self).write(values)

    def create_multi_collection(self, docs):
        new_doc = []
        new_docs = []
        driver_list = []
        for doc in docs:
            if doc.assign_to_agent.id not in driver_list:
                driver_list.append(doc.assign_to_agent.id)
        for driver in driver_list:
            for doc in docs:
                if doc.assign_to_agent.id == driver:
                    new_doc.append(doc)
            new_docs.append(new_doc)
            new_doc= []
        print(new_docs)
        return new_docs

    # inherit module[olivery_branch_collection]
    @api.model
    def create(self, values):
        user = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
        active_ids = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        if 'state' in values and values['state']:
            self.authorize_change_status(values['state'])
            self.env['rb_delivery.action'].notify_for_action_type('for_collection',state_name=values['state'],collection_type='agent_returned_collection')

        orders_no_agent = ''
        for order in active_ids:
            if not order.assign_to_agent:
                orders_no_agent = orders_no_agent  + ', ' + order.sequence

        if orders_no_agent != '':
            message = _("There is no agent in these orders ")+orders_no_agent
            self.env['rb_delivery.error_log'].raise_olivery_error(326,self.id,{'orders_no_agent':orders_no_agent})
            #raise ValidationError(message)


        orders_agent_lists = self.create_multi_collection(active_ids)
        if 'name' in values and values['name']:
            title = values['name']
        else:
            title = ''

        branch_id = ''
        if values.get('branch_id'):
            branch_id = values.get('branch_id')
        agent_returned_states_ids = self.env['rb_delivery.client_configuration'].get_param('agent_returned_collection_status')
        for orders_list in orders_agent_lists:
            values = {}
            if branch_id:
                values['branch_id'] = branch_id
            if not orders_list[0].assign_to_business:
                self.env['rb_delivery.error_log'].raise_olivery_error(327,self.id,{'order_sequance':orders_list[0].sequence})
                #raise ValidationError(_('Please add a business to the order'))
            if orders_list[0].assign_to_agent:
                if orders_list[0].assign_to_agent.default_payment_type:
                    values['payment_type'] = orders_list[0].assign_to_agent.default_payment_type.id
                    values['payment_detail'] = orders_list[0].assign_to_agent.default_payment_detail
                else:
                    payment_type = self.env['rb_delivery.payment_type'].search([('default','=',True)])
                    if len(payment_type) != 0:
                        values['payment_type'] = payment_type[0].id if payment_type else None
            if title != '':
                values['name'] = title + _('_agent ') + orders_list[0].assign_to_agent.username
            else:
                values['name'] =_('Agent ')+ orders_list[0].assign_to_agent.username
            print(values)
            ids = []

            total_cost = 0.0
            total_delivery_cost = 0.0
            total_ammount = 0.0
            total_money_collection_cost = 0.0
            replacement_orders = 0
            rejected_orders = 0
            partial_rejected_orders = 0
            cancelled_orders = 0
            stuck_orders = 0
            reschedule_orders = 0
            agent_returned_states = []
            message = ''
            if not agent_returned_states_ids:
                self.env['rb_delivery.error_log'].raise_olivery_error(328,self.id,{'collection_status': agent_returned_states_ids})
                #raise ValidationError('Please contact your adminstrator to add states for agent returned collection')
            for state_id in agent_returned_states_ids:
                state = self.env['rb_delivery.status'].search([('id','=',state_id)])
                agent_returned_states.append(state.name)
                message = message +' ' +state.title +'/'
            for order in orders_list:
                if order.state and order.state in agent_returned_states:
                    total_cost = total_cost + order.required_from_business
                    total_delivery_cost = total_delivery_cost + order.delivery_cost
                    if order.inclusive_delivery:
                        total_ammount = total_ammount + order.copy_total_cost
                    else:
                        total_ammount = total_ammount + order.cost + order.delivery_cost
                    total_money_collection_cost = total_money_collection_cost + order.money_collection_cost
                    ids.append(order.id)
                    if order.state == 'rejected':
                        rejected_orders = rejected_orders + 1
                    elif order.state == 'replacement':
                        replacement_orders = replacement_orders + 1
                    elif order.state == 'canceled':
                        cancelled_orders = cancelled_orders + 1
                    elif order.state == 'rejected_partial':
                        partial_rejected_orders = partial_rejected_orders + 1
                    elif order.state == 'stuck':
                        stuck_orders = stuck_orders + 1
                    elif order.state == 'reschedule':
                        reschedule_orders = reschedule_orders + 1
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(329,self.id,{'order_status':order.state_id.name, 'order_sequance': order.sequence, 'message': message})
                    #raise ValidationError(_('To create agent returned collection, order status must be in ') + message)


            values['order_ids'] = [(6,0,ids)]
            values['order_count'] = len(ids)
            values['total_cost'] = total_cost
            values['total_money_collection_cost'] = total_money_collection_cost
            values['total_delivery_cost'] = total_delivery_cost
            values['total_ammount'] = total_ammount
            values['stuck_orders'] = stuck_orders
            values['cancelled_orders'] = cancelled_orders
            values['partial_rejected_orders'] = partial_rejected_orders
            values['reschedule_orders'] = reschedule_orders
            values['replacement_orders'] = replacement_orders
            values['rejected_order'] = rejected_orders
            values['business_id'] = orders_list[0].assign_to_business.id
            values['driver_id'] = orders_list[0].assign_to_agent.id
            values['agent_id'] = orders_list[0].assign_to_agent.id

            for order in orders_list:
                status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','agent_returned_collection'),('status_type','=','olivery_collection')],limit=1)
                if status and status.default_related_order and status.related_order_status:
                    order_status = self.env['rb_delivery.status'].search([('name','=',status.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                    if order_status and order_status.name and order_status.name != order.state:
                        data = {'uid':self._uid,'message':_("Orders status updated from create agent money collection."),'records':order,'values':{'state':order_status.name},'update':True}
                        self.env['rb_delivery.utility'].olivery_sudo(data)

            new_sequence = self.env['ir.sequence'].next_by_code('rb_delivery.agent_returned_collection')
            values['sequence'] = new_sequence
            order_report = super(rb_delivery_agent_returned_collection, self).create(values)

            for order in order_report.order_ids:
                order.write({'agent_returned_collection_id':order_report.id})
            if order_report.report_type == 'branch':
                order_report.to_branch= rec[0].to_branch.id
                order_report.final_cost= total_ammount

        message = _('Agent returned collection generated')
        self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))
        return order_report

    def wkf_action_change_status(self):
        address_form_id = self.env.ref('rb_delivery.view_form_rb_delivery_order_select_agent_returned_money_collection_state').id
        context = {"parent_obj":self.id}

        return {
            'type': 'ir.actions.act_window',
            'name': 'Select State',
            'res_model': 'rb_delivery.select_agent_returned_money_collection_state',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'form'), (False, 'tree')],
            'target': 'new',
            'context': context,
            'domain': []}

    @api.model
    def authorize_change_status(self,status):

        if self.state and (self._uid!=1 and self._uid!=2): self.check_lock_status(self.state,status)
        #for super (used sudo) admin and super manager skip
        user = self.env['res.users'].search([('id', '=', self._uid)])
        if user.has_group('rb_delivery.role_super_manager') or self._uid==1 or self._uid==2:
            return
        #get the current role and the current status
        user_group = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id

        #get fields that has teh ability to edit for that specific role | status
        record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','agent_returned_collection')],limit=1)

        exist = False
        if record.role_action_status_ids and len(record.role_action_status_ids)>0:
            for role in record.role_action_status_ids:
                if role.id == user_group.id:
                    exist=True
                    break
        if not exist:
            self.env['rb_delivery.error_log'].raise_olivery_error(293,self.id,{'group': user_group, 'status': status,'collection_type':_('Agent returned collection')})
            #raise Warning(_("You are not allowed to change to this state"))

        return

    @api.model
    def check_lock_status(self,status,next_status):
        current_status_record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','agent_returned_collection')],limit=1)
        next_status_record=self.env['rb_delivery.status'].search([('name','=',next_status),('status_type','=','olivery_collection'),('collection_type','=','agent_returned_collection')],limit=1)

        if not current_status_record.lock_status:
            # then there is no lock you can move to next status
            return
        else :
            # check if there is exception
            allowed_group=current_status_record.pass_lock_allowed_group_ids
            user_groups = self.env['res.users'].search([('id', '=', self._uid)]).groups_id
            if user_groups and allowed_group and set(allowed_group).intersection(set(user_groups)): return
            elif next_status_record.id in current_status_record.next_state_ids.ids :return
            else :
                # check if the next status is in the next status of the record
                self.env['rb_delivery.error_log'].raise_olivery_error(292,self.id,{'first': current_status_record.title, 'second': next_status_record.title,'collection_type':_('Agent returned collection')})
                #raise Warning(_("You are not allowed to change from this status {} to this status {}").format(current_status_record.title,next_status_record.title))

    @api.one
    @api.depends('sequence')
    def create_barcode(self):
        if (self.sequence):
            import barcode
            from barcode.writer import ImageWriter
            import io
            import base64
            barcode.base.Barcode.default_writer_options['write_text'] = False
            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.sequence, writer=ImageWriter(), add_checksum=False)
            # ean = EAN(self.name, writer=ImageWriter())
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.barcode = encoded
            # self.write({'barcode':encoded})



    @api.model
    def print_multi_orders_agent_returned_collection_report(self,collection_id):
        pdf, _ = self.env.ref('rb_delivery.report_rb_delevery_agent_returned_collection_action').sudo().render_qweb_pdf(collection_id)
        pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
        data = base64.encodestring(pdf)
        return data

    def get_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        ids = []
        for order in self.order_ids:
            ids.append(order.id)
        domain = [('id', 'in', ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    def get_signature(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_signature').id
        domain = [('agent_returned_collection_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.signature',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    @api.multi
    def change_agent_returned_collection_state(self,state_name):
        collection_state = self.env['rb_delivery.status'].sudo().search([('name','=',state_name),('collection_type','=','agent_returned_collection'),('status_type','=','olivery_collection')])
        order_state = self.env['rb_delivery.status'].sudo().search([('name','=',collection_state.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')])

        self.write({'state': collection_state.name})
        if order_state and len(order_state) >0:
            data = {'uid':self._uid,'message':_("Orders status updated from update returned agent money collection status."),'records':self.order_ids,'values':{'state': order_state.name,'is_from_collection':True},'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)
        return True

    @api.model
    def group_by_get_business(self,domain):
        users = []
        collections = self.env['rb_delivery.agent_returned_collection'].search(domain)
        for collection in collections:
                if {"id":collection.business_id.id ,"name":collection.business_id.commercial_name} not in users :
                        users.append({"id":collection.business_id.id ,"name":collection.business_id.commercial_name})
        return users

    @api.model
    def group_by_get_driver(self,domain):
        users = []
        collections = self.env['rb_delivery.agent_returned_collection'].search(domain)
        for collection in collections:
                if {"id":collection.driver_id.id ,"name":collection.driver_id.username} not in users :
                        users.append({"id":collection.driver_id.id ,"name":collection.driver_id.username})
        return users

    @api.model
    def agent_returned_collection_count(self,domain):
        count = 0
        if domain:
            count = self.env['rb_delivery.agent_returned_collection'].search_count(domain)
            return count
        else:
            count = self.env['rb_delivery.agent_returned_collection'].search_count([])
            return count

    @api.multi
    @api.depends('sequence')
    def create_qr_code(self):
        for rec in self:
            if (rec.sequence):
                qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
                qr.add_data(rec.sequence)
                qr.make(fit=True)
                img = qr.make_image()
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.qr_code_image=qr_image