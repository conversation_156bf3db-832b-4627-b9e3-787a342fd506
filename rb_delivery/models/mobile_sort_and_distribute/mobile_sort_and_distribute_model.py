# -*- coding: utf-8 -*-
from openerp import models, fields, api,_


class rb_delivery_mobile_sort_and_distribute(models.Model):

    _name = 'rb_delivery.mobile_sort_and_distribute'
    _description = "Mobile Sort and distribute"
    _inherit = 'mail.thread'

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups, track_visibility='on_change')

    setting_items = fields.One2many('rb_delivery.mobile_sort_and_distribute_items', inverse_name='sort_and_distribute_creator')

    should_scan_follow_orders = fields.Boolean('Should scan follow orders')


    @api.model
    def sort_and_destribute_orders(self, order_barcords):
        if self._uid == 4: 
            return 'UNAUTHORIZED_ACCESS'  # translated in mobile

        orders = self.env['rb_delivery.order'].sudo().search(['|','|','|', ('sequence', 'in', order_barcords), ('reference_id', 'in', order_barcords),('partner_reference_id', '=', order_barcords),('follower_ref_id', '=', order_barcords)])

        orders_by_state = {}
        for order in orders:
            state = order.sudo().state  
            if state not in orders_by_state:
                orders_by_state[state] = self.env['rb_delivery.order']
            orders_by_state[state] += order

        unchanged_orders = [] 
        sort_configurations = self.env['rb_delivery.mobile_sort_and_distribute'].search([], limit=1)  # Fetch sorting configuration

        if sort_configurations:
            confs = sort_configurations.setting_items
            for conf in confs:
                from_state_name = conf.from_status_id.name
                
                if from_state_name in orders_by_state:
                    matching_orders = orders_by_state[from_state_name]
                    
                    username = self.env.user.name
                    message = _("Order updated by %s using sort and destribute from mobile.")%(username)
                    values = {
                        'state' : conf.to_status_id.name,   
                        }
                    data = {'uid':self._uid,'message':message,'records':matching_orders,'values':values,'update':True}
                    self.env['rb_delivery.utility'].olivery_sudo(data)

                    del orders_by_state[from_state_name]

        for state, orders in orders_by_state.items():
            for order in orders:
                unchanged_orders.append([order.sudo().sequence, order.sudo().reference_id])

        return unchanged_orders


    @api.model
    def get_order_info(self, barcode):
        if self._uid == 4: 
            return 'UNAUTHORIZED_ACCESS' # translated in mobile
        
        order = self.env['rb_delivery.order'].sudo().search(['|','|','|', ('sequence','=',barcode), ('reference_id', '=', barcode), ('partner_reference_id', '=', barcode), ('follower_ref_id', '=', barcode)])
        
        if len(order) > 1:
            return 'MULTIPLE_ORDERS_FOUND_FOR_THE_SAME_REFERENCE'

        if not order:
            order = self.env['rb_delivery.follow_up_order'].sudo().search([('follow_up_sequence', '=', barcode)])
            if order:
                order = order.sudo().order_id

        return order.sudo().read()
    

    @api.model
    def get_if_should_scan_follow_orders(self):
        user_group = self.env['rb_delivery.user'].sudo().search([('user_id', '=', self._uid)], limit=1).group_id
        
        config = self.sudo().search([('group_id', '=', user_group.id)], limit=1)
        
        return config.should_scan_follow_orders if config else False


    