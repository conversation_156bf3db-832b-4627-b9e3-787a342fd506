# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import ValidationError
from datetime import datetime
from odoo.osv import osv
from odoo.tools import config
from datetime import timedelta
import jwt
import os
from odoo import http
import base64
import zipfile
import io

class order_add_payout_order(models.TransientModel):
    _name = 'rb_delivery.add_payout_order'
    _description = "Add payout order Model"

    assign_to_business = fields.Many2one('rb_delivery.user', 'Business', domain=[("role_code",'=','rb_delivery.role_business')],required=True)

    amount = fields.Float('Amount',required=True)

    def create_order(self):
        if self.assign_to_business and self.amount:
            payout_order_type = self.env.ref('rb_delivery.cash_payout_order').id
            uid = self.env.user.partner_id.id
            amount = self.amount
            if self.amount >0:
                amount = self.amount* -1
            self.env['rb_delivery.order'].with_context(original_uid=uid,skeleton_order=True).sudo().create({'assign_to_business':self.assign_to_business.id,'cost':amount,'copy_total_cost':amount,'order_type_id':payout_order_type})
        return True
    

class order_multi_assign_to_business_wizard(models.TransientModel):
    _name = 'rb_delivery.multi_assign_to_business'
    _description = "Multi Assign To Agent Model"


    def _get_business_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_business')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]
    @api.model
    def default_business(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_business = user.has_group('rb_delivery.role_business')
        if is_business:
            del_user = self.env['rb_delivery.user'].search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False
    assign_to_business = fields.Many2one(
        'rb_delivery.user', 'Business', domain=_get_business_users, default=default_business)
    @api.multi
    def select_business(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        recs.write({'assign_to_business': self.assign_to_business.id})
        return True

class order_multi_assign_to_area_wizard(models.TransientModel):
    _name = 'rb_delivery.multi_area'
    _description = "Multi Area Model"

    customer_area = fields.Many2one('rb_delivery.area', 'Area', ondelete='restrict')

    @api.multi
    def select_area(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        if self.customer_area:
            recs.write({'customer_area':self.customer_area.id})
        return True


class order_multi_refresh_pricelist_wizard(models.TransientModel):
    _name = 'rb_delivery.multi_refresh_pricelist'
    _description = "Multi Refresh Pricelist Model"

    @api.multi
    def refresh_pricelist(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        for rec in recs:
            rec.with_context(write_compute_fields=True).get_customer_price()
        return True

class order_multi_assign_to_sub_area_wizard(models.TransientModel):
    _name = 'rb_delivery.multi_sub_area'
    _description = "Multi Sub Area Model"

    customer_sub_area = fields.Many2one('rb_delivery.sub_area',domain=[('show_in_create', '=', True)], string='Sub Area', ondelete='restrict')

    area_id = fields.Many2one(related="customer_sub_area.parent_id")

    @api.multi
    def select_sub_area(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        if self.area_id and self.customer_sub_area:
            recs.write({'customer_area':self.area_id.id,'customer_sub_area': self.customer_sub_area.id})
        return True


class order_multi_assign_to_agent_wizard(models.TransientModel):
    _name = 'rb_delivery.multi_assign_to_agent'
    _description = "Multi Assign To Agent Model"

    @api.model
    def default_driver(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_driver = user.has_group('rb_delivery.role_driver')
        if is_driver:
            del_user = self.env['rb_delivery.user'].search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False
    assign_to_agent = fields.Many2one(
        'rb_delivery.user', 'Agent', default=default_driver)

    @api.multi
    def select_agent(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        recs.write({'assign_to_agent': self.assign_to_agent.id})

        return True
    

#deprecated
class order_multi_compute_area_wizard(models.TransientModel):
    _name = 'rb_delivery.multi_compute_area'
    _description = "Multi compute area"

    @api.multi
    def select_area(self):
        recs = self.env['rb_delivery.multi_print_orders_money_collector'].browse(
            self._context.get('active_ids'))
        
        recs._compute_area_id()
        return True

class order_multi_assign_to_collector_wizard(models.TransientModel):
    _name = 'rb_delivery.multi_assign_to_collector'
    _description = "Multi Assign To Collector Model"

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    @api.model
    def default_driver(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_driver = user.has_group('rb_delivery.role_driver')
        if is_driver:
            del_user = self.env['rb_delivery.user'].search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False
    assign_to_collector = fields.Many2one(
        'rb_delivery.user', 'Assign To Collector', domain=_get_driver_users, default=default_driver)

    @api.multi
    def select_collector(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        recs.write({'assign_to_collector': self.assign_to_collector.id})

        return True

class order_create_money_collection_wizard(models.TransientModel):
    _name = 'rb_delivery.create_money_collection'
    _description = "Create Money Collection Model"

    def create_money_collection(self):
        context_type = self._context.get('default_report_type')
        active_ids = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
        collection_groups = {}
        for order in active_ids:
            if order.collection_id:
                collection_id = order.collection_id.id
                if collection_id not in collection_groups:
                    collection_groups[collection_id] = {'orders': [], 'message': ''}
                collection_groups[collection_id]['orders'].append(order)

        message = ''

        orders_not_in_collection = [order for order in active_ids if not order.collection_id]
        if orders_not_in_collection:
            orders_not_in_collection_sequences = [str(order.sequence) for order in orders_not_in_collection[:3]] + (['...'] if len(orders_not_in_collection) > 3 else [])
            message = _("Orders of sequences %s will be added to collection") % (', '.join(orders_not_in_collection_sequences))
            message = message


        warning = self.env['rb_delivery.create_money_collection_warning'].sudo().create({'text': message})

        context = {"orders_in_collection": [order.id for order in active_ids if order.collection_id],"orders_not_in_collection": [order.id for order in active_ids if not order.collection_id],
                "order_ids": [order.id for order in active_ids],
                "context_type": context_type, "name": self.name}

        return {
            'type': 'ir.actions.act_window',
            'name': _('Message'),
            'res_model': 'rb_delivery.create_money_collection_warning',
            'view_type': 'form',
            'view_mode': 'form',
            'target': 'new',
            'res_id': warning.id,
            'context': context
        }

    @api.model
    def _default_show_note(self):
        default_show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        return default_show_note

    @api.one
    def _compute_show_note(self):
        show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        self.show_note = show_note

    name = fields.Char('Note')

    show_note = fields.Boolean('Show Note', default=_default_show_note, compute="_compute_show_note", readonly=True)

class rb_delivery_money_collection_send_email(models.TransientModel):
    _name="rb_delivery.money_collection_send_email"
    _description = "Money Collection Send Email Model"

    @api.model
    def _get_company_user(self):
        company = self.env['res.company'].search([])
        return company[0].partner_id

    @api.model
    def _get_business_users(self):
        recs = self.env['rb_delivery.multi_print_orders_money_collector'].browse(
                self._context.get('active_ids'))
        businesses=[]
        for rec in recs:
            businesses = [(6, 0, [rec.sudo().business_id.id])]
        return businesses

    @api.model
    def _get_body(self):
        template = self.env['mail.template'].sudo().search([('model_id','=','rb_delivery.multi_print_orders_money_collector')])
        if template:
            body = template[0].body_html
            return body

    @api.model
    def _get_subject(self):
        template = self.env['mail.template'].sudo().search([('model_id','=','rb_delivery.multi_print_orders_money_collector')])
        if template:
            subject = template[0].subject
            return subject

    @api.model
    def _get_attachments(self):
        import base64
        recs = self.env['rb_delivery.multi_print_orders_money_collector'].browse(
                self._context.get('active_ids'))
        attachments=[]
        for rec in recs:
            data = self.env['rb_delivery.report_job_queue'].compute_totals('rb_delivery.multi_print_orders_money_collector_print_report', str(rec.id))
            pdf = self.env['ir.actions.report'].search([('report_name','=','rb_delivery.multi_print_orders_money_collector_print_report'),('model','=','rb_delivery.multi_print_orders_money_collector')]).sudo().render_qweb_pdf(rec.id, data)
            fmt = "%Y-%m-%d"
            create_date = datetime.strftime(rec.create_date, fmt)

            business = rec.sudo().business_id
            business_name = business.username if not business.commercial_name else business.commercial_name
            file_name = f"{business_name}_{create_date}"
            file_name = file_name.replace('.','_')
            data_id = self.env['ir.attachment'].create({
                'name': file_name,
                'type': 'binary',
                'datas': base64.encodestring(pdf[0]),
                'res_model': 'rb_delivery.multi_print_orders_money_collector',
                'res_id': rec.id,
                'datas_fname':file_name
            })
            attachments.append(data_id.id)
        attachment_ids = [(6, 0, attachments)]
        return attachment_ids

    sender_id = fields.Many2one('res.partner','Sender',default=_get_company_user)

    partner_ids = fields.Many2many(comodel_name = 'rb_delivery.user',
        string = 'Recipients',
        relation = 'recipients_money_collection_item',
        column1 = 'money_collection_id',
        column2 = 'recipients_id',default=_get_business_users)

    subject = fields.Char("Subject", default=_get_subject)

    body = fields.Html("Body", default=_get_body)

    attachment_ids = fields.Many2many(comodel_name = 'ir.attachment',
        string = 'Attachments',
        relation = 'attachments_money_collection_item',
        column1 = 'money_collection_id',
        column2 = 'attachment_id',default=_get_attachments)

    def send_email(self):
        recs = self.env['rb_delivery.multi_print_orders_money_collector'].browse(
                self._context.get('active_ids'))
        company_email = self.sender_id.email
        template_obj = self.env['mail.mail']
        emails = []
        attachments = []
        collections = []
        for rec in recs:
            if rec.business_id.email:
                emails.append(rec.sudo().business_id.email)
                attach = self.env['ir.attachment'].search([('res_id','=',rec.id),('res_model','=','rb_delivery.multi_print_orders_money_collector')])
                attachments.append(attach[0].id)
                collections.append(rec)
        index = 0
        for email in emails:
            subject = self.subject
            body = self.body
            email_from = '"'+self.sender_id.name+'" <'+company_email+'>'
            template_data = {
                'subject': subject,
                'body_html': body,
                'email_from': email_from,
                'email_to': email}
            template_data['attachment_ids'] = [(6, 0, [attachments[index]])]
            template_id = template_obj.create(template_data)
            template_id.send()
            user = self.env['res.users'].sudo().search([('id','=',self._uid)])
            collections[index].write({'email_sent':True})
            collections[index].message_post(body=_("An email was sent by %s") % (user.name))
            index = index + 1

class rb_delivery_collection_detach_order(models.TransientModel):
    _name="rb_delivery.collection_detach_order"
    _description = "Collection Detach Order Model"

    @api.multi
    def detach_orders(self):
        recs = self.env['rb_delivery.multi_print_orders_money_collector'].browse(
            self._context.get('active_ids'))
        recs.write({'order_ids':[(6,0,[])]})
        return True
class money_collection_change_agent_wizard(models.TransientModel):
    _name = 'rb_delivery.money_collection_change_agent_wizard'
    _description = "Money Collection Change Agent Wizard Model"

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    @api.model
    def default_driver(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_driver = user.has_group('rb_delivery.role_driver')
        if is_driver:
            del_user = self.env['rb_delivery.user'].search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False
    assign_to_agent = fields.Many2one(
        'rb_delivery.user', 'Agent', domain=_get_driver_users, default=default_driver)

    @api.multi
    def select_agent(self):
        collections = self.env['rb_delivery.multi_print_orders_money_collector'].browse(
            self._context.get('active_ids'))
        collections.write({'driver_id': self.assign_to_agent.id})
        return True



class order_select_money_collection_state_wizard(models.TransientModel):
    _name = 'rb_delivery.select_money_collection_state'
    _description = "Select Money Collection State Model"

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    state = fields.Many2one('rb_delivery.status', string="Status")

    all_status_ids = fields.Many2many(
        'rb_delivery.status',
        string="All statuses",
        compute="_load_all_status_ids",
        copy=False
    )
    @api.depends('state')
    def _load_all_status_ids(self):
        for rec in self:
            ids = rec._get_status_ids()
            rec.all_status_ids = [(6, 0, ids)]
    @api.model
    def _get_status_ids(self):
        user = self.env.user
        is_admin = user._is_admin() or user.has_group('rb_delivery.role_super_manager')
        user_group_ids = user.groups_id.ids
        user_group = (
            self.env.ref('rb_delivery.role_super_manager').id
            if is_admin
            else user.rb_user.group_id.id
        )
        collection_ids = self._context.get('active_ids', [])
        if len(collection_ids):
            collections = self.env['rb_delivery.multi_print_orders_money_collector'].browse(collection_ids)
            states = self.env['rb_delivery.status'].sudo().search([('status_type','=','olivery_collection'),('collection_type','=','collection')])
            next_state_ids = {
                next_state.id
                for collection in collections
                for next_state in states
                if is_admin or bool(set(collection.state_id.pass_lock_allowed_group_ids.ids) & set(user_group_ids)) or (next_state.id in collection.state_id.next_state_ids.ids and user_group in next_state.role_action_status_ids.ids)
            }
            return list(next_state_ids)
        else:
            return []

    agent = fields.Many2one( 'rb_delivery.user', 'Agent', track_visibility="on_change", domain=_get_driver_users)
    required_agent = fields.Boolean('Show Agent Required', default=False)
    show_agent = fields.Boolean('Show Agent', default=False)
    closing_date = fields.Datetime(string="Closing Date",track_visibility="on_change")
    show_closing_date = fields.Boolean(string="Show Closing Date",track_visibility="on_change")
    required_closing_date = fields.Boolean(string="Required Closing Date",track_visibility="on_change")

    show_closing_date = fields.Boolean(string="Show Closing Date",track_visibility="on_change")

    required_closing_date = fields.Boolean(string="Required Closing Date",track_visibility="on_change")

    # inherit module[olivery_branch_collection]
    @api.onchange('state')
    def change_state(self):
        if self.state:
            state = self.state
            optional_status_actions = state.status_action_optional_related_fields
            required_status_actions = state.status_action_required_aditional_fields
            self.show_agent = False
            self.required_agent = False
            self.agent=False
            self.show_closing_date = False
            self.closing_date = False
            self.required_closing_date = False
            if state and optional_status_actions:
                for status_action in optional_status_actions:
                    if status_action.name == 'collection_show_agent':
                        self.show_agent = True
                    elif status_action.name == 'show_closing_date':
                        self.show_closing_date = True
            if state and required_status_actions:
                for status_action in required_status_actions:
                    if status_action.name == 'collection_show_agent':
                        self.show_agent = True
                        self.required_agent = True
                    elif status_action.name == 'show_closing_date':
                        self.show_closing_date = True
                        self.required_closing_date = True


    @api.multi
    def select_state(self):
        collection_ids = self.env['rb_delivery.multi_print_orders_money_collector'].browse(
            self._context.get('active_ids'))
        vals = {}
        if self.state:
            vals['state'] = self.state.name
        if self.agent:
            vals['driver_id'] = self.agent.id
        if self.closing_date:
            vals['close_date'] = self.closing_date
        if vals:
            return collection_ids.write(vals)

class user_select_unarchive_order_wizard(models.TransientModel):
    _name = 'rb_delivery.order_select_unarchive'
    _description = "Order Select Unarchive Model"

    @api.multi
    def select_unarchive(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        orders = self.env['rb_delivery.order'].search([('active','=',False),('id','in',recs.ids)])
        orders.write({'active':True})
        return True

class user_select_archive_order_wizard(models.TransientModel):
    _name = 'rb_delivery.order_select_archive'
    _description = "Order Select Archive Model"

    @api.multi
    def select_archive(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        recs.write({'active':False})
        return True

class set_to_delete_order_wizard(models.TransientModel):
    _name = 'rb_delivery.set_to_delete'
    _description = "Order Set to delete Model"

    def get_order_vals(self,order):
        sequence = order.sequence + '-d'
        order_vals = {'sequence':sequence}
        if order.is_replacement:
            order_vals['is_replacement'] = False
            order_vals['clone_reference'] = False
        return order_vals


    @api.multi
    def select_change_sequence(self):
        recs = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
        username = self.env.user.name
        for rec in recs:
            message=_("The order was updated by %s, the feature was used to change sequence for fixing some issues like cloning, this will allow user to do another clone for the original cloned order.")%(username)
            order_vals = self.get_order_vals(rec)
            data = {'uid':self._uid,'message':message,'records':rec,'values':order_vals,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)
        return True

class user_select_archive_collection_wizard(models.TransientModel):
    _name = 'rb_delivery.money_collection_select_archive'
    _description = "Collection Select Archive Model"

    @api.multi
    def select_archive(self):
        archive = self._context.get('archive')
        model = self._context.get('model')
        domain_field = 'collection_id'
        model_name = _("Money collection")
        if model == 'rb_delivery.multi_print_orders_money_collector':
            domain_field = 'collection_id'
            model_name = _("Money collection")
        elif model == 'rb_delivery.runsheet':
            domain_field = 'runsheet_collection_id'
            model_name = _("Runsheet")
        elif model == 'rb_delivery.returned_money_collection':
            domain_field = 'returned_collection_id'
            model_name = _("Returned collection")
        elif model == 'rb_delivery.agent_money_collection':
            domain_field = 'agent_collection_id'
            model_name = _("Agent collection")
        elif model == 'rb_delivery.agent_returned_collection':
            domain_field = 'agent_returned_collection_id'
            model_name = _("Agent returned collection")
        user = self.env['res.users'].search([('id','=',self._uid)])
        if user and (user.has_group('rb_delivery.role_collection_archiver')):
            recs = self.env[model].browse(
                self._context.get('active_ids'))
            if archive :
                for rec in recs:
                    if rec.order_ids and len(rec.order_ids) > 0:
                        orders = rec.order_ids
                        orders.write({'active':False})

                recs.write({'active':False})
            else:
                for rec in recs:
                    orders = self.env['rb_delivery.order'].search([(domain_field,'=',rec.id),('active','=',False)])
                    if orders and len(orders) > 0:
                        orders.write({'active':True})
                recs.write({'active':True})
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(251,self.id,{'collection_type': model_name})
            #raise ValidationError(_('You are not allowed to archive collection'))
        return True

class order_select_state_wizard(models.TransientModel):
    _name = 'rb_delivery.select_state'
    _description = "Select State Model"

    def _show_totals(self):
        show_totals = self.env['rb_delivery.client_configuration'].get_param('show_totals_in_select_state_wizard')
        return show_totals


    state = fields.Selection(selection='get_status',track_visibility="on_change",string="Status",default="")

    state_id = fields.Char('State id')

    total_agent_cost = fields.Float('Total Agent Cost',readonly=True)

    total_required_to_company = fields.Float('Total Required to company',readonly=True)

    total_delivery_cost = fields.Float('Total Delivery Fee',readonly=True)

    total_required_from_business = fields.Float('Total Net value',readonly=True)

    total_money_collection_cost = fields.Float('Total COD',readonly=True)

    reschedule_date = fields.Datetime(string="Scheduled Delivery",track_visibility="on_change")

    agent = fields.Many2one( 'rb_delivery.user', 'Agent', track_visibility="on_change")

    note = fields.Text('Note', track_visibility="on_change")

    payment_type = fields.Many2one('rb_delivery.payment_type', 'Payment Method',track_visibility="on_change")

    payment_type_two = fields.Many2one('rb_delivery.payment_type', 'Second payment method',track_visibility="on_change")

    customer_payment = fields.Char("Customer payment")

    customer_payment_two = fields.Char("Second customer payment")

    reject_reason = fields.Many2one('rb_delivery.reject_reason', 'Reject Reason',track_visibility="on_change")

    order_action = fields.Many2one('rb_delivery.order_action', 'Required Action')

    stuck_comment = fields.Text('Stuck Comment')

    extra_agent_cost = fields.Float('Extra agent cost')

    show_extra_agent_cost = fields.Boolean('Show Extra agent cost',default=False)

    required_extra_agent_cost = fields.Boolean('Required Extra agent cost',default=False)

    show_reschedule_date = fields.Boolean('Show Reschedule Date', default=False)

    show_reject_reason = fields.Boolean('Show Reschedule Date', default=False)

    show_order_action = fields.Boolean('Show Required action', default=False)

    show_payment_type = fields.Boolean('Show Payment Type', default=False)

    show_payment_type_two = fields.Boolean('Show Payment Type', default=False)

    show_agent = fields.Boolean('Show Agent', default=False)

    show_note = fields.Boolean('Show Agent', default=False)

    show_customer_payment = fields.Boolean('Show Customer Payment', default=False)

    show_customer_payment_two = fields.Boolean('Show Customer Payment', default=False)

    required_reschedule_date = fields.Boolean('Show Reschedule Date Required', default=False)

    required_reject_reason = fields.Boolean('Show Reschedule Date Required', default=False)

    required_order_action = fields.Boolean('Show Required Action Required', default=False)

    required_payment_type = fields.Boolean('Show Payment Type Requried', default=False)

    required_payment_type_two = fields.Boolean('Show Payment Type Requried', default=False)

    required_agent = fields.Boolean('Show Agent Required', default=False)

    required_note = fields.Boolean('Show Note Required', default=False)

    required_customer_payment = fields.Boolean('Show Customer Payment Required', default=False)

    required_customer_payment_two = fields.Boolean('Show Customer Payment Required', default=False)

    show_totals = fields.Boolean('Show totals',default=_show_totals)


    @api.onchange('payment_type')
    def _onchange_payment_type(self):
        """Exclude selected payment_type from payment_type_two"""
        if self.payment_type:
            return {'domain': {'payment_type_two': [('id', '!=', self.payment_type.id)]}}
        return {'domain': {'payment_type_two': []}}

    @api.onchange('payment_type_two')
    def _onchange_payment_type_two(self):
        """Exclude selected payment_type_two from payment_type"""
        if self.payment_type_two:
            return {'domain': {'payment_type': [('id', '!=', self.payment_type_two.id)]}}
        return {'domain': {'payment_type': []}}


    @api.model
    def load_views(self, views, options=None):
        orders_ids = self._context.get('orders') or self._context.get('active_ids', [])
        orders = self.env['rb_delivery.order'].browse(orders_ids)

        inactive_orders = [order.sequence for order in orders if not order.active]

        if inactive_orders:
            self.env['rb_delivery.error_log'].raise_olivery_error(295, self.id, {'order_sequences': ', '.join(inactive_orders)})
        else:
            return super(order_select_state_wizard, self).load_views(views, options)

    @api.onchange('state')
    def change_state(self):

        order_state = self.env['rb_delivery.status'].sudo().search([('name','=',self.state),'|',('status_type','=',False),('status_type','=','olivery_order')])
        self.state_id = order_state.id
        optional_status_actions = order_state.status_action_optional_related_fields
        required_status_actions = order_state.status_action_required_aditional_fields
        self.show_reschedule_date = False
        self.show_reject_reason = False
        self.show_order_action = False
        self.show_agent = False
        self.show_note = False
        self.show_payment_type = False
        self.show_customer_payment = False
        self.show_payment_type_two = False
        self.show_customer_payment_two = False
        self.required_reschedule_date = False
        self.required_order_action = False
        self.required_agent = False
        self.required_note = False
        self.required_payment_type = False
        self.required_customer_payment = False
        self.required_payment_type_two = False
        self.required_customer_payment_two = False
        self.agent=False
        self.required_extra_agent_cost = False
        self.show_extra_agent_cost = False
        self.extra_agent_cost = 0
        if self._context.get('orders'):
            recs = self.env['rb_delivery.order'].browse(
                self._context.get('orders'))
        else:
            recs = self.env['rb_delivery.order'].browse(
                self._context.get('active_ids'))

        if self.show_totals:
            if len(recs.ids) == 1:
                domain = [('id', '=', recs.id)]
            else:
                domain = [('id', 'in', recs.ids)]

            result = self.env['rb_delivery.order'].read_group(domain, fields=[
                'agent_cost',
                'money_collection_cost',
                'delivery_cost',
                'required_from_business',
                'required_to_company'
            ], groupby=[])

            self.total_agent_cost = 0
            self.total_money_collection_cost = 0
            self.total_delivery_cost = 0
            self.total_required_from_business = 0
            self.total_required_to_company = 0

            for group in result:
                self.total_required_to_company += group['required_to_company']
                self.total_agent_cost += group['agent_cost']
                self.total_money_collection_cost += group['money_collection_cost']
                self.total_delivery_cost += group['delivery_cost']
                self.total_required_from_business += group['required_from_business']

        if order_state and optional_status_actions:
            for status_action in optional_status_actions:
                if status_action.name == 'show_reschedule_date':
                    self.show_reschedule_date = True
                if status_action.name == 'show_agent':
                    self.show_agent = True
                if status_action.name == 'show_note':
                    self.show_note = True
                if status_action.name == 'show_payment_type':
                    self.show_payment_type = True
                if status_action.name == 'show_customer_payment':
                    self.show_customer_payment = True
                if status_action.name == 'show_payment_type_two':
                    self.show_payment_type_two = True
                if status_action.name == 'show_customer_payment_two':
                    self.show_customer_payment_two = True
                if status_action.name == 'show_reject_reason':
                    self.show_reject_reason = True
                if status_action.name == 'show_order_action':
                    self.show_order_action = True
                if status_action.name == 'show_extra_agent_cost':
                    self.show_extra_agent_cost = True


        if order_state and required_status_actions:
            for status_action in required_status_actions:
                if status_action.name == 'show_reschedule_date':
                    self.show_reschedule_date = True
                    self.required_reschedule_date = True
                if status_action.name == 'show_agent':
                    self.show_agent = True
                    self.required_agent = True
                if status_action.name == 'show_note':
                    self.show_note = True
                    self.required_note = True
                if status_action.name == 'show_payment_type':
                    self.show_payment_type = True
                    self.required_payment_type = True
                if status_action.name == 'show_customer_payment':
                    self.show_customer_payment = True
                    self.required_customer_payment = True
                if status_action.name == 'show_payment_type_two':
                    self.show_payment_type_two = True
                    self.required_payment_type_two = True
                if status_action.name == 'show_customer_payment_two':
                    self.show_customer_payment_two = True
                    self.required_customer_payment_two = True
                if status_action.name == 'show_reject_reason':
                    self.show_reject_reason = True
                    self.required_reject_reason = True
                if status_action.name == 'show_order_action':
                    self.show_order_action = True
                    self.required_order_action = True
                if status_action.name == 'show_extra_agent_cost':
                    self.show_extra_agent_cost = True
                    self.required_extra_agent_cost = True

    def get_status(self):
        group_id = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])
        for status in next_statuses:
            if group_id:
                if status.role_action_status_ids and len(status.role_action_status_ids)>0:
                    for role in status.role_action_status_ids:
                        if role.id == group_id.id:
                            status_list.append((status.name,status.title))
            else:
                    status_list.append((status.name,status.title))
        return status_list

    #inherit module [storex_modules]
    #inherit module [olivery_cargo]
    #inherit module [olivery_compound_order]
    #inherit module [olivery_delivery_repetition]
    #inherit module [olivery_warehouse]
    @api.multi
    def btn_show_dialog_box(self):
        if self._context.get('orders'):
            recs = self.env['rb_delivery.order'].browse(
                self._context.get('orders'))
        else:
            recs = self.env['rb_delivery.order'].browse(
                self._context.get('active_ids'))

        ids = recs.ids
        values = {}
        collection_values = {}
        agent_collections = []
        agent_collection_counter = 0
        agent_collection_order_sequences = ''
        agent_collection_sequences = ''
        collection_values['returned_agent_collection'] = False
        collection_values['agent_collection'] = False
        returned_agent_collections = []
        returned_agent_collection_counter = 0
        returned_agent_collection_order_sequences = ''
        returned_agent_collection_sequences = ''
        for rec in recs:
            agent_collection_id = rec.sudo().agent_collection_id
            agent_returned_collection_id = rec.sudo().agent_returned_collection_id
            if (agent_collection_id) and agent_collection_counter < 5:
                agent_collection_counter += 1
                if agent_collection_id.sequence not in agent_collections:
                    collection_values['agent_collection'] = True
                    agent_collection_order_sequences += "<p class=\"o_white_pill\">" + agent_collection_id.sequence + "</p>"
                    agent_collections.append(agent_collection_id.sequence)
                agent_collection_sequences += "<p class=\"o_white_pill\">" + rec.sequence + "</p>"
            if (agent_returned_collection_id) and returned_agent_collection_counter < 5:
                returned_agent_collection_counter += 1
                if agent_returned_collection_id.sequence not in returned_agent_collections:
                    collection_values['returned_agent_collection'] = True
                    returned_agent_collection_order_sequences += "<p class=\"o_white_pill\">" + agent_returned_collection_id.sequence + "</p>"
                    returned_agent_collections.append(agent_returned_collection_id.sequence)
                returned_agent_collection_sequences += "<p class=\"o_white_pill\">" + rec.sequence + "</p>"


        collection_values['agent_collection_sequences'] = agent_collection_sequences
        collection_values['agent_collection_order_sequences'] = agent_collection_order_sequences
        collection_values['returned_agent_collection_sequences'] = returned_agent_collection_sequences
        collection_values['returned_agent_collection_order_sequences'] = returned_agent_collection_order_sequences

        values['rec_len'] = len(recs)
        status_titles = self.env['rb_delivery.status'].search([
            ('name', 'in', recs.mapped('state')),
            '|', ('status_type', '=', False), ('status_type', '=', 'olivery_order')
        ])
        values['status_titles'] = status_titles

        agents = recs.sudo().filtered(lambda r: r.assign_to_agent).mapped('assign_to_agent.username')
        values['agents'] = agents
        if self.state:
            current_status = self.env['rb_delivery.status'].search([
                ('name', '=', self.state),
                '|', ('status_type', '=', False), ('status_type', '=', 'olivery_order')
            ])
            values['current_status'] = current_status
        values['reschedule_date'] = self.reschedule_date
        warning_text = self.get_warning_text(values)
        collection_text = self.get_collection_text(collection_values)
        agent_collection_text = self.get_agent_collection_text(self.state,recs)
        customer_payment = ''
        if self.show_customer_payment and (self.customer_payment or self.customer_payment_two):
            customer_payment = str(float(self.customer_payment or 0.0) + float(self.customer_payment_two or 0.0))

        value = self.env['display.dialog.box'].sudo().create({
            'warning_text':warning_text,
            'collection_text':collection_text,
            'agent_collection_text':agent_collection_text
        })
        context = {'state':self.state,'agent':self.agent.id, 'note':self.note, 'orders':ids,'reschedule_date':self.reschedule_date,'customer_payment_one':self.customer_payment,'customer_payment_two':self.customer_payment_two,'customer_payment':customer_payment,'payment_type':self.payment_type.id,'payment_type_two':self.payment_type_two.id,'reject_reason':self.reject_reason.id,'order_action':self.order_action.id,'stuck_comment':self.stuck_comment,'extra_agent_cost':self.extra_agent_cost}
        return{
            'type':'ir.actions.act_window',
            'name': "<span><img src=\"/rb_delivery/static/src/img/question_header_circle.png\" class=\"o_header_img\"/></span> <span class=\"o_header_title\">" + _('Confirmation Message') + "</span>",
            'res_model':'display.dialog.box',
            'view_type':'form',
            'view_mode':'form',
            'target':'new',
            'res_id':value.id,
            'context':context
        }
    
    def get_agent_collection_text(self,status,orders):
        confs = self.env['rb_delivery.client_configuration'].get_param(['statues_to_prevent_change_to_if_not_from_agent_collection','show_warning_if_not_from_agent_collection'])
        statuses_to_prevent = confs['statues_to_prevent_change_to_if_not_from_agent_collection']
        show_warning = confs['show_warning_if_not_from_agent_collection']

        if not statuses_to_prevent or not show_warning:
            return ''
        state = self.env['rb_delivery.status'].search([('name','=',status),'|', ('status_type', '=', False), ('status_type', '=', 'olivery_order')],limit=1)
        if state[0].id not in statuses_to_prevent:
            return ''
        
        orders_with_no_agent_collection = ', '.join(orders.filtered(lambda order: not order.agent_collection_id).mapped('sequence'))
        orders_with_agent_collection = ', '.join(orders.filtered(lambda order: order.agent_collection_id).mapped('sequence'))
        message = _("the status you are trying to change to is in configuration 'statues_to_prevent_change_to_if_not_from_agent_collection'. Do you confirm you want to change the status?")
        if orders_with_no_agent_collection:
            message = _("Orders of sequences %s are not in any agent collection, and ")%orders_with_no_agent_collection + message
        if orders_with_agent_collection:
            message = _("Orders of sequences %s are in agent collection ")%orders_with_agent_collection + message
        return message

    def get_collection_text(self, values):
        if values:
            order_sequence = _("Orders of Sequences")
            agent_collection_header = _("Agent Collection Sequences")
            returned_agent_collection_header = _("Returned Agent Collection Sequences")
            exist_in = _("Exist in")
            if values['agent_collection']:
                agent_collection_sequences = values['agent_collection_sequences']
                agent_collection_order_sequences = values['agent_collection_order_sequences']
                agent_collection = (
                    "<div class=\"row o_collection_wrapper\">"
                    "<div class=\"col-6 text-center\"><span class=\"o_display_text\" style=\"font-weight: bold;\">%s</span><br><br>%s</div>"
                    "<div class=\"o_collection_line\" style=\"margin-top: 10px;\"></div>"
                    "<div class=\"o_collection_wordwrapper\"><div class=\"o_collection_word\">%s</div></div>"
                    "<div class=\"col-6 text-center\"><span class=\"o_display_text\" style=\"font-weight: bold;\">%s</span><br><br>%s</div>"
                    "</div>"
                ) % (order_sequence, agent_collection_sequences, exist_in, agent_collection_header, agent_collection_order_sequences)
            else:
                agent_collection = ''

            if values['returned_agent_collection']:
                returned_agent_collection_sequences = values['returned_agent_collection_sequences']
                returned_agent_collection_order_sequences = values['returned_agent_collection_order_sequences']
                returned_agent_collection = (
                    "<div class=\"row o_collection_wrapper\">"
                    "<div class=\"col-6 text-center\"><span class=\"o_display_text\" style=\"font-weight: bold;\">%s</span><br><br>%s</div>"
                    "<div class=\"o_collection_line\" style=\"margin-top: 10px;\"></div>"
                    "<div class=\"o_collection_wordwrapper\"><div class=\"o_collection_word\">%s</div></div>"
                    "<div class=\"col-6 text-center\"><span class=\"o_display_text\" style=\"font-weight: bold;\">%s</span><br><br>%s</div>"
                    "</div>"
                ) % (order_sequence, returned_agent_collection_sequences, exist_in, returned_agent_collection_header , returned_agent_collection_order_sequences)
            else:
                returned_agent_collection = ''

            collection_text = agent_collection + returned_agent_collection
            return collection_text
        else:
            return ''

    def get_warning_text(self, values):
        html_text = ''
        try_change = _('You are trying to change ')
        try_change_status = _('You are trying to change from statuses ')
        try_change_driver = _('You are trying to change from drivers ')
        try_change_date = _("You are trying to change Scheduled Delivery ")

        if values and values.get('rec_len'):
            html_text += "<div class=\"o_display_text\" style=\"font-weight: bold;\">%s<p class=\"o_white_pill\">%s</p><p class=\"o_display_text\" style=\"font-weight: bold;\">%s</p></div><br>" % (
                try_change, str(values['rec_len']), _(' records'))

        if values and values.get('status_titles') and values.get('current_status'):
            status_titles_html = ''.join("<p class=\"o_white_pill\">%s</p>" % state.title for state in values['status_titles'])
            html_text += "<div class=\"o_display_text\" style=\"margin-top: 15px;\">%s%s%s<p class=\"o_white_pill\">%s</p></div><br>" % (
                try_change_status, status_titles_html, _(' to '), values['current_status'].title)

        if values and values.get('agents') and self.agent and self.agent.username:
            agents_html = ''.join("<p class=\"o_white_pill\">%s</p>" % agent for agent in values['agents'])
            html_text += "<div class=\"o_display_text\" style=\"margin-top: 15px;\">%s%s%s<p class=\"o_white_pill\">%s</p></div><br>" % (
                try_change_driver, agents_html, _(' to '), self.agent.username)

        if values and values.get('reschedule_date') and values['reschedule_date'] is not False:
            html_text += "<div class=\"o_display_text\" style=\"margin-top: 15px;\">%s<p class=\"o_white_pill\">%s</p></div><br>" % (
                try_change_date, str(values['reschedule_date']))

        return html_text

class order_public_link_wizard(models.TransientModel):
    _name = 'rb_delivery.public_order_link'
    _description = "Public Order Link Model"

    @api.model
    def default_business(self):
        user = self.env['res.users'].sudo().search([('id', '=', self._uid)])
        is_business = user.sudo().has_group('rb_delivery.role_business')
        if is_business:
            del_user = self.env['rb_delivery.user'].sudo().search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False


    business_user = fields.Many2one('rb_delivery.user', 'Business',required=True, default=default_business,track_visibility="on_change",domain="[('role_code','=','rb_delivery.role_business')]")

    order_form_link = fields.Char('Public Order Link',compute='_generate_link')

    token = fields.Char('Token',compute='_generate_link')

    show_link = fields.Boolean('Show Token')

    def create_token(self):
        exp = datetime.utcnow() + timedelta(days=90)
        payload = {
            'exp': exp,
            'iat': datetime.utcnow(),
            'db': config['db_name'],
            'business':self.business_user.id,
            'is_multi_order' : self.env['rb_delivery.client_configuration'].get_param('is_public_link_multi_orders'),
            'view_home_page' : self.env['rb_delivery.client_configuration'].get_param('view_home_page'),
        }

        token = jwt.encode(
            payload,
            str(os.environ.get('ODOO_JWT_KEY')),
            algorithm='HS256'
        )


        return token

    @api.depends('business_user')
    def _generate_link(self):
        token = self.create_token()
        domain = self.env['rb_delivery.client_configuration'].get_param('public_link_domain')
        self.token = token
        self.order_form_link = f'{domain}?token={token}'
        self.show_link = False

    def save_token(self):
        is_multi_orders_link = self.env['rb_delivery.client_configuration'].get_param('is_public_link_multi_orders')
        self.env['rb_delivery.public_link_tokens'].sudo().create({'token':self.token, 'is_multi_order':is_multi_orders_link})
        self.show_link = True
        return{
            'type':'ir.actions.act_window',
            'res_model':'rb_delivery.public_order_link',
            'view_mode':'form',
            'res_id':self.id,
            'target':'new'
        }

class order_create_replacement_wizard(models.TransientModel):
    _name = 'rb_delivery.create_replacement'
    _description = "Create Replacement Model"


    @api.multi
    def create_replacement(self):
        recs = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))

        recs.with_context(create_replacement_action=True).write({'replacement_order': True})
        return True

class order_change_to_previous_status(models.TransientModel):
    _name = 'rb_delivery.change_to_previous_status'
    _description = "Change To Previous Status Model"

    def _default_text(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        text = _("Are you sure you want to change\n")
        for rec in recs:
            if rec.previous_status:
                current_state = self.env['rb_delivery.status'].search([('name','=',rec.state),'|',('status_type','=',False),('status_type','=','olivery_order')])
                prev_state = self.env['rb_delivery.status'].search([('name','=',rec.previous_status),'|',('status_type','=',False),('status_type','=','olivery_order')])
                text = text + _("From status ") + current_state.title + _(" to ")+prev_state.title+'\n'
        return text
    text = fields.Text('Text', default=_default_text,readonly="1")

    @api.multi
    def change_to_previous_status(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        for rec in recs:
            if rec.previous_status:
                rec.write({'state':rec.previous_status,'previous_status':rec.state})

        return True

class order_change_to_previous_agent(models.TransientModel):
    _name = 'rb_delivery.change_to_previous_agent'
    _description = "Change To Previous Agent Model"

    def _default_text(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        text = _("Are you sure you want to change\n")
        for rec in recs:
            if rec.previous_agent:
                if rec.assign_to_agent:
                    text = text + _("From agent ") + rec.assign_to_agent.username + _(" to ")+rec.previous_agent.username+'\n'
                else:
                    text = text + _("Agent to ")+rec.previous_agent.username+'\n'
        return text
    text = fields.Text('Text', default=_default_text,readonly="1")

    @api.multi
    def change_to_previous_agent(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        for rec in recs:
            if rec.previous_agent:
                if rec.assign_to_agent:
                    rec.write({'assign_to_agent':rec.previous_agent.id,'previous_agent':rec.assign_to_agent.id})
                else:
                    rec.write({'assign_to_agent':rec.previous_agent.id})

        return True

class rb_delivery_notify_orders(models.TransientModel):

    _name = 'rb_delivery.notify_orders'
    _description = "Notify Orders Model"
    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    header = fields.Char('Message Title')

    message = fields.Text(string="Message")

    is_sms = fields.Boolean("Sms", default=True)


    def notify(self):
        users = []
        mobile_numbers = []
        emails=[]
        players=[]
        is_sms = self.is_sms
        orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))

        for order in orders:
            users = []
            mobile_numbers = []
            users.append(order.assign_to_business)
            if order.customer_mobile:
                mobile_numbers.append(str(order.customer_mobile))
            try:
                self.env['rb_delivery.notification_center'].notification(users, emails, players, mobile_numbers, self.header, self.message, is_sms, False, False,order,order.sequence,'rb_delivery.order',notification_group_type="OrderGroup")
            except:
                pass

class rb_delivery_notify_business(models.TransientModel):

    _name = 'rb_delivery.notify_business'
    _description = "Notify Business Model"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    header = fields.Char('Message Title')

    message = fields.Text(string="Message")

    template = fields.Many2one('mail.template', 'Template', domain=[('model_id','=','rb_delivery.order')])

    notification_type = fields.Selection([
        ('is_email','Email'),
        ('is_notification','Notification'),
        ('is_sms','Sms')
    ],default='is_notification')


    def notify(self):
        players = []
        users = []
        mobile_numbers = []
        emails = []
        is_sms = False
        is_notification = False
        is_email = False
        if self.template:
            self.header = self.template.subject
            self.message = self.template.body_html
        orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
        if self.notification_type == 'is_email':
            is_email = True
        if self.notification_type == 'is_notification':
            is_notification = True
        if self.notification_type == 'is_sms':
            is_sms = True
        for order in orders:
            users = []
            players = []
            emails = []
            mobile_numbers = []
            users.append(order.assign_to_business)
            if order.assign_to_business.player_id:
                players.append(str(order.assign_to_business.player_id))
            if order.assign_to_business.email:
                emails.append(order.assign_to_business.email)
            if order.assign_to_business.mobile_number:
                mobile_numbers.append(order.assign_to_business.mobile_number)
            try:
                self.env['rb_delivery.notification_center'].notification(users, emails, players, mobile_numbers, self.header, self.message, is_sms, is_email, is_notification,order,order.sequence,'rb_delivery.order',notification_group_type="OrderNotifyBusiness")
            except:
                pass

class order_create_returned_wizard(models.TransientModel):

    _name = 'rb_delivery.create_returned'
    _description = "Create Returned Model"

    @api.multi
    def create_returned(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        recs.write({'returned_order':True})

        return True

class order_multi_select_reschedule_date_wizard(models.TransientModel):
    _name = 'rb_delivery.multi_select_reschedule_date'
    _description = "Multi Select Reschedule Date Model"

    reschedule_date = fields.Datetime(string="Scheduled Delivery",track_visibility="on_change")


    @api.multi
    def select_reschedule_date(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        recs.write({'state':'reschedule','reschedule_date': self.reschedule_date})

        return True



class order_create_paid_collection_wizard(models.TransientModel):
    _name = 'rb_delivery.create_paid_collection'
    _description = "Create Paid Collection Model"


    @api.multi
    def create_pre_paid_collection(self):

        orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))

        allowed_statuses = self.env['rb_delivery.client_configuration'].get_param('ability_to_create_pre_paid_collection')
        allowed_status_names = self.env['rb_delivery.status'].browse(allowed_statuses).mapped('name')

        not_allowed_orders = []
        orders_in_collection = []
        orders_to_create_collection = []
        paid_orders = []
        # Separate orders based on status and collection
        for order in orders:
            if order.state not in allowed_status_names:
                not_allowed_orders.append(order.sequence)
            elif order.collection_id:
                orders_in_collection.append(order)
            elif order.is_prepaid_order:
                paid_orders.append(order.sequence)
            else:
                orders_to_create_collection.append(order.id)

        # Check for orders with disallowed status
        if not_allowed_orders and len(not_allowed_orders) > 0:
            sequences = ', '.join(map(str, not_allowed_orders[:3])) + ('...' if len(not_allowed_orders) > 3 else '')
            #message = _('Orders with sequence %s will not be created due to status restrictions, to be able to create pre paid collection order state should be in ability_to_create_pre_paid_collection configuration')%(sequences)
            self.env['rb_delivery.error_log'].raise_olivery_error(252,self.id,{'sequence':sequences})
            #raise ValidationError(message)
        if paid_orders and len(paid_orders) > 0:
            sequences = ', '.join(paid_orders)
            #message = _('Orders with sequence %s will not be created due to financial state restrictions, to be able to create pre paid collection order financial state should not be paid')%(sequences)
            self.env['rb_delivery.error_log'].raise_olivery_error(253,self.id,{'sequance':sequences})
            #raise ValidationError(message)
        if orders_in_collection and len(orders_in_collection) > 0:
            message = ''
            grouped_orders_in_collection = {}
            for order in orders_in_collection:
                collection_id = order.collection_id.sequence
                if collection_id not in grouped_orders_in_collection:
                    grouped_orders_in_collection[collection_id] = {'orders': [], 'count': 0}
                grouped_orders_in_collection[collection_id]['orders'].append(order.sequence)
                grouped_orders_in_collection[collection_id]['count'] += 1

            for collection_id, group_data in grouped_orders_in_collection.items():
                if group_data['count'] > 3:
                    group_data['orders'] = group_data['orders'][:3] + ['...']

                #message = _('Orders with sequences %s will not be created due to existing in collection %s')%(', '.join(map(str, group_data['orders'])),collection_id)
                self.env['rb_delivery.error_log'].raise_olivery_error(254,self.id,{'orders':', '.join(map(str, group_data['orders'])), 'collection_id':collection_id})
                #raise ValidationError(message)

        if orders_to_create_collection:
            self._create_new_collection(orders_to_create_collection)

        return True

    def _create_new_collection(self, order_ids):
        values = {'order_ids': self.env['rb_delivery.order'].browse(order_ids)}
        values['is_pre_paid_collection'] = True

        try:
            pre_paid_collection = self.env['rb_delivery.multi_print_orders_money_collector'].create(values)
            pre_paid_collection.order_ids.write({'is_prepaid_order': True})
        except Exception as e:
            raise ValidationError(_(e))

class create_money_collection_warning(osv.osv):
    _name = "rb_delivery.create_money_collection_warning"
    _description = "create money collection warning model"

    text = fields.Text('Text',readonly=True)

    def confirm_creation(self):
        print_pdf = self.env['rb_delivery.client_configuration'].get_param('print_pdf_on_create')
        ids = self.env['rb_delivery.utility'].create_money_collection(self._context)
        context = {
            'active_ids': ids
        }
        if len(ids)>0 and print_pdf:
            action = {
                'type': 'ir.actions.report',
                'report_type': 'qweb-pdf',
                'report_name': 'rb_delivery.multi_print_orders_money_collector_print_report',
                'activeIds': ids,
                'report_file': 'rb_delivery.multi_print_orders_money_collector_print_report',
                'context': context
            }
            return action
        else:
            return True

class refresh_address_wizard(models.TransientModel):
    _name = "rb_delivery.refresh_address_wizard"
    _description = "to refresh the address values and delivery cost for the orders for the orders that their full address has been changed it\'s values"

    def confirm_refresh(self):
        orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
        orders_with_no_need_to_be_refreshed = orders.filtered(lambda x:x.is_address_tag_values_changed==False)
        if len(orders_with_no_need_to_be_refreshed):
            self.env['rb_delivery.error_log'].raise_olivery_error(547,self.id,{})
        else:
            orders.refresh_address()
class rb_delivery_money_collection_download_attachment(models.TransientModel):
    _name="rb_delivery.money_collection_download_all_attachments"
    _description = "Money Collection Download all attachments Model"

    @api.multi
    def download_attachments(self,collection_ids):
        if not collection_ids:
            return
        zip_buffer = io.BytesIO()
        collections = self.env['rb_delivery.multi_print_orders_money_collector'].browse(collection_ids)
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for collection in collections:
                data = self.env['rb_delivery.report_job_queue'].compute_totals('rb_delivery.multi_print_orders_money_collector_print_report', str(collection.id))
                pdf = self.with_context(lang=self.env.user.lang).env['ir.actions.report'].search([('report_name','=','rb_delivery.multi_print_orders_money_collector_print_report'),('model','=','rb_delivery.multi_print_orders_money_collector')]).sudo().render_qweb_pdf(collection.id, data)

                create_date = datetime.strftime(collection.create_date, "%Y-%m-%d")
                if collection.sudo().business_id:
                    business_name = collection.sudo().business_id.username
                    if collection.sudo().business_id.commercial_name:
                        business_name = collection.sudo().business_id.commercial_name
                else:
                    business_name = collection.name
                file_name =  business_name + '_' + create_date
                file_name = file_name.replace('.','_')
                attachment_id = self.env['ir.attachment'].create({
                    'name': file_name,
                    'type': 'binary',
                    'datas': base64.encodestring(pdf[0]),
                    'res_model': 'rb_delivery.multi_print_orders_money_collector',
                    'res_id': collection.id,
                    'datas_fname':file_name,
                    'public':True
                })
                if attachment_id.datas:
                    pdf_data = base64.b64decode(attachment_id.datas)
                    zipf.writestr(attachment_id.name+".pdf" or "file.pdf", pdf_data)
        
        
        zip_buffer.seek(0)
        zip_content = base64.b64encode(zip_buffer.read())

        zip_attachment = self.env['ir.attachment'].create({
            'name': 'attachments.zip',
            'datas': zip_content,
            'datas_fname': 'attachments.zip',
            'res_model': self._name,
            'res_id': self.id,
            'type': 'binary',
        })

        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{zip_attachment.id}?download=true',
            'target': 'self',
        }
