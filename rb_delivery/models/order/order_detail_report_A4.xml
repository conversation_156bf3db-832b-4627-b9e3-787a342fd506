<?xml version="1.0"?>

<odoo>

	<data>
		<record id="paperformat_order_detail_a4" model="report.paperformat">
			<field name="name">Delivery_Distribution</field>
			<field name="default" eval="True" />
			<field name="format">A4</field>
			<field name="orientation">Landscape</field>
			<field name="margin_top">1</field>
			<field name="margin_bottom">18</field>
			<field name="margin_left">7</field>
			<field name="margin_right">7</field>
			<field name="header_line" eval="False" />
			<field name="header_spacing">1</field>
			<field name="dpi">90</field>
		</record>

		<report id="report_rb_delivery_order_detail_a4_action" string="Waybill A4"
			model="rb_delivery.order" report_type="qweb-pdf" name="rb_delivery.order_detail_a4"
			paperformat="paperformat_order_detail_a4" />
		<template id="minimal_layout_inherit" inherit_id="web.minimal_layout">
			<xpath expr="//head" position="inside">
				<link rel='stylesheet' href="/rb_delivery/static/src/css/report.css" />
			</xpath>
		</template>


		<template id="order_detail_a4">
			<t t-call="web.basic_layout">
				<t t-foreach="docs" t-as="doc">
					<t t-if="not o" t-set="o" t-value="doc" />
					<t t-if="not company">
						<t t-if="company_id">
							<t t-set="company" t-value="company_id" />
						</t>
						<t t-elif="o and 'company_id' in o">
							<t t-set="company" t-value="o.company_id.sudo()" />
						</t>
						<t t-else="else">
							<t t-set="company" t-value="res_company" />
						</t>
					</t>
					<!-- this is to prevent taking size -->
					<div class="header" style="display:none;font-size:12px !important"> </div>
					<div class="footer">
						<div class="row">
							<div style="font-size:12px" class="col-12 text-right">
								<span t-esc="company.report_footer" />
							</div>
						</div>
					</div>
					<div class="page" style="page-break-before:always;">
						<div class="row">
							<div class="col-4 company_details_col" style="margin-top:5px">
								<div class="row" style="font-size:10px">
									<div>
										<div class="col-12" style="float:right;">
											
												<span t-esc="company.state_id.name"></span>
												<span t-if="company.state_id.name">
													<span t-if="company.country_id.name">,</span>
												</span>
												<span t-esc="company.country_id.name"></span>
											
										</div>
										<div class="col-12 " style="float:right;"
											t-if="company.phone">
											
												<span t-esc="company.phone"></span>
											
										</div>
										<div class="col-12 " style="float:right;"
											t-if="company.email">
											
												<span t-esc="company.email"></span>
											
										</div>
										<div class="col-12 " style="float:right;"
											t-if="company.website">
											
												<span t-esc="company.website"></span>
											
										</div>
										<div class="col-12 " style="float:right;"
											t-if="company.company_registry">
											
												<span>Company Registry:</span>
												<span t-esc="company.company_registry"></span>
												
											
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="container">
						<div class="row waybill-header-row" style="display:flex ;justify-content:center ;align-items:center;">
							<div class="col-4 flex-grow-0 partner_reference_col" style="margin-top:2px; margin-right:5px; margin-left:5px;" t-if="doc.partner_reference_id_barcode">
								<div class="text-center" style="font-size:18px !important; font-weight:bold !important;">
									Partner Reference
								</div>
								<img style="width:350px; height:60px; display:block; margin:2 auto;" t-attf-src="data:image/*;base64,{{doc.partner_reference_id_barcode}}" />
								<h5 class="text-center" style="font-size:18px !important; font-weight:bold;">
									<span t-field="doc.partner_reference_id" />
								</h5>
							</div>
							<t t-else="else">
								<div class="col-4 flex-grow-0" style="margin: 2px 5px; text-align: center;"></div>
							</t>
							<div class="col-4 flex-grow-0 barcode_col text-center" style="margin-top:2px; margin-right:5px; margin-left:5px;">
								<div style="font-size:18px !important; font-weight:bold !important;">
									Waybill
								</div>
									<t t-if="request.env['rb_delivery.client_configuration'].sudo().get_param('use_qr_code')">
										<img style="width:100px;height:100px;display:block ;margin:0 auto" t-attf-src="data:image/*;base64,{{doc.qr_code_image}}" />
									</t>
									<t t-else="">
										<img style="width:300px;height:80px;display:block ;margin:0 auto" t-attf-src="data:image/*;base64,{{doc.barcode}}" />
									</t>
									<h5 class="text-center" style="font-size:18px ;font-weight:bold">
										<t>
											<span t-field="doc.sequence" />
										</t>
									</h5>
							</div>
							<div class="col-4 flex-grow-0 reference_col" style="margin-top:2px; margin-right:5px; margin-left:5px;" t-if="doc.barcode_reference">
								<div class="text-center" style="font-size:18px !important; font-weight:bold !important;">
									Reference
								</div>
									<t t-if="request.env['rb_delivery.client_configuration'].sudo().get_param('use_qr_code')">
										<img style="width:100px;height:100px;display:block ;margin:0 auto" t-attf-src="data:image/*;base64,{{doc.qr_code_reference}}" />
									</t>
									<t t-else="">
										<img style="width:300px;height:80px;display:block ;margin:0 auto" t-attf-src="data:image/*;base64,{{doc.barcode_reference}}" />
									</t>
									<h5 class="text-center" style="font-size:18px ;font-weight:bold">
										<t>
											<span t-field="doc.reference_id" />
										</t>
									</h5>
							</div>
							<t t-else="else">
								<div class="col-4 flex-grow-0" style="margin: 5px 5px; text-align: center;"></div>
							</t>
						</div>
						</div>
						<div class="image_bg"></div>
						<div class="oe_structure" />
						<div class="row waybill_body_row" style=" padding-top:12px">
							<div class="col-8">
								<table class="table-condensed sender_table"
									style="font-size:1.1em;width:100%;border:1px solid black">
									<thead>
										<tr>
											<th colspan="5">Sender's details</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td >
												<div>Name:
													<t t-set="business" t-value="doc.sudo().assign_to_business" />
													<t t-if="doc.show_alt_address and doc.alt_business_name ">
														<t t-set="business_name" t-value="doc.alt_business_name" />
													</t>
													<t t-elif="doc.show_follower_info_in_waybill and doc.show_follower_info and doc.follower_store_name">
														<t t-set="business_name" t-value="doc.follower_store_name" />
													</t>
													<t t-elif="business.commercial_name">
														<t t-set="business_name" t-value="business.commercial_name" />
													</t>
													<t t-else="else">
														<t t-set="business_name" t-value="business.username" />
													</t>
													<span style="font-weight: bold" t-esc="business_name" />
												</div>
											</td>
											<t t-set="show_sender_city_in_waybill" t-value="request.env['rb_delivery.client_configuration'].sudo().get_param('show_sender_city_in_waybill')" />
											<t t-if="show_sender_city_in_waybill">
											<td t-if="doc.sudo().assign_to_business.show_area_in_waybill">
												<div>City:
													<t t-if="doc.show_alt_address and doc.business_alt_area">
														<t t-set="area" t-value="doc.business_alt_area.name" />
													</t>
													<t t-elif="doc.show_follower_info_in_waybill and doc.show_follower_info and doc.follower_area">
														<t t-set="area" t-value="doc.follower_area" />
													</t>
													<t t-else="else">
														<t t-set="area" t-value="doc.business_area" />
													</t>
													<span style="font-weight: bold" t-esc="area" />
												</div>
											</td>	
											</t>
										</tr>
										<t t-if='doc.show_sender_address_in_waybill'>
											<tr t-if="doc.sudo().assign_to_business.show_address_in_waybill">
												<td colspan="2" style="text-align:start;padding:0px 10px;">
												<div>Address:
													<t t-if="doc.show_alt_address and doc.business_alt_address">
														<t t-set="address" t-value="doc.business_alt_address" />
													</t>
													<t t-elif="doc.show_follower_info_in_waybill and doc.show_follower_info and doc.follower_address">
														<t t-set="address" t-value="doc.follower_address" />
													</t>
													<t t-else="else">
														<t t-set="address" t-value="doc.business_address" />
													</t>
													<span style="font-weight: bold" t-esc="address" />
												</div>
												</td>
											</tr>
										</t>
										<t t-set="show_sender_mobile_in_waybill" t-value="request.env['rb_delivery.client_configuration'].sudo().get_param('show_sender_mobile_in_waybill')" />
										<t t-if="show_sender_mobile_in_waybill">
											<tr>
												<td colspan="2" style="text-align:start;padding:0px 10px">
													<div>Mobile number:
														<t t-if="doc.show_alt_address and doc.alt_mobile_number ">
															<t t-set="mobile_number" t-value="doc.alt_mobile_number" />
														</t>
														<t t-elif="doc.show_follower_info_in_waybill and doc.show_follower_info and doc.follower_mobile_number">
															<t t-set="mobile_number" t-value="doc.follower_mobile_number" />
														</t>
														<t t-elif="doc.commercial_number">
															<t t-set="mobile_number" t-value="doc.commercial_number" />
														</t>
														<t t-else="else">
															<t t-set="mobile_number" t-value="doc.business_mobile_number" />
														</t>
														<span style="font-weight: bold" t-esc="mobile_number" />
													</div>
												</td>
											</tr>
										</t>
									</tbody>
								</table>
								<table class="table-condensed customer_table"
									style="font-size:1.1em;width:100%;border:1px solid black">
									<thead>
										<tr>
											<th colspan="5">Recipient's details</th>
										</tr>
									</thead>

									<tbody>
										<tr>
											<td>

												<div>Name: <span style="font-weight: bold"
														t-field="doc.customer_name" />
														<br/>
														<span t-field="doc.customer_mobile" />        

												</div>
											</td>

											<td>
												<div>City: <span style="font-weight: bold"
														t-field="doc.customer_area" />
														<span
														style="font-weight: bold"
														t-if="doc.customer_sub_area"> , </span>
														<span
														style="font-weight: bold"
														t-if="doc.customer_sub_area"
														t-field="doc.customer_sub_area" />
												</div>
											</td>
										</tr>
										<tr>
											<td colspan="2" style="text-align:start;padding:0px 10px">
												<div class="o_note_container">Address in detail: <span
														style="font-weight: bold"
														t-field="doc.customer_address" />
												</div>

											</td>
										</tr>
										<tr>
											<td colspan="2" style="text-align:start;padding:0px 10px">
												<div>Mobile number: <span style="font-weight: bold"
														t-field="doc.customer_mobile" />
												</div>
											</td>
										</tr>

										<tr t-if="doc.second_mobile_number">
											<td colspan="2" style="text-align:start;padding:0px 10px">
												<div>Second mobile number: <span
														style="font-weight: bold"
														t-field="doc.second_mobile_number" />
												</div>
											</td>
										</tr>


									</tbody>

								</table>
							</div>
							<div class="col-4 order_details_table">
								<table class="table-condensed"
									style="font-size:1.1em;width:100%;border:1px solid black">
									<thead>
										<tr>
											<th colspan="2">Order's details</th>
										</tr>
										<tr>
											<th colspan="1">
												Type
											</th>
											<th colspan="1">
												Weight

											</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>
												<span style="font-weight: bold"
													t-field="doc.order_type_id" />
												<br />
												<br />
											</td>
											<td>
												<span style="font-weight: bold"
													t-field="doc.order_weight" />
												<br />
												<br />
											</td>
										</tr>
									</tbody>
								</table>
								<table class="table-condensed money_collection_cost_table"
									style="font-size:1.1em;width:100%;border:1px solid black">
									<thead>
										<tr>
											<th colspan="2">
												Money collection cost when received

											</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td style="text-align:center">
												<span style="font-size:20px;font-weight: bold" t-field="doc.money_collection_cost"/>
												<br />
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="row d-flex note_row" style="justify-content: flex-start; font-size: 15px; border:1px solid #ccc; padding:12px;margin-top:12px;margin-bottom:12px;" t-if="doc.note">
							<div class="col-12 d-flex o_note_container" style="justify-content: flex-start;">
								Notes: <span style="font-weight: bold" t-field="doc.note" />
							</div>
						</div>

						<!-- Second row for description tags -->
						<div class="row d-flex description_tag_row" style="justify-content: flex-start; font-size: 15px; border:1px solid #ccc; padding:12px;margin-top:12px;margin-bottom:12px;" t-if="doc.description_tags and doc.show_description_tags_in_waybill">
							<div class="col-12 d-flex" style="justify-content: flex-start;">
								Description Tags:
								<t t-foreach="doc.description_tags" t-as="description_tag">
									<t t-if="description_tag and description_tag.name">
										<span style="font-weight: bold" t-esc="description_tag.name" />
										<span t-if="not description_tag_last">,</span>
									</t>
								</t>
							</div>
						</div>

						<!-- Third row for product notes -->
						<div class="row d-flex product_note_row" style="justify-content: flex-start; font-size: 15px; border:1px solid #ccc; padding:12px;margin-top:12px;margin-bottom:12px;" t-if="doc.product_note">
							<div class="col-12 d-flex o_note_container" style="justify-content: flex-start;">
								Product Notes: <span style="font-weight: bold" t-field="doc.product_note" />
							</div>
						</div>

						<div class="row waybill_footer_thead" style="font-size: 15px; text-align:center">
							<div class="col-2">Recipient's name and signature </div>
							<div class="col-2">Agent's name and signature </div>
							<div class="col-4 text-center"
								t-if="doc.sudo().assign_to_business.print_user_logo_in_bolisa">
								<img t-if="doc.sudo().assign_to_business.user_image"
									t-att-src="image_data_uri(doc.sudo().assign_to_business.user_image)"
									alt="Logo" style="height: 180px; object-fit: contain;max-width:500px;" />
							</div>
							<t t-else="else">
								<div class="col-4" style="height: 180px;"></div>
							</t>
							<div class="col-4 logo_img_col text-center">
								<t t-if="doc.sudo().assign_to_business.replace_company_logo_with_business_logo" >
									<img t-if="doc.sudo().assign_to_business.user_image" t-att-src="image_data_uri(doc.sudo().assign_to_business.user_image)" alt="Logo" style="height: 180px;max-width:500px; object-fit: contain;"/>
								</t>
								<t t-else="else">									
									<img t-if="company.logo" t-att-src="image_data_uri(company.logo)" alt="Logo" style="height: 180px; object-fit: contain;max-width:500px;"/>
								</t>
							</div>
						</div>
						<div class="row waybill_footer_tbody" style="font-size: 12px; text-align:center; margin-top: -100px">
							<div class="col-4">
								<div class="row" style="margin-bottom: 20px">
									<div class="col-6">..............................</div>
									<div class="col-6">..............................</div>
								</div>
								<div class="row">
									<div class="col-12">Create Date:<span style="font-weight: bold" t-esc="context_timestamp(doc.create_date).strftime('%Y-%m-%d %H:%M')"/><br/>
									Print Date:<span style="font-weight: bold" t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M')"/>
									</div>
									<br />
								</div>
							</div>
						</div>
					</div>
				</t>
			</t>
		</template>
	</data>
</odoo>