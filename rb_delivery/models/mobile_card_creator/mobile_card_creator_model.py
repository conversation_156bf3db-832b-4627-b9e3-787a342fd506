# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json

class rb_delivery_mobile_card_creator(models.Model):

    _name = 'rb_delivery.mobile_card_creator'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    _sql_constraints = [('card_name_group_unique', 'unique(card_name,group_id)', _('card name with this role already exists!'))]

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]

    
    card_name = fields.Selection([('order_card','Order card'),('user_card','User card'),('business_stores_card','Business Stores card'),('money_collection_card','Money Collection Card'),('returned_money_collection_card','Returned Money Collection Card'),('agent_money_collection_card','Agent Money Collection Card'),('agent_returned_collection_card','Agent Returned Collection Card'),('runsheet_card','Runsheet Card'),('pickup_request_card','Pickup Request Card'),('report_card','Report Card'),('sort_and_destribute_card', 'Sort And Destribute'), ('ondemand_timer_card', 'Ondemand timer card'), ('tasks_card', 'Tasks Card')], required=True)

    model = fields.Many2one('ir.model', required=True)

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups,copy=False)

    name = fields.Char('Unique Name',compute = "compute_name", store=True)

    card_items = fields.One2many('rb_delivery.mobile_card_item',inverse_name="card_creator")

    form_creator = fields.Many2one('rb_delivery.mobile_form_creator')

    form_creator_fields_names = fields.Char(related='form_creator.fields_names')

    fields_ids = fields.Many2many('ir.model.fields',compute="compute_card_fields",store=True)

    fields_names = fields.Char(compute="compute_card_fields",store=True)

    show_collapse = fields.Boolean()

    default_collapsed = fields.Boolean()

    order_detail = fields.Many2one('rb_delivery.mobile_order_detail')
    
    card_theme = fields.Selection([('olivery_theme','Olivery Theme'),('new_olivery_theme','New Olivery Theme'),('united_theme','United Theme'),('movify_theme','Movify Theme')],default='olivery_theme',)

    @api.onchange('show_collapse')
    def reset_default_collapsed(self):
        if not self.show_collapse:
            self.default_collapsed=False

    @api.multi
    @api.depends('card_name','group_id')
    def compute_name(self):
        for rec in self:
            if rec.card_name and rec.group_id:
                rec.name = rec.card_name + " [" + rec.group_id.name + "]"

    @api.one
    @api.depends('card_items','form_creator','form_creator_fields_names', 'order_detail', 'order_detail.card_structure_hash')
    def compute_card_fields(self):
        fields_ids=[]
        fields_names=[]
        all_card_items = self.card_items
        ondemand_card_domain = [('card_name', '=', 'ondemand_timer_card'),('group_id', '=', self.group_id.id)]
        if isinstance(self.id, int):
            ondemand_card_domain.append(('id', '!=', self.id))
        ondemand_card = self.sudo().search(ondemand_card_domain, limit=1)
        if ondemand_card:
            all_card_items += ondemand_card.card_items
        for card_item in all_card_items:
            if card_item.hyperlink_field:
                fields_ids.append(card_item.hyperlink_field.id)
                fields_names.append(card_item.hyperlink_field.name)
            if card_item.field:
                fields_ids.append(card_item.field.id)
                fields_names.append(card_item.field.name)
            elif len(card_item.card_items)>0:
                for sub_item in card_item.card_items:
                    fields_ids.append(sub_item.field.id)
                    fields_names.append(sub_item.field.name)
            elif card_item.button_function and len(card_item.button_function.required_fields)>0:
                for field in card_item.button_function.required_fields:
                    fields_ids.append(field.id)
                    fields_names.append(field.name)
        if self.order_detail and self.order_detail.id:
            order_detail_fields = self.order_detail.get_card_fields()
            fields_ids+=order_detail_fields.ids
            fields_names+=order_detail_fields.mapped('name')
        if self.form_creator and self.form_creator.id:
            fields_ids+=self.form_creator.fields_ids.ids
            fields_names+=json.loads(self.form_creator_fields_names)
        fields_names = list(set(fields_names))
        self.fields_ids=[[6,0,fields_ids]]
        self.fields_names = json.dumps(fields_names)

    def find_related_form_creator(self):
        domain = [
            ('group_id', '=', self.group_id.id),
            ('model', '=', self.model.id),
            ('is_custom', '=', False)
        ]
        
        form_creator = self.env['rb_delivery.mobile_form_creator'].search(
            domain + [('form_name', 'not in', ['status_action_form', 'quick_order_form', 'business_stores_form'])], 
            limit=1
        ) or self.env['rb_delivery.mobile_form_creator'].search(
            domain + [('form_name', '=', 'business_stores_form')], 
            limit=1
        )
        return form_creator.id if form_creator else False
    
    @api.model
    def create(self,values):
        card_creator = super(rb_delivery_mobile_card_creator,self).create(values)
        card_creator.form_creator = card_creator.find_related_form_creator()
        return card_creator

    @api.one
    def write(self,values):
        is_written = super(rb_delivery_mobile_card_creator,self).write(values)
        if('form_creator' not in values):
            self.form_creator = self.find_related_form_creator()
        return is_written
    
    @api.multi
    def copy(self, default=None):
        if default is None:
            default = {}
        default['card_items'] = [(0, 0, {
            'is_card_container': card_item.is_card_container,
            'card_item': card_item.card_item.id,
            'card_item_model': card_item.card_item_model.id,
            'field': card_item.field.id,
            'label': card_item.label,
            'is_label': card_item.is_label,
            'is_button': card_item.is_button,
            'is_empty_feild': card_item.is_empty_feild,
            'button_icon': card_item.button_icon,
            'button_function': card_item.button_function.id,
            'have_image': card_item.have_image,
            'image_field_name': card_item.image_field_name.id,
            'have_color': card_item.have_color,
            'color_field_name': card_item.color_field_name.id,
            'color': card_item.color,
            'background': card_item.background,
            'sequence': card_item.sequence,
            'position': card_item.position,
            'sub_position': card_item.sub_position.id,
        }) for card_item in self.card_items]
        return super(rb_delivery_mobile_card_creator, self).copy(default=default)

    @api.onchange('model')
    def reset_card_items(self):
        self.card_items=[(6,0,[])]
