# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json
import ast

class rb_delivery_mobile_form_input(models.Model):

    _name = 'rb_delivery.mobile_form_input'
    _order = "sequence ASC"
    _inherit = 'mail.thread'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    
    form_creator = fields.Many2one('rb_delivery.mobile_form_creator', track_visibility="on_change")

    form_creator_fields = fields.Many2many('ir.model.fields',related="form_creator.fields_ids", track_visibility="on_change")

    form_creator_inputs = fields.One2many('rb_delivery.mobile_form_input',related="form_creator.form_inputs", track_visibility="on_change")

    model = fields.Many2one('ir.model' , related='form_creator.model', track_visibility="on_change")

    field = fields.Many2one('ir.model.fields', track_visibility="on_change")
    
    field_relation = fields.Char(related="field.relation", track_visibility="on_change")

    field_model = fields.Char(related="field.model", track_visibility="on_change")

    field_ttype = fields.Selection(related="field.ttype", track_visibility="on_change")

    is_public_form = fields.Boolean(related="form_creator.is_public_form", track_visibility="on_change")

    domain = fields.Char(default=[], track_visibility="on_change")

    search_by = fields.Many2many('ir.model.fields', track_visibility="on_change")

    limit_per_search = fields.Integer(track_visibility="on_change")

    search_domain = fields.Char(compute="compute_search_domain",store=True, track_visibility="on_change")

    input_type = fields.Selection([('number','Number'),('text','Text'),('tel','Phone'),('email','Email'),('password','Password'),('search','Search'),('url','Url')], track_visibility="on_change")

    placeholder = fields.Char(track_visibility="on_change")

    parent_input = fields.Many2one('rb_delivery.mobile_form_input' , compute='compute_parent_input',store=True, track_visibility="on_change")

    parent_field = fields.Many2one('ir.model.fields', track_visibility="on_change")

    parent_field_relation = fields.Char(related='parent_field.relation', track_visibility="on_change")

    parent_field_map = fields.Many2one('ir.model.fields', track_visibility="on_change")

    required = fields.Boolean(track_visibility="on_change")

    is_location=fields.Boolean(track_visibility="on_change")

    is_signature=fields.Boolean(track_visibility="on_change")

    is_multi_scan=fields.Boolean(track_visibility="on_change")

    barcode_verified=fields.Boolean(track_visibility="on_change")

    readonly = fields.Boolean(track_visibility="on_change")

    is_button = fields.Boolean(track_visibility="on_change")

    button_text = fields.Char(track_visibility="on_change")

    button_icon_selction = fields.Many2one('rb_delivery.icons', track_visibility="on_change")

    button_icon = fields.Char(track_visibility="on_change")
    
    field_icon_selction = fields.Many2one('rb_delivery.icons', track_visibility="on_change")

    field_icon = fields.Char(track_visibility="on_change")

    button_function = fields.Selection([('getCurrentLocation','Get Current Location'),('selectDateRange','Select Date Range'),('submitForm','Submit Form'),('login','Login'),('signup','Signup'),('submitStatusAction','Submit Status Action'),('openForm','Open Form'),('logOut','Log Out'),('contactUs','Contact Us')],default="submitForm", track_visibility="on_change")

    show_terms_and_conditions = fields.Boolean(string='Show Terms And Conditions')

    form_to_open = fields.Many2one('rb_delivery.mobile_form_creator',string="Form To Open",domain=[])

    default_value = fields.Char(track_visibility="on_change")

    have_image = fields.Boolean(compute="_get_field_have_image",store=True, track_visibility="on_change")

    image_source = fields.Selection([('CAMERA','Camera Only'),('PHOTOS','Gallery Only'),('PROMPT','Camera And Gallery')],default='PROMPT')

    image_field_name = fields.Many2one("ir.model.fields", track_visibility="on_change")

    is_separator = fields.Boolean(track_visibility="on_change")

    as_step = fields.Boolean(track_visibility="on_change")

    separator_title = fields.Char(track_visibility="on_change")

    color = fields.Char(track_visibility="on_change")

    background = fields.Char(track_visibility="on_change")

    invisible = fields.Boolean(track_visibility="on_change")

    invisible_domain = fields.Char(string="User invisible domain",track_visibility="on_change")

    show_barcode_scanner = fields.Boolean(track_visibility="on_change")

    show_location_selector = fields.Boolean(track_visibility="on_change")
    
    show_true_buyer_button = fields.Boolean(track_visibility="on_change")

    min_length = fields.Integer(track_visibility="on_change")

    max_length = fields.Integer(track_visibility="on_change")

    sequence = fields.Integer(track_visibility="on_change")

    create_or_select = fields.Selection([('choose_existing','Choose From Existing'),('create_new','Create New Records')],default="choose_existing", track_visibility="on_change")

    position = fields.Selection([('in_form','Inside Form'),('in_footer','Inside Bottom of Footer'),('inside_footer','Inside Top of Footer')],default="in_form", track_visibility="on_change")

    show_voice_to_text_ability = fields.Boolean(track_visibility="on_change")

    is_monetary = fields.Boolean(track_visibility="on_change")

    unique_field_name = fields.Char(compute = "compute_unique_field_name", store=True, track_visibility="on_change")

    compute_function = fields.Many2one('rb_delivery.mobile_compute_functions','Compute Function', track_visibility="on_change")

    is_auto_fill_mapping_fields_enabled = fields.Boolean('Is auto fill fields enabled', track_visibility="on_change")

    mapping_relation_fields = fields.One2many('rb_delivery.mapping_relation_fields',inverse_name="form_input", track_visibility="on_change")

    open_selection_modal_when_click = fields.Boolean('Open selection modal when click', track_visibility="on_change")

    connected_field_to_selection_modal = fields.Many2one('ir.model.fields', track_visibility="on_change")

    connected_field_relation = fields.Char(related="connected_field_to_selection_modal.relation", track_visibility="on_change")

    search_by_inside_selection_modal = fields.Many2many(comodel_name = 'ir.model.fields',
        string = 'Search By inside selection modal',
        relation = 'search_by_inside_selection_modal',
        column1 = 'search_by_inside_selection_modal_id',
        column2 = 'search_by_inside_selection_modal_form_id', track_visibility="on_change")

    search_domain_inside_selection_modal = fields.Char(compute="compute_search_domain_inside_selection_modal",store=True, track_visibility="on_change")

    show_client_history = fields.Boolean('Show client history', track_visibility="on_change")
    
    sort_by = fields.Selection([('asc','Ascending'),('desc','Descending')],default='desc', track_visibility="on_change")

    is_label_field = fields.Boolean('Is Lable Field', track_visibility="on_change")

    combine_two_booleans = fields.Boolean('Combine two booleans', track_visibility="on_change")

    second_boolean_field = fields.Many2one('ir.model.fields', track_visibility="on_change")

    first_placeholder = fields.Char('First Field Placeholder', track_visibility="on_change")

    second_placeholder = fields.Char('Second Field Placeholder', track_visibility="on_change")

    is_isolated_input = fields.Boolean(string='Is Isolated', track_visibility="on_change")

    is_stand_alone = fields.Boolean(string='Is Stand Alone', track_visibility="on_change")

    stand_alone_function = fields.Char('Stand Alone Function', track_visibility="on_change")

    isolated_form = fields.One2many('rb_delivery.mobile_form_creator',string='Isolated Form',inverse_name="isolated_input",ondelete="cascade", track_visibility="on_change")

    quick_order_position = fields.Selection([('left','Left'),('right','Right')],default='left', track_visibility="on_change")

    clear_after_submit = fields.Boolean('Is Clear After submit', default=True, track_visibility="on_change")

    form_name = fields.Selection(related='form_creator.form_name', track_visibility="on_change")

    quick_order_touch_selection = fields.Boolean('Touch Selection', default=False, track_visibility="on_change")

    warning_message = fields.Char('warning message', track_visibility="on_change")

    combine_three_fields = fields.Boolean('combine Three Fields', default=False, track_visibility="on_change")

    lock_required = fields.Boolean('Lock Required', default=False, track_visibility="on_change")

    quick_order_show_as = fields.Selection([('tags','Tags'),('table','Table'),('attachments','Attachments')],default="tags", track_visibility="on_change")

    quick_order_table_fields = fields.One2many('rb_delivery.table_items',
        string = 'Table Fields',
        inverse_name="form_input",
        track_visibility="on_change")
    
    quick_order_table_fields_json = fields.Char('Quick order fields', compute="compute_quick_order_table_fields")

    table_comodel_inverse_name = fields.Many2one('ir.model.fields', track_visibility="on_change")

    cascade_child_to_parent = fields.Boolean('Clear Parent when clear child', default=False, track_visibility="on_change")

    cascade_mapping_fields = fields.Boolean('Clear mapped fields when clear parent', default=False, track_visibility="on_change")

    mobile_invisible_domain = fields.Char(track_visibility="on_change")

    show_create_button = fields.Boolean('Show Create Button', default=False, track_visibility="on_change")

    @api.onchange('button_function')
    def form_to_open_domain(self):
        if self.button_function == 'openForm':
            return {
                'domain':{
                    'form_to_open':[['group_id','=',self.form_creator.group_id.id]]
                }
            }

    @api.one
    @api.depends('image_field_name')
    def _get_field_have_image(self):
        if self.image_field_name:
            self.have_image=True


    @api.onchange('button_icon_selction')
    def button_icon_selction_onchange(self):
        if self.button_icon_selction: 
            self.button_icon = self.button_icon_selction.name
    

    @api.onchange('field_icon_selction')
    def field_icon_selction_onchange(self):
        if self.field_icon_selction: 
            self.field_icon = self.field_icon_selction.name


    @api.one
    @api.depends('parent_field')
    def compute_parent_input(self):
        if self.parent_field:
            parent_input = self.form_creator.form_inputs.filtered(lambda x:x.field.id==self.parent_field.id)
            if parent_input and parent_input.id:
                self.parent_input = parent_input.id

    @api.one
    @api.depends('search_by')
    def compute_search_domain(self):
        domain=[]
        for field in self.search_by:
            domain.append([field.name,'ilike','value'])
        i=0
        while i < len(self.search_by)-1:
            domain.insert(0,'|')
            i+=1
        if len(domain)>0:
            self.search_domain=json.dumps(domain)
        else:
            self.search_domain=json.dumps([['name','ilike','value']])
    

    @api.one
    @api.depends('search_by_inside_selection_modal')
    def compute_search_domain_inside_selection_modal(self):
        
        domain=[]
        for field in self.search_by_inside_selection_modal:
            domain.append([field.name,'ilike','value'])
        i=0
        while i < len(self.search_by_inside_selection_modal)-1:
            domain.insert(0,'|')
            i+=1
        if len(domain)>0:
            self.search_domain_inside_selection_modal=json.dumps(domain)
        else:
            self.search_domain_inside_selection_modal=json.dumps([['name','ilike','value']])

    @api.one
    @api.depends('quick_order_table_fields')
    def compute_quick_order_table_fields(self):
        fields = []

        if self.quick_order_table_fields:
            for rec in self.quick_order_table_fields:
                fields.append({'id': rec.field.name, 'name': rec.field.field_description, 'ttype': rec.field.ttype, 'relation': rec.field.relation, 'domain': rec.domain, 'label_template': rec.label_template, 'required': rec.required, 'compute_function': rec.local_compute_function, 'disabled': rec.locked})

        self.quick_order_table_fields_json = json.dumps(fields)
    
    @api.multi
    @api.depends('form_creator','field','is_button','button_text','is_separator','separator_title')
    def compute_unique_field_name(self):
        for rec in self:
            if rec.form_creator and rec.form_creator.name:
                if rec.field and rec.field.name:
                    if rec.field.name == 'password':
                        rec.unique_field_name = rec.form_creator.name + '_' + rec.field.name + '_' + rec.placeholder
                    else:
                        rec.unique_field_name = rec.form_creator.name + '_' + rec.field.name
                elif rec.is_button and rec.button_text:
                    rec.unique_field_name = rec.form_creator.name + '_' + rec.button_text
                elif rec.is_separator and rec.separator_title:
                    rec.unique_field_name = rec.form_creator.name + '_' + rec.separator_title
                elif rec.is_location:
                    rec.unique_field_name = rec.form_creator.name + '_Location'
                elif rec.is_signature:
                    rec.unique_field_name = rec.form_creator.name + '_Signature'
                elif rec.is_multi_scan:
                    rec.unique_field_name = rec.form_creator.name + '_MultiScan'
            
        

    @api.multi
    def name_get(self):
        res = []
        for rec in self:
            name = rec.field.name
            if not name:
                if rec.is_separator:
                    name = _('Separator')
                elif rec.is_button:
                    name = _('Button')
            res.append((rec.id, name))

        return res

    
    

    @api.model
    def get_form(self,form_name=False,status=False,lang='en_US',form=False):
        group_id=4
        if(self._uid != 4):
            group_id = self.env['rb_delivery.user'].sudo().search([['user_id','=',self._uid]]).group_id.id
        if not form and form_name:
            form_domain=["|",['form_name_custom','=',form_name],['form_name','=',form_name],['group_id','=',group_id]]
            if self.env.user.has_group('base.group_system') and form_name == 'quick_order_form':
                record_id = self.env['ir.model.data'].xmlid_to_res_id('rb_delivery.role_super_manager')
                sp_group = self.env['res.groups'].browse(record_id)
                form_domain = ["|",['form_name_custom','=',form_name],['form_name','=',form_name], ['group_id','in',[sp_group.id]]]
            if status:
                form_domain.append(['status_ids','in',[status]])
            if group_id == 4:
                form = self.env['rb_delivery.mobile_form_creator'].sudo().with_context(lang=lang).search(form_domain)
            else:
                form = self.env['rb_delivery.mobile_form_creator'].sudo().search(form_domain)
        if self._context.get('record_id') and form and form.status_action_domain and form.status_action_domain != '[]':
            domain = json.loads(form.status_action_domain) + [('id','=',self._context.get('record_id'))]
            rec = self.env[form.model.model].search(domain)
            if not rec:
                return False

        form_inputs = form.form_inputs
        steps = []
        inputs=[]
        for input in form_inputs:
            values={}
            if input.quick_order_position:
                values['quick_order_position'] = input.quick_order_position
            values['clear_after_submit'] = input.clear_after_submit
            values['quick_order_touch_selection'] = input.quick_order_touch_selection
            values['warning_message'] = input.warning_message
            if input.is_separator:
                values['is_separator']=True
                values['as_step']=input.as_step
                values['separator_title']=input.separator_title
                values['background']=input.background
                values['color']=input.color
                if input.as_step:
                    steps.append({
                        'title':input.separator_title,
                        'form_inputs':[]
                    })
            elif input.is_button:
                values['is_button']=True
                values['background']=input.background
                values['color']=input.color
                values['button_text']=input.button_text
                values['button_icon']=input.button_icon
                values['button_function']=input.button_function
                values['form_to_open']=input.form_to_open.form_name_custom or input.form_to_open.form_name
                values['show_terms_and_conditions']=input.show_terms_and_conditions
                values['position']=input.position
            else:
                values['position']=input.position
                values['field']={
                    'id':input.field.id,
                    'ttype':input.field.ttype,
                    'name':input.field.name,
                    'model_name':input.field.relation,
                    'input_type':input.input_type,
                    'placeholder':input.placeholder if input.placeholder else input.field.field_description,
                    'readonly':input.readonly,
                    'is_location':input.is_location,
                    'is_signature':input.is_signature,
                    'is_multi_scan':input.is_multi_scan,
                    'barcode_verified':input.barcode_verified,
                    'sort_by': input.sort_by,
                    'cascade_mapping_fields': input.cascade_mapping_fields
                }
                if input.field.ttype == 'selection':
                    selection_items = self.env[input.field.model].fields_get([input.field.name])
                    if selection_items and selection_items.get(input.field.name):
                        selection_items_values = selection_items.get(input.field.name)
                        if 'selection' in selection_items_values:
                            values['selection_items'] =  selection_items_values['selection']
                elif input.combine_two_booleans:
                    values['selection_items'] = [
                        [True, input.first_placeholder or input.field.field_description],
                        [False, input.second_placeholder or input.second_boolean_field.field_description]
                    ]
                values['combine_two_booleans']=input.combine_two_booleans
                values['combine_three_fields']=input.combine_three_fields
                values['show_create_button']=input.show_create_button
                values['field_icon'] = input.field_icon
                values['value']=input.default_value
                values['mobile_invisible_domain']=input.mobile_invisible_domain
                values['validations']={'required':input.required,'max_length':input.max_length,'min_length':input.min_length,'email':input.input_type=='email'}
                if input.invisible_domain and len(json.loads(input.invisible_domain.lower())):
                    invisible = self.env['rb_delivery.user'].search([['user_id','=',self._uid]]+json.loads(input.invisible_domain.lower()))
                else:
                    invisible = input.invisible
                values['invisible']=True if invisible else False
                if input.field.ttype == 'many2one' or input.field.ttype == 'many2many' or input.field.ttype == 'one2many':
                    if input.parent_field:
                        values['parent_field']={
                            'ttype':input.parent_field.ttype,
                            'name':input.parent_field.name,
                            'model_name':input.parent_field.relation,
                            'field_map':input.parent_field_map.name,
                            'cascade_child_to_parent': input.cascade_child_to_parent,
                            'reflect_to_parent':input.parent_input.field.name
                        }
                    if input.is_public_form:
                        fields_to_get = ['id', 'display_name']
                        if input.mapping_relation_fields:
                            for autoFillInput in input.mapping_relation_fields:
                                if autoFillInput:
                                    fields_to_get.append(autoFillInput[0].inverse_model_fields.name)
                        if input.parent_input and input.parent_field_map:                        
                            fields_to_get.append(input.parent_field_map.name)
                        if input.domain:
                            values['selection_items']=self.env[input.field.relation].sudo().with_context(lang=lang).search_read(json.loads(input.domain),fields_to_get)
                        else:
                            values['selection_items']=self.env[input.field.relation].sudo().with_context(lang=lang).search_read([],fields_to_get)
                    values['have_image']=input.have_image
                    if values['have_image']:
                        values['image_field_name']=input.image_field_name.name

                    if input.domain:
                        values['domain'] = ast.literal_eval(input.domain or '[]')
                    if input.search_domain:
                        try:
                            values['search_domain']=json.loads(input.search_domain)
                        except:
                            values['search_domain']=[['name','ilike','value']]
                    if input.limit_per_search:
                        values['limit_per_search'] = input.limit_per_search
                elif input.field.ttype=='text' or input.field.ttype == 'char' or input.field.ttype=='integer' or input.field.ttype=='float':
                    values['is_monetary'] = input.is_monetary
                    values['show_voice_to_text_ability']=input.show_voice_to_text_ability
                elif input.field.ttype=='binary':
                    values['image_source'] = input.image_source
            values['show_barcode_scanner']=input.show_barcode_scanner
            values['show_location_selector']=input.show_location_selector
            values['show_true_buyer_button']=input.show_true_buyer_button
            values['show_client_history']=input.show_client_history
            if input.form_name == 'quick_order_form':
                if input.quick_order_show_as and input.quick_order_show_as == 'table':
                    values['quick_order_table_fields_json'] = json.loads(input.quick_order_table_fields_json)
                    values['quick_order_show_as'] = input.quick_order_show_as
                elif input.quick_order_show_as:
                    values['quick_order_show_as'] = input.quick_order_show_as
                
                if input.table_comodel_inverse_name:
                    values['table_comodel_inverse_name'] = input.table_comodel_inverse_name.name
            if input.compute_function:
                values['compute_function_name'] = input.compute_function.name 
                values['compute_function_fields'] = input.compute_function.fields.ids
            if input.is_auto_fill_mapping_fields_enabled or input.open_selection_modal_when_click:
                mapping_relation_fields = []
                values['is_auto_fill_mapping_fields_enabled'] = input.open_selection_modal_when_click if input.open_selection_modal_when_click else input.is_auto_fill_mapping_fields_enabled
                for record in input.mapping_relation_fields:
                    if record.origin_model_fields and record.origin_model_fields.name and record.inverse_model_fields and record.inverse_model_fields.name :
                        mapping_relation_fields.append([record.origin_model_fields.name,record.inverse_model_fields.name])
                values['mapping_relation_fields'] = mapping_relation_fields
                if input.connected_field_to_selection_modal:
                    values['connected_field_to_selection_modal'] = input.connected_field_to_selection_modal.read()
            if input.search_domain_inside_selection_modal:
                values['search_domain_inside_selection_modal'] = json.loads(input.search_domain_inside_selection_modal)
            if input.is_isolated_input and input.isolated_form:
                values['isolated_form'] = self.env['rb_delivery.mobile_form_input'].get_form(lang=lang,form=input.isolated_form)
                if input.is_stand_alone:
                    values.update({
                        'is_stand_alone':True,
                        'stand_alone_function':input.stand_alone_function
                    })
            if len(steps)>0:
                steps[len(steps)-1]['form_inputs'].append(values)
            else:
                inputs.append(values)
        if len(steps)>0:
            return {'model':form.model.model,'steps':steps,'show_toolbar':form.show_toolbar,'skip_success_message':form.skip_success_message}
        else:
            return {'model':form.model.model,'inputs':inputs,'show_toolbar':form.show_toolbar,'skip_success_message':form.skip_success_message}

    @api.multi  
    def unlink(self):
        for rec in self:
            if rec.is_isolated_input and len(rec.isolated_form):
                rec.isolated_form.unlink()
        return super(rb_delivery_mobile_form_input,self).unlink()
