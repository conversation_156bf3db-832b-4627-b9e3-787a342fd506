# -*- coding: utf-8 -*-
from openerp import models, fields, api,_


class rb_delivery_mobile_sort_and_distribute_items(models.Model):

    _name = 'rb_delivery.mobile_sort_and_distribute_items'
    _description = "Mobile Sort and distribute items"
    _inherit = 'mail.thread'

    from_status_id = fields.Many2one(
        'rb_delivery.status',
        string='From Status',
        track_visibility="on_change",
        required=True,
    )

    to_status_id = fields.Many2one(
        'rb_delivery.status',
        string='To Status',
        track_visibility="on_change",
        required=True,
    )


    sort_and_distribute_creator = fields.Many2one(comodel_name='rb_delivery.mobile_sort_and_distribute',string='Mobile sort and distribute')


    @api.multi
    def write(self, values):
        if 'from_status_id' in values and values['from_status_id']:
            for rec in self: 
                if rec.sort_and_distribute_creator:
                    sort_item = self.env['rb_delivery.mobile_sort_and_distribute_items'].search([('sort_and_distribute_creator', '=', rec.sort_and_distribute_creator.id), ('from_status_id', '=', values['from_status_id'])])
                    if sort_item: 
                        self.env['rb_delivery.error_log'].raise_olivery_error(801,self.id, {})


        

        return super(rb_delivery_mobile_sort_and_distribute_items, self).write(values)
    

    @api.model
    def create(self,values):
        if 'from_status_id' in values and values['from_status_id']:
            sort_item = self.env['rb_delivery.mobile_sort_and_distribute_items'].search([('sort_and_distribute_creator', '=', values['sort_and_distribute_creator']), ('from_status_id', '=', values['from_status_id'])])
            if sort_item: 
                self.env['rb_delivery.error_log'].raise_olivery_error(801,self.id, {})
        
        return super(rb_delivery_mobile_sort_and_distribute_items, self).create(values)
