# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

class rb_delivery_sub_area(models.Model):

    _name = 'rb_delivery.sub_area'
    _inherit = 'mail.thread'
    _order = "sequence, name ASC"
    _description = "Sub Area Model"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    name = fields.Char('Name', required=True, translate=True,track_visibility="on_change")

    code = fields.Char('Code', required=True,track_visibility="on_change")

    parent_id = fields.Many2one('rb_delivery.area', 'Area Group', required=True,track_visibility="on_change")

    drivers = fields.Many2many('rb_delivery.user',string='State Drivers', domain=_get_driver_users,track_visibility="on_change")

    area_parent_id = fields.Many2one(related='parent_id.parent_id', readonly=True)

    show_in_create = fields.Boolean('Show in create order form', default=True,track_visibility="on_change")

    show_in_register = fields.Boolean('Show in register user form', default=True,track_visibility="on_change")

    sequence = fields.Integer(string="Sequence", track_visibility=False)

    zone_id = fields.Many2one(
        comodel_name='rb_delivery.area_zone',
        string='Zone',
        relation='rb_area_zone_sub_area_rel',
        column1='sub_area_id',
        column2='zone_id',
        track_visibility='on_change'
    )

    # ----------------------------------------------------------------------
    # Constraints
    # ----------------------------------------------------------------------

    _sql_constraints = [
        ('name', 'Check(1=1)', 'Name already exists!'),
        ('code', 'unique(code)', 'Code already exists!')
        ]

    @api.model
    def create(self,values):
        if values.get('name'):
            values['name'] = values.get('name').strip()
        area = False
        if 'parent_id' in values and values['parent_id']:
            area = self.env['rb_delivery.area'].sudo().search([('id','=',values['parent_id'])])
        if 'code' not in values or ('code' in values and not values['code']):
            code = self.env['ir.sequence'].next_by_code('rb_delivery.sub_area')
            values['code'] = code
        if area and area.code:
            values['code'] = str(area.code) + '_' + str(values['code'])

        if 'name' in values and values['name'] and 'parent_id' in values and values['parent_id']:
           self.check_sub_area(values['name'],values['parent_id'])
        sub_area = super(rb_delivery_sub_area, self).create(values)
        sub_area.create_address_tags()
        return sub_area

    @api.one
    def write(self,values):
        if values.get('name'):
            values['name'] = values.get('name').strip()
        if ('name' in values and values['name']) or ('parent_id' in values and values['parent_id']):
            if 'name' in values and values['name']:
                sub_area_name = values['name']
            else:
                sub_area_name = self.name
            if 'parent_id' in values and values['parent_id']:
                parent_id = values['parent_id']
            else:
                parent_id = self.parent_id.id
            self.check_sub_area(sub_area_name,parent_id)
        if 'parent_id' in values and values['parent_id']:
            full_address = self.env['rb_delivery.address_tags'].search([('area_id', '=', self.parent_id.id), ('sub_area_id', 'in', self.ids)])
            if full_address:
                full_address.write({'area_id': values['parent_id']})
        old_zone_id = self.zone_id.id
        sub_area = super(rb_delivery_sub_area, self).write(values)
        self.create_address_tags()
        new_zone_id = values.get('zone_id')
        if old_zone_id != new_zone_id:
            domain = [('customer_sub_area', '=', self.id)]
            self.env['rb_delivery.utility'].update_orders_zone(new_zone_id, domain)
        return sub_area

    def check_sub_area(self,name,parent_id):
        sub_area = self.env['rb_delivery.sub_area'].sudo().search([('name','=',name),('parent_id','=',parent_id)],limit=1)
        if sub_area:
            self.env['rb_delivery.error_log'].raise_olivery_error(470,self.id,{'area_name':name, 'area_code':sub_area.code})
            #raise ValidationError(_("Area of name %s already exists with code %s")% (name,sub_area.code))

    def create_address_tags(self):
        self.env['rb_delivery.address_tags'].create_missing_address_tags(self)


    def copy(self, default=None):
        default = dict(default or {})
        default.update(
            { 'name': self.name + '_copy',
             'code': self.code + '_copy'})

        return super(rb_delivery_sub_area, self).copy(default)

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        if args is None:
            args = []
        if str(name).isnumeric():
            recs = self.search([('id', operator, name)] + args, limit=limit)
        else:
            recs = self.search([('name', operator, name)] + args, limit=limit)
        if not recs.ids:
            return super(rb_delivery_sub_area, self).name_search(name=name, args=args,operator=operator,limit=limit)
        return recs.name_get()