# -*- coding: utf-8 -*-

import logging
import requests

from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

_logger = logging.getLogger(__name__)

class rb_delivery_one_signal(models.Model):

    _name = 'rb_delivery.one_signal'
    _description = "One Signal Model"

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    name = fields.Char('Name', required=True)

    user_auth_key = fields.Char('Firebase id')

    app_auth_key = fields.Char('Rest API Key')
 
    app_id = fields.Char('App ID')

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
    def test_onesignal_connection(self):
        for record in self:
            url = f"https://onesignal.com/api/v1/apps/{record.app_id}"
            headers = {
                'Authorization': f'Basic {record.app_auth_key}',
                'Content-Type': 'application/json'
            }
            try:
                response = requests.get(url, headers=headers)
                if response.status_code == 200:
                    message = _('Connection successful')
                    self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))
                    return True
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(900,self.id,{'error':str(response.text)})
            except Exception as e:
                raise ValidationError(_("An error occurred: %s") % str(e))
