# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import requests
import json
import re
from datetime import  datetime
from datetime import timedelta
import pytz
import logging
_logger = logging.getLogger(__name__)
class rb_delivery_sms(models.Model):

    _name = 'rb_delivery.sms'
    _inherit = 'mail.thread'
    _description = "Sms Model"

    # ----------------------------------------------------------------------
    # Computed
    # ----------------------------------------------------------------------

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    username = fields.Char(track_visibility="on_change",string="Username")

    link = fields.Char('Link',track_visibility="on_change")

    password = fields.Char( string="Password",track_visibility="on_change")

    mobile_number = fields.Char('Mobile number',track_visibility="on_change")

    sender_id = fields.Char('Sender ID',track_visibility="on_change")

    content_type = fields.Char("Content Type",track_visibility="on_change")

    body_request = fields.Text("Request body",track_visibility="on_change")

    header = fields.Text("Header",track_visibility="on_change")

    use_body_request = fields.Boolean('Use body request',track_visibility="on_change",default=False)

    sms_item_ids = fields.Many2many(
        comodel_name = 'rb_delivery.sms_item',
        string = 'Sms Items',
        relation = 'sms_sms_item_items',
        column1 = 'sms_item_ids',
        column2 = 'sms_ids')

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    def send_sms(self,content,receiver_mobile_number,user_id=False,order_id=False,raise_exception=False,model_name=False):
        default_sms_provider = self.env['rb_delivery.client_configuration'].get_param('default_sms_provider')
        if default_sms_provider == 'twilio':
            self.send_twilio_sms(content,receiver_mobile_number,user_id,order_id,raise_exception)
        if default_sms_provider == 'kastana':
            self.send_text_sms(content,receiver_mobile_number)
        if default_sms_provider == 'general':
            return self.general_send_sms(content,receiver_mobile_number,order_id,model_name,raise_exception)

    def send_twilio_sms(self,content,receiver_mobile_number,user_id,order_id,raise_exception):
        from twilio.rest import Client
        twilio_information = self.env['rb_delivery.client_configuration'].get_param('twilio_information')
        sid = ''
        auth_token = ''
        messaging_service_sid = ''
        if 'sid' in twilio_information and twilio_information['sid']:
            sid = twilio_information['sid']
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(440,self.id,{})
            #raise ValidationError(_("Please add an SID to your twilio information."))
        if 'auth_token' in twilio_information and twilio_information['auth_token']:
            auth_token = twilio_information['auth_token']
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(441,self.id,{})
            #raise ValidationError(_("Please add an Auth Token to your twilio information."))

        if 'messaging_service_sid' in twilio_information and twilio_information['messaging_service_sid']:
            messaging_service_sid = twilio_information['messaging_service_sid']
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(442,self.id,{})
            #raise ValidationError(_("Please add a Messaging Service SID to your twilio information."))

        client = Client(sid, auth_token)
        no_space_first_number=receiver_mobile_number.strip()
        if no_space_first_number[0]=='0':
            no_space_first_number=no_space_first_number[1:]
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        mobile_number = prefix_one+no_space_first_number
        try:
            message = client.messages.create(
                messaging_service_sid=messaging_service_sid,
                body=content,
                to=mobile_number)
            if message and message.sid:
                print(message.sid)
                if order_id:
                    order = order_id
                    if order:
                        order.message_post(body=_("SMS was send with SID %s") % (message.sid))
                if user_id:
                    user = self.env['rb_delivery.user'].search([('id','=',user_id)])
                    if user:
                        user.message_post(body=_("SMS was send with SID %s") % (message.sid))
        except Exception as e:
            if order_id:
                order = self.env['rb_delivery.order'].search([('id','=',order_id)])
                if order:
                    order.message_post(body=_("SMS failed to send because of %s") % (e.msg))
            if user_id:
                user = self.env['rb_delivery.user'].search([('id','=',user_id)])
                if user:
                    user.message_post(body=_("SMS failed to send because of %s") % (e.msg))
            if raise_exception:
                raise ValidationError(_(e.msg))


    def send_text_sms(self,content,receiver_mobile_number):
        import requests
        import json

        no_space_first_number=receiver_mobile_number.strip()
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        if no_space_first_number[0]=='0':
            no_space_first_number=no_space_first_number[1:]
        mobile_number = prefix_one+no_space_first_number
        sms_info = self.env['rb_delivery.sms'].sudo().search([])[0]
        if sms_info and sms_info[0]:
            link = sms_info.link
            username = sms_info.username
            password = sms_info.password
            mobile = sms_info.mobile_number
            url = link+'?username='+username+'&password='+password+'&body='+content+'&numbers='+mobile_number+'&language=en'
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(443,self.id,{})
            #raise ValidationError(_('please add your sms information'))
        # mobile_number = '962798435779'
        if url:
            params = {"jsonrpc": "2.0","params": {}}
            headers = {'content-type': 'application/json'}
            response = requests.post(url, data=json.dumps(params), headers=headers)
            print(response)

    def send_basic_sms(self,mobile_number,sms_info,link,order_id,content,raise_exception=False):
        add_plus_sign = self.env['rb_delivery.client_configuration'].get_param('add_plus_sign_for_sms')
        url = ''
        username_field_name = ''
        password_field_name = ''
        sender_id_field_name = ''
        mobile_field_name = ''
        message_field_name = ''
        customer_mobile_field_name = ''
        order_id_field_name = ''
        sender_id = ''
        username = ''
        password = ''
        mobile = ''
        for item in sms_info.sms_item_ids:
            if item.field_name == 'username':
                if sms_info.username:
                    username_field_name = item.name
                    username = sms_info.username
                else:
                    ValidationError(_('please add username in sms information'))
            elif item.field_name == 'message':
                message_field_name = item.name
            elif item.field_name == 'mobile_numbers':
                customer_mobile_field_name = item.name
            elif item.field_name == 'password':
                if sms_info.password:
                    password_field_name = item.name
                    password = sms_info.password
                else:
                    ValidationError(_('please add password in sms information'))
            elif item.field_name == 'sender_id':
                if sms_info.sender_id:
                    sender_id_field_name = item.name
                    sender_id = sms_info.sender_id
                else:
                    ValidationError(_('please add Sender ID in sms information'))
            elif item.field_name == 'mobile_number':
                if sms_info.mobile_number:
                    mobile_field_name = item.name
                    mobile = sms_info.mobile_number
                else:
                    ValidationError(_('please add Mobile Number in sms information'))
            elif item.field_name == 'order_id':
                order_id_field_name = item.name
        if customer_mobile_field_name and link and message_field_name:
            if mobile_number and mobile_number[0] != '+' and add_plus_sign:
                mobile_number = '+'+mobile_number
            url = link+'?' + customer_mobile_field_name + '=' + mobile_number + '&' + message_field_name + '=' + content
            if sender_id_field_name and sender_id:
                url = url + '&' + sender_id_field_name + '=' + sender_id
            if username_field_name and username:
                url = url + '&' + username_field_name + '=' + username
            if password_field_name and password:
                url = url + '&' + password_field_name + '=' +password
            if mobile_field_name and mobile:
                url = url + '&' + mobile_field_name + '=' + mobile
            if order_id_field_name and order_id:
                url = url + '&' + order_id_field_name + '=' + str(order_id)
        if url:
            url = url.replace('+','%2b')
            url = url.replace(' ','%20')
            if sms_info[0].content_type:
                content_type = sms_info[0].content_type
            else:
                content_type = 'application/json'
            params = {"jsonrpc": "2.0","params": {}}
            headers = {'content-type': content_type}
            response = requests.get(url, data=json.dumps(params), headers=headers)
            print(response.text)
            return response.text
        elif raise_exception:
            self.env['rb_delivery.error_log'].raise_olivery_error(444,self.id,{})
            


    def get_sms_body(self,body_request,message,mobile_number,record_id,sms_info):
        variables = re.findall(r'\(.*?\)', body_request)
        for variable in variables:
            variable_str = variable.strip('()')
            if variable_str == 'message':
                body_request = body_request.replace(variable, '"'+str(message)+'"')
            elif variable_str == 'mobile_number':
                body_request = body_request.replace(variable, '"'+str(mobile_number)+'"')
            elif variable_str == 'record_id':
                body_request = body_request.replace(variable, '"'+str(record_id)+'"')
            elif variable_str == 'sender_mobile' and sms_info.mobile_number:
                body_request = body_request.replace(variable, '"'+str(sms_info.mobile_number)+'"')
            elif variable_str == 'date':
                timezone = self._context.get('tz') or self.env.user.tz or 'Asia/Hebron'
                date = datetime.now(pytz.timezone(timezone))+ timedelta(hours = 2)
                date = datetime.strftime(date, "%d/%m/%y %H:%M")
                body_request = body_request.replace(variable, '"'+str(date)+'"')
        return body_request

    def send_generic_sms(self,sms_info,url,model_name,content,receiver_mobile_number,record_id=False):
        if not model_name:
            model_name = 'rb_delivery.order'
        body_request = sms_info.body_request
        header = json.loads(sms_info.header)
        body_request = self.get_sms_body(body_request,content,receiver_mobile_number,record_id,sms_info)
        body_request = body_request.replace('\n','')
        response = requests.request('POST',url=url, data=body_request.encode('utf-8'), headers=header)
        _logger.info(body_request)
        _logger.info(response.text)
        return response.text

    def general_send_sms(self,content,receiver_mobile_number,model_name,record_id=False,raise_exception=False):

        no_space_first_number=receiver_mobile_number.strip()
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        if no_space_first_number[0]=='0':
            no_space_first_number=no_space_first_number[1:]
        mobile_number = prefix_one+no_space_first_number

        sms_info = self.env['rb_delivery.sms'].sudo().search([])[0]
        if sms_info and sms_info[0]:
            link = sms_info.link
            if not sms_info[0].use_body_request:
                return self.send_basic_sms(mobile_number,sms_info,link,record_id,content,raise_exception)
            else:
                return self.send_generic_sms(sms_info[0],link,model_name,content,receiver_mobile_number,record_id)
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(443,self.id,{})
            #raise ValidationError(_('please add your sms information'))