# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from odoo.exceptions import ValidationError

class rb_delivery_security_matrix(models.Model):
    _name = 'rb_delivery.security_matrix'
    _inherit = 'mail.thread'
    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]
            
        


    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups,track_visibility="on_change")

    menu_access_ids = fields.One2many(comodel_name='rb_delivery.menu_access',inverse_name='security_matrix_id',string='Views',store=True, ondelete='cascade')

    action_access_ids = fields.One2many(comodel_name='rb_delivery.action_access',inverse_name='security_matrix_id',string='Views',store=True, ondelete='cascade')

    report_access_ids = fields.One2many(comodel_name='rb_delivery.report_access',inverse_name='security_matrix_id',string='Views',store=True, ondelete='cascade')


    def _all_descendants(self, menus):
        """Return a recordset with menus + every submenu under them."""
        to_check = menus
        all_rs = menus
        while to_check:
            children = self.env['ir.ui.menu'].sudo().search([
                ('parent_id', 'in', to_check.ids)
            ])
            new_kids = children - all_rs
            if not new_kids:
                break
            all_rs |= new_kids
            to_check = new_kids
        return all_rs

    def update_menu_access_ids(self):
        if not self.group_id:
            return
        if self.menu_access_ids:
            self.menu_access_ids.sudo().unlink()

        roots = (
            self.env.ref('rb_delivery.rb_delivery_settings') |
            self.env.ref('rb_delivery.rb_delivery_top_menu')
        )
        
        all_menus = self._all_descendants(roots)
        access_vals = [{'menu_id': m.id} for m in all_menus]
        created = self.env['rb_delivery.menu_access'].sudo().create(access_vals)
        self.sudo().menu_access_ids = [(6, 0, created.ids)]

    def update_action_access_ids(self):
        if self.group_id:
            if len(self.action_access_ids):
                self.action_access_ids.sudo().unlink()
            action_items = self.env['ir.actions.act_window'].sudo().search(['|','|',['res_model','ilike','storex'],['res_model','ilike','rb_delivery'],['xml_id','ilike','olivery']])

            created_action_access_ids = []

            for action_item in action_items:
                created_view = self.env['rb_delivery.action_access'].sudo().create({
                    'action_id': action_item.id
                })
                created_action_access_ids.append(created_view.id)

            self.sudo().action_access_ids = [(6, 0, created_action_access_ids)]

    def update_report_access_ids(self):
        if self.group_id:
            if len(self.report_access_ids):
                self.report_access_ids.sudo().unlink()
            report_items = self.env['ir.actions.report'].sudo().search(['|','|',['report_name','ilike','storex'],['report_name','ilike','rb_delivery'],['xml_id','ilike','olivery']])

            created_report_access_ids = []
            
            for report_item in report_items:
                created_view = self.env['rb_delivery.report_access'].sudo().create({
                    'report_id': report_item.id
                })
                created_report_access_ids.append(created_view.id)

            self.sudo().report_access_ids = [(6, 0, created_report_access_ids)]

    @api.constrains('group_id')
    def _on_change_group_id(self):
        self.refresh()

    def refresh(self):
        self.update_menu_access_ids()
        self.update_action_access_ids()
        self.update_report_access_ids()
         




    





    
    
