
<odoo>
  <data>
    <record id="view_form_rb_delivery_mobile_card_item" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_mobile_card_item</field>
      <field name="model">rb_delivery.mobile_card_item</field>

      <field name="arch" type="xml">
        <form>

          <header>
            <!-- Buttons and status widget -->
          </header>
          <sheet>

            <group name="group_top">
              <field name="model" invisible="1"/>
              <field name="card_creator_fields" invisible="1"/>
              <field name="card_creator" invisible="1"/>
              <field name="card_item" invisible="1"/>
              <field name="card_item_model" invisible="1"/>
              <field name="has_card_items" invisible="1"/>
              <field name="field_ttype" invisible="1"/>
              <field name="field_relation" invisible="1"/>
              <field name="sub_position_name" invisible="1"/>
              <group name="group_right" >
                <field name="is_button" attrs="{'invisible':['|','|',['is_label','=',True],['is_hyperlink','=',True],['is_empty_feild','=',True]]}"/>
                <field name="is_hyperlink" attrs="{'invisible':['|','|',['is_label','=',True],['is_button','=',True],['is_empty_feild','=',True]]}"/>
                <field name="is_label" attrs="{'invisible':['|','|',['is_hyperlink','=',True],['is_button','=',True],['is_empty_feild','=',True]]}"/>
                <field name="is_empty_feild" attrs="{'invisible':['|',['is_hyperlink','=',True],['is_button','=',True]]}"/>
                <field name="keep_show_while_collapse" attrs="{'invisible':['|',['is_hyperlink','=',True],['is_button','=',True]]}"/>                                    
                <field name="hide_empty_field"/>
                <field name="replace_empty_field"/>
                <field name="invisible_domain"/>
                <field name="user_invisible_domain" widget="domain" options="{'model': 'rb_delivery.user','in_dialog': true}"/>
                <field name="hyperlink_type" attrs="{'invisible':[['is_hyperlink','=',False]]}"/>
                <field name="hyperlink_field" attrs="{'invisible':[['is_hyperlink','=',False]]}"  domain="['|',['model_id','=',model],['model_id','=',card_item_model],'|',['ttype','=','char'],['ttype','=','text']]"/>
                <field name="position" attrs="{'invisible':[['model','=',False]]}"/>
                <field name="sub_position" widget="selection"/>
                <field name="local_compute_function"/>
              </group>
              <group name="group_left" attrs="{'invisible':[['is_button','=',False]]}">
                <field name="button_theme"/>
                <field name="label"/>
                <field name="button_icon_selction"/>
                <field name="button_icon"/>
                <field name="button_function" domain="[['model','=',model]]" attrs="{'required':[['is_button','=',True]]}"/>
                <field name="background"/>
                <field name="font_color" string="Font color"/>
              </group>
              <group name="group_right" string="Values" attrs="{'invisible':['|','|',['is_button','=',True],['is_empty_feild','=',True],['has_card_items','=',True]]}">
                <field name="field" attrs="{'invisible':[['is_label','=',True],['is_button','=',True]],'required':[['is_label','=',False],['is_empty_feild','=',False],['is_button','=',False],['is_hyperlink','=',False],['has_card_items','=', False]]}"  domain="['|',['model_id','=',model],['model_id','=',card_item_model]]"/>
                <field name="image_field_name" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}" domain="[['model','=',field_relation],['ttype','=','binary']]"/>
                <field name="have_image" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}"/>
                <field name="color_field_name" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}"/>
                <field name="secondary_color_field_name" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}"/>
                <field name="button_theme"/>
                <field name="have_color" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}"/>
                <field name="title"/>
                <field name="label" attrs="{'required':[['is_label','=',True],['is_empty_feild','=',False]]}"/>
                <field name="is_monetary" attrs="{'invisible':[['field_ttype','!=','float'],['field_ttype','!=','integer'],['field_ttype','!=','char']]}"/>
                <field name="copy_when_clicked" attrs="{'invisible':[['sub_position_name','!=','bubble']]}"/>
                <field name="button_function" domain="[['model','=',model]]"/>
                <field name="button_icon_selction"/>
                <field name="button_icon" string="icon"/>
                <field name="background"/>
                <field name="font_color" string="Font color"/>
              </group>
            </group>
            
            <group>
              <field name="card_items" attrs="{'invisible':['|','|','|','|','|',['position','!=','content'],['model','=',False],['field','!=',False],['is_label','=',True],['is_button','=',True],['is_empty_feild','=',True]]}">
                <tree>
                  <field name="display_name"/>
                  <field name="label"/>
                  <field name="sequence" widget="handle"/>
                </tree>
              </field>
              <field name="item_actions" attrs="{'invisible':['|','|','|','|','|',['position','!=','content'],['model','=',False],['field','=',False],['is_label','=',True],['is_button','=',True],['is_empty_feild','=',True]]}">
                <tree>
                  <field name="action_type"/>
                  <field name='sequence' widget='handle'/>
                </tree>
              </field>
            </group>
            
          </sheet>
        </form>

      </field>
    </record>

  
    <!-- Sub Position Demo Data -->
    <record id="sub_position_bubble" model="rb_delivery.card_sub_position">
                <field name="name">bubble</field>
                <field name="parent_position">header</field>
    </record> 

    <record id="sub_position_title" model="rb_delivery.card_sub_position">
                <field name="name">title</field>
                <field name="parent_position">header</field>
    </record> 
    <record id="sub_position_sub_title" model="rb_delivery.card_sub_position">
                <field name="name">sub_title</field>
                <field name="parent_position">header</field>
    </record> 
    <record id="sub_position_sub_bubble" model="rb_delivery.card_sub_position">
                <field name="name">sub_bubble</field>
                <field name="parent_position">header</field>
    </record> 
    <record id="sub_position_side" model="rb_delivery.card_sub_position">
                <field name="name">side</field>
                <field name="parent_position">content</field>
    </record> 
    <record id="sub_position_notes_section" model="rb_delivery.card_sub_position">
      <field name="name">notes_section</field>
      <field name="parent_position">content</field>
</record> 
    <record id="sub_position_main" model="rb_delivery.card_sub_position">
                <field name="name">main</field>
                <field name="parent_position">content</field>
    </record> 
    <record id="sub_position_start" model="rb_delivery.card_sub_position">
                <field name="name">start</field>
                <field name="parent_position">footer</field>
    </record> 
    <record id="sub_position_end" model="rb_delivery.card_sub_position">
                <field name="name">end</field>
                <field name="parent_position">footer</field>
    </record> 

    <!-- Model Functions Demo Data -->
    <record id="edit_order_mobile_function" model="rb_delivery.card_model_functions">
                <field name="name">Edit Order</field>
                <field name="technical_name">editOrder</field>
                <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record> 

    <record id="client_order_history_mobile_function" model="rb_delivery.card_model_functions">
                <field name="name">Client Order History</field>
                <field name="technical_name">clientOrderHistory</field>
                <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record> 

    <record id="update_reference_order_mobile_function" model="rb_delivery.card_model_functions">
          <field name="name">Update Reference</field>
          <field name="technical_name">updateReference</field>
          <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record>

    <record id="update_address_note_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Update Address Note</field>
      <field name="technical_name">updateAddressNote</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record>

    <record id="solve_stuck_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Solve Stuck</field>
      <field name="technical_name">solveStuck</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record>

    <record id="edit_note_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Edit Note</field>
      <field name="technical_name">editNote</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record>

    <record id="add_note_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Add note</field>
      <field name="technical_name">addNote</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
  </record> 
  <record id="add_follow_order_mobile_function" model="rb_delivery.card_model_functions">
        <field name="name">Add follow order</field>
        <field name="technical_name">addFollowOrder</field>
        <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
  </record> 
    
    <record id="open_location_selector_order_mobile_function" model="rb_delivery.card_model_functions">
          <field name="name">Open Location Selector</field>
          <field name="technical_name">openLocationsAlert</field>
          <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
          <field name="required_fields" eval="[[6,0,[
                  ref('rb_delivery.field_rb_delivery_order__business_longitude'),
                  ref('rb_delivery.field_rb_delivery_order__business_latitude'),
                  ref('rb_delivery.field_rb_delivery_order__longitude'),
                  ref('rb_delivery.field_rb_delivery_order__latitude')
                ]]]"></field>
    </record>

    <record id="call_customer_mobile_function" model="rb_delivery.card_model_functions">
                <field name="name">Call Customer</field>
                <field name="technical_name">callCustomer</field>
                <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
                <field name="required_fields" eval="[[6,0,[
                  ref('rb_delivery.field_rb_delivery_order__customer_mobile'),
                  ref('rb_delivery.field_rb_delivery_order__second_mobile_number'),
                  ref('rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile'),
                  ref('rb_delivery.field_rb_delivery_order__cus_second_whatsapp_mobile')
                ]]]"></field>
    </record> 

    <record id="order_history_mobile_function" model="rb_delivery.card_model_functions">
                <field name="name">Show Order History</field>
                <field name="technical_name">showOrderHistory</field>
                <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
                <field name="required_fields" eval="[[6,0,[
                  ref('rb_delivery.field_rb_delivery_order__create_date'),
                  ref('rb_delivery.field_rb_delivery_order__write_date'),
                  ref('rb_delivery.field_rb_delivery_order__state_id'),
                ]]]"></field>
    </record> 
    <record id="call_sender_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Call Sender</field>
      <field name="technical_name">callSender</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
      <field name="required_fields" eval="[[6,0,[
        ref('rb_delivery.field_rb_delivery_order__business_mobile_number'),
        ref('rb_delivery.field_rb_delivery_order__second_business_mobile_number'),
        ref('rb_delivery.field_rb_delivery_order__business_whatsapp_mobile'),
        ref('rb_delivery.field_rb_delivery_order__business_second_whatsapp_mobile')
      ]]]"></field>
  </record> 

    <record id="call_sender_money_collection_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Call Sender</field>
      <field name="technical_name">callSender</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_multi_print_orders_money_collector')"></field>
      <field name="required_fields" eval="[[6,0,[
        ref('rb_delivery.field_rb_delivery_multi_print_orders_money_collector__mobile_number'),
        ref('rb_delivery.field_rb_delivery_multi_print_orders_money_collector__second_business_mobile_number'),
        ref('rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_second_whatsapp_mobile')
      ]]]"></field>
  </record>
  <record id="message_sender_order_money_collection_mobile_function" model="rb_delivery.card_model_functions">
    <field name="name">Message Sender</field>
    <field name="technical_name">messageSender</field>
    <field name="model" eval="ref('rb_delivery.model_rb_delivery_multi_print_orders_money_collector')"></field>
    <field name="required_fields" eval="[[6,0,[
      ref('rb_delivery.field_rb_delivery_multi_print_orders_money_collector__mobile_number'),
      ref('rb_delivery.field_rb_delivery_multi_print_orders_money_collector__second_business_mobile_number')
    ]]]"></field>
  </record>
  <record id="call_sender_returned_money_collection_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Call Sender</field>
      <field name="technical_name">callSender</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_returned_money_collection')"></field>
      <field name="required_fields" eval="[[6,0,[
        ref('rb_delivery.field_rb_delivery_returned_money_collection__mobile_number'),
        ref('rb_delivery.field_rb_delivery_returned_money_collection__second_business_mobile_number'),
        ref('rb_delivery.field_rb_delivery_returned_money_collection__business_second_whatsapp_mobile')
      ]]]"></field>
  </record>
  <record id="message_sender_order_returned_money_collection_mobile_function" model="rb_delivery.card_model_functions">
    <field name="name">Message Sender</field>
    <field name="technical_name">messageSender</field>
    <field name="model" eval="ref('rb_delivery.model_rb_delivery_returned_money_collection')"></field>
    <field name="required_fields" eval="[[6,0,[
      ref('rb_delivery.field_rb_delivery_returned_money_collection__mobile_number'),
      ref('rb_delivery.field_rb_delivery_returned_money_collection__second_business_mobile_number')
    ]]]"></field>
  </record>

  <record id="call_agent_mobile_function" model="rb_delivery.card_model_functions">
    <field name="name">Call Agent</field>
    <field name="technical_name">callAgent</field>
    <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    <field name="required_fields" eval="[[6,0,[
      ref('rb_delivery.field_rb_delivery_order__agent_mobile_number'),
      ref('rb_delivery.field_rb_delivery_order__second_agent_mobile_number'),
      ref('rb_delivery.field_rb_delivery_order__agent_whatsapp_mobile'),
      ref('rb_delivery.field_rb_delivery_order__agent_second_whatsapp_mobile')
    ]]]"></field>
  </record> 

  <record id="call_agent_mobile_function" model="rb_delivery.card_model_functions">
    <field name="name">Call Agent</field>
    <field name="technical_name">callAgent</field>
    <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    <field name="required_fields" eval="[[6,0,[
      ref('rb_delivery.field_rb_delivery_order__agent_mobile_number'),
      ref('rb_delivery.field_rb_delivery_order__second_agent_mobile_number'),
      ref('rb_delivery.field_rb_delivery_order__agent_whatsapp_mobile'),
      ref('rb_delivery.field_rb_delivery_order__agent_second_whatsapp_mobile')
    ]]]"></field>
  </record> 


  <record id="message_sender_order_mobile_function" model="rb_delivery.card_model_functions">
    <field name="name">Message Sender</field>
    <field name="technical_name">messageSender</field>
    <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    <field name="required_fields" eval="[[6,0,[
      ref('rb_delivery.field_rb_delivery_order__business_mobile_number'),
      ref('rb_delivery.field_rb_delivery_order__second_business_mobile_number')
    ]]]"></field>
  </record> 

    <record id="print_order_mobile_function" model="rb_delivery.card_model_functions">
                <field name="name">Print Order</field>
                <field name="technical_name">printOrder</field>
                <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record> 

    <record id="message_order_mobile_function" model="rb_delivery.card_model_functions">
                <field name="name">Message Customer</field>
                <field name="technical_name">messageCustomer</field>
                <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
                <field name="required_fields" eval="[[6,0,[
                  ref('rb_delivery.field_rb_delivery_order__customer_mobile'),
                  ref('rb_delivery.field_rb_delivery_order__second_mobile_number')
                ]]]"></field>
    </record> 

    <record id="message_agent_order_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Message Driver</field>
      <field name="technical_name">messageAgent</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
      <field name="required_fields" eval="[[6,0,[
        ref('rb_delivery.field_rb_delivery_order__agent_mobile_number'),
        ref('rb_delivery.field_rb_delivery_order__second_agent_mobile_number')
      ]]]"></field>
    </record> 

    <record id="message_sender_order_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Message Sender</field>
      <field name="technical_name">messageSender</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
      <field name="required_fields" eval="[[6,0,[
        ref('rb_delivery.field_rb_delivery_order__business_mobile_number'),
        ref('rb_delivery.field_rb_delivery_order__second_business_mobile_number')
      ]]]"></field>
    </record> 

    <record id="chat_order_mobile_function" model="rb_delivery.card_model_functions">
                <field name="name">Chat Order</field>
                <field name="technical_name">chatOrder</field>
                <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record> 

    <record id="more_details_order_mobile_function" model="rb_delivery.card_model_functions">
                <field name="name">More Details</field>
                <field name="technical_name">showMoreDetails</field>
                <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record> 

    <record id="edit_user_mobile_function" model="rb_delivery.card_model_functions">
                <field name="name">Edit User</field>
                <field name="technical_name">editUser</field>
                <field name="model" eval="ref('rb_delivery.model_rb_delivery_user')"></field>
    </record> 

    <record id="confirm_user_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Confirm User</field>
      <field name="technical_name">confirmUser</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_user')"></field>
    </record> 

    <record id="deactivate_user_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Deactivate User</field>
      <field name="technical_name">deactivateUser</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_user')"></field>
    </record>
    <record id="call_user_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Call User</field>
      <field name="technical_name">callUser</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_user')"></field>
      <field name="required_fields" eval="[[6,0,[
        ref('rb_delivery.field_rb_delivery_user__mobile_number'),
        ref('rb_delivery.field_rb_delivery_user__second_mobile_number'),
        ref('rb_delivery.field_rb_delivery_user__whatsapp_mobile'),
        ref('rb_delivery.field_rb_delivery_user__second_whatsapp_mobile')
      ]]]"></field>
</record> 

    <record id="download_report_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Download report</field>
      <field name="technical_name">downloadReport</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_report_job_queue')"></field>
    </record>

    <record id="change_status_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Change State</field>
      <field name="technical_name">changeStatus</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
    </record> 

    <record id="change_status_multi_print_orders_money_collector_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Change State</field>
      <field name="technical_name">changeStatus</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_multi_print_orders_money_collector')"></field>
    </record> 

    <record id="drivr_role_showAttach" model="rb_delivery.card_model_functions">
      <field name="name">Show Attachment</field>
      <field name="technical_name">showAttachment</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_order')"></field>
      <field name="required_fields" eval="[(6, 0, [ref('rb_delivery.field_rb_delivery_order__attachment_ids')])]"/>
    </record>  

    <record id="change_status_returned_money_collection_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Change State</field>
      <field name="technical_name">changeStatus</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_returned_money_collection')"></field>
    </record> 

    <record id="change_status_agent_money_collection_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Change State</field>
      <field name="technical_name">changeStatus</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_agent_money_collection')"></field>
    </record> 

    <record id="change_status_agent_returned_collection_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Change State</field>
      <field name="technical_name">changeStatus</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_agent_returned_collection')"></field>
    </record> 
    
    <record id="change_status_runsheet_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Change State</field>
      <field name="technical_name">changeStatus</field>
      <field name="model" eval="ref('rb_delivery.model_rb_delivery_runsheet')"></field>
    </record> 

    <record id="location_mobile_function" model="rb_delivery.card_model_functions">
      <field name="name">Location Button</field>
      <field name="technical_name">geoLocation</field>
    </record>

        <record id="view_tree_rb_delivery_mobile_card_item" model="ir.ui.view">

      <field name="name">view_tree_rb_delivery_mobile_card_item</field>
      <field name="model">rb_delivery.mobile_card_item</field>

      <field name="arch" type="xml">
        <tree create='false'>
          <field name="id"/>
        </tree>

      </field>
    </record>
  
    </data>     
</odoo>
