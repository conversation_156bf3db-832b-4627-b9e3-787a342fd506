from odoo import models, fields, api
from odoo.tools.translate import _

class AgentCommissionRange(models.Model):
    _name = 'rb_delivery.agent_commission_range'
    _description = 'Agent Commission Range'
    _order = 'from_amount'

    driver_id = fields.Many2one(
        'rb_delivery.user',
        string=_('Driver'),
        required=True,
        ondelete='cascade',
        help=_('Select the driver to whom this commission range applies.')
    )

    from_amount = fields.Float(
        _('From'),
        required=True,
        help=_('The lower bound of the order amount for which this commission applies.')
    )

    to_amount = fields.Float(
        _('To'),
        required=True,
        help=_('The upper bound of the order amount for which this commission applies.')
    )

    commission_type = fields.Selection(
        [
            ('fixed', 'Fixed Amount'),
            ('percentage', 'Percentage')
        ],
        string='Commission Type',
        required=True,
        help=_('Choose whether the commission is a fixed amount or a percentage of the order total.')
    )

    value = fields.Float(
        'Value',
        required=True,
        help=_('Enter the commission value. If percentage type is selected, enter the percentage number (e.g., 10 for 10%).')
    )

    @api.constrains('from_amount', 'to_amount', 'driver_id')
    def _check_range_validity(self):
        for rec in self:
            if rec.to_amount <= rec.from_amount:
                self.env['rb_delivery.error_log'].raise_olivery_error(870, rec.id, {})
            overlapping = self.env['rb_delivery.agent_commission_range'].sudo().search([
                ('driver_id', '=', rec.driver_id.id),
                ('id', '!=', rec.id),
                ('from_amount', '<=', rec.to_amount),
                ('to_amount', '>=', rec.from_amount)
            ], limit=1)
            if overlapping:
                self.env['rb_delivery.error_log'].raise_olivery_error(871, rec.id, {
                    'from': overlapping.from_amount,
                    'to': overlapping.to_amount
                })
