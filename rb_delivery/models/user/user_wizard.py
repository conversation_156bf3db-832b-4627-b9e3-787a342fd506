# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

import logging
_logger = logging.getLogger(__name__)

class order_select_confirm_wizard(models.TransientModel):
    _name = 'rb_delivery.select_confirm'
    _description = "Select Confirm Model"

    @api.multi
    def select_confirm(self):
        recs = self.env['rb_delivery.user'].browse(
            self._context.get('active_ids'))

        for rec in recs:
            user = self.env['rb_delivery.user'].search([('id','=',rec.id)])
            if user.password:
                user.wkf_action_confirm()
        return True

class user_select_pricelist_wizard(models.TransientModel):
    _name = 'rb_delivery.select_pricelist'
    _description = "Select Pricelist Model"

    def get_pricelist(self):
        ids = []
        pricelists = self.env['rb_delivery.pricelist'].search([])
        for pricelist in pricelists:
            ids.append(pricelist.id)
        return [('id', 'in', ids)]

    pricelist = fields.Many2one(
        'rb_delivery.pricelist', 'Select Pricelist', domain=get_pricelist)

    @api.multi
    def select_pricelist(self):
        recs = self.env['rb_delivery.user'].browse(
            self._context.get('active_ids'))

        for rec in recs:
            user = self.env['rb_delivery.user'].search([('id','=',rec.id)])
            user.write({'pricelist_id': self.pricelist.id})
        return True

class user_select_previous_pricelist_wizard(models.TransientModel):
    _name = 'rb_delivery.select_previous_pricelist'
    _description = "Select Previous Pricelist Model"

    @api.multi
    def select_previous_pricelist(self):
        recs = self.env['rb_delivery.user'].browse(
            self._context.get('active_ids'))

        for rec in recs:
            user = self.env['rb_delivery.user'].search([('id','=',rec.id)])
            if user.previous_pricelist_id:
                user.write({'pricelist_id': user.previous_pricelist_id.id,'previous_pricelist_id': user.pricelist_id.id})
        return True

class user_select_reconfirm_wizard(models.TransientModel):
    _name = 'rb_delivery.select_reconfirm'
    _description = "Select Reconfirm Model"

    @api.multi
    def select_reconfirm(self):
        recs = self.env['rb_delivery.user'].browse(
            self._context.get('active_ids'))

        for rec in recs:
            if rec.state != 'pending':
                rec.wkf_action_reconfirm()
        return True


class user_select_archive_wizard(models.TransientModel):
    _name = 'rb_delivery.select_archive'
    _description = "Select Archive Model"

    @api.multi
    def select_archive(self):
        users = self.env['rb_delivery.user'].browse(
            self._context.get('active_ids'))

        for user in users:
            if user.user_id.id == self._uid:
                self.env['rb_delivery.error_log'].raise_olivery_error(530,self.id,{})
                #raise ValidationError(_('You are not allow to archive yourself'))
            user.wkf_action_archive()
        return True

class user_select_unarchive_wizard(models.TransientModel):
    _name = 'rb_delivery.select_unarchive'
    _description = "Select Unarchive Model"

    @api.multi
    def select_unarchive(self):
        users = self.env['rb_delivery.user'].browse(
            self._context.get('active_ids'))

        for user in users:
            if user.mobile_number:
                user.check_mobile_number(user.mobile_number, archive=1)
            user.wkf_action_unarchive()
        return True


class rb_delivery_notify_users(models.TransientModel):
    _name = 'rb_delivery.notify_users'
    _description = "Notify Users Model"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    header = fields.Char('Message Title')

    message = fields.Text(string="Message")

    template = fields.Many2one('mail.template', 'Template', domain=[('model_id','=','rb_delivery.user')])

    images = fields.Binary(string='Images')
    
    link = fields.Text(string='Link')

    notification_type = fields.Selection(selection='get_notification_type', string='Notification Type', track_visibility="on_change", copy=False, default="is_notification")

    @api.model
    def get_notification_type(self):
        notification_type = [
                ('is_email','Email'),
                ('is_notification','Notification'),
                ('is_sms','Sms'),
                ('is_announcement', 'Announcment'),
            ]
        return notification_type

    def notify(self):
        players = []
        users = []
        mobile_numbers = []
        emails = []
        is_email = False
        is_notification = False
        is_sms = False
        is_announcement = False
        if self.notification_type == 'is_email':
            is_email = True
        elif self.notification_type == 'is_notification':
            is_notification = True
        elif self.notification_type == 'is_sms':
            is_sms = True
        elif self.notification_type == 'is_announcement':
            is_announcement = True
        if self.template:
            self.header = self.template.subject
            self.message = self.template.body_html
        users = self.env['rb_delivery.user'].browse(self._context.get('active_ids'))
        for user in users:
            if user.email:
                emails.append(user.email)
            if user.player_id:
                players.append(str(user.player_id))
            if user.mobile_number:
                mobile_numbers.append(str(user.mobile_number))
        try:
            if is_announcement:
                announcement = self.env['rb_delivery.announcement'].create({
                  'title': self.header,
                  'description': self.message,
                  'link': self.link,
                  'images': self.images,
                  'users': [(6, 0, users.ids)],
                })
                announcement.action_custom_button()
            else:
                self.env['rb_delivery.notification_center'].notification(users, emails, players, mobile_numbers, self.header, self.message, is_sms, is_email, is_notification,None,None,'rb_delivery.user',notification_group_type="UserGroup")
        except Exception as e:
            _logger.info("Error in notify user: "+str(e))

class user_add_password_wizard(models.TransientModel):
    _name = 'rb_delivery.add_password'
    _description = "Add Password Model"


    password = fields.Char('Password', required=True)

    @api.one
    def save(self):
        recs = self.env['rb_delivery.user'].browse(
            self._context.get('active_ids'))

        for rec in recs:
            rec.write({'password': self.password})
            rec.write({'state': 'confirmed'})
        return True
