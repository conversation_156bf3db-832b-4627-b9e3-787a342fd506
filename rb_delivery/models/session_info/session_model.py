from odoo import models


class Http(models.AbstractModel):
    _inherit = 'ir.http'

    def session_info(self):
        res = super(Http, self).session_info()
        ability_to_export_data = True
        group_codes = []
        conf = self.env['rb_delivery.client_configuration'].sudo().get_param(['roles_who_can_not_export_data','is_public_link_button_visible','show_create_order_button','show_payout_order_button','default_search','global_dicimal_value','number_of_records_to_be_loaded_at_the_same_time', 'roles_to_hide_create_order_button', 'use_month_names_in_group_by','roles_to_hide_public_link_button','roles_to_hide_quick_order_button'])
        roles_id = conf['roles_who_can_not_export_data']
        is_public_link_button_visible = conf['is_public_link_button_visible']
        show_create_order_button = conf['show_create_order_button']
        show_payout_order_button = conf['show_payout_order_button']
        roles_to_hide_create_order_button = conf['roles_to_hide_create_order_button']
        roles_to_hide_quick_order_button = conf['roles_to_hide_quick_order_button']
        roles_to_hide_public_link_button = conf['roles_to_hide_public_link_button']
        default_search = conf['default_search']
        global_dicimal_value = conf['global_dicimal_value']
        number_of_records_to_be_loaded_at_the_same_time = conf['number_of_records_to_be_loaded_at_the_same_time']
        use_month_names_in_group_by = conf['use_month_names_in_group_by']
        user = self.env.user
        if user:
            group_ids = user.groups_id
            for group_id in group_ids:
                if group_id.code:
                    group_codes.append(group_id.code)
            if roles_id:
                for group_id in group_ids.ids:
                    if group_id in roles_id:
                        ability_to_export_data = False
        user_info = self.env['rb_delivery.user'].search_read([('user_id','=',self._uid)],['id', 'user_id', 'state', 'username', 'mobile_number', 'area_id', 'email', 'address', 'group_id', 'role_name','role_code','inclusive_delivery','commercial_name','has_customers','player_id','forgot_password','online','is_block_delivery_fee','block_delivery_profit','default_payment_type','default_payment_detail','bank_name','bank_number','wallet_name', 'wallet_number','holder_name','show_editable_button','show_fields_button'],limit=1)

        if user_info and len(user_info) > 0 :
            res.update({
            'user_info' : user_info[0]
            })

        res.update({
            'ability_to_export_data': ability_to_export_data,
            'group_codes': group_codes,
            'is_public_link_button_visible':is_public_link_button_visible,
            'show_create_order_button':show_create_order_button,
            'show_payout_order_button':show_payout_order_button,
            'roles_to_hide_create_order_button': roles_to_hide_create_order_button,
            'roles_to_hide_quick_order_button':roles_to_hide_quick_order_button,
            'roles_to_hide_public_link_button':roles_to_hide_public_link_button,
            'default_search':default_search,
            'global_dicimal_value':global_dicimal_value,
            'number_of_records_to_be_loaded_at_the_same_time':number_of_records_to_be_loaded_at_the_same_time,
            'use_month_names_in_group_by':use_month_names_in_group_by
        })
        return res