<odoo>
  <data>
    <!-- Tree View with Search and Grouping -->
    <record id="view_tree_archive_order" model="ir.ui.view">
      <field name="name">view_tree_archive_order</field>
      <field name="model">rb_delivery.archive_order</field>
      <field name="arch" type="xml">
        <tree string="Archived Orders">
          <field name="archived_date" string="Archived Date"/>
          <field name="sender_name" string="Sender Name"/>
          <field name="receiver_name" string="Receiver Name"/>
          <field name="sender_mobile" string="Sender Mobile"/>
          <field name="receiver_area" string="Receiver Area"/>
          <field name="receiver_sub_area" string="Receiver Sub Area"/>
          <field name="receiver_full_address" string="Receiver Full Address"/>
          <field name="receiver_mobile_number" string="Receiver Mobile Number"/>
        </tree>
      </field>
    </record>
    <record id="view_search_archive_order" model="ir.ui.view">
      <field name="name">view_search_archive_order</field>
      <field name="model">rb_delivery.archive_order</field>
      <field name="arch" type="xml">
        <search>
          <field name="archived_date"/>
          <field name="sender_name"/>
          <field name="receiver_name"/>
          <field name="sender_mobile"/>
          <field name="receiver_area"/>
          <field name="receiver_sub_area"/>
            <group string="Groups">
            <filter name="group_by_archived_date" string="By Archived Date" domain="[]" context="{'group_by': 'archived_date'}" />
            <filter name="group_by_sender_name" string="By Sender Name" domain="[]" context="{'group_by': 'sender_name'}" />
            <filter name="group_by_sender_mobile" string="By Mobile Sender" domain="[]" context="{'group_by': 'sender_mobile'}" />
             <filter name="group_by_receiver_name" string="By Receiver Name" domain="[]" context="{'group_by': 'receiver_name'}" />
            <filter name="group_by_receiver_sub_area" string="By Receiver Sub Area" domain="[]" context="{'group_by': 'receiver_sub_area'}" />
            <filter name="group_by_receiver_area" string="By Receiver Area" domain="[]" context="{'group_by': 'receiver_area'}" />
            </group>
        </search>

      </field>
    </record>

  </data>
</odoo>