from odoo import models, fields

class ArchiveOrder(models.Model):

    _name = 'rb_delivery.archive_order'
    _description = 'Archived Order'

    archived_date = fields.Datetime(string="Archived Date", default=fields.Datetime.now,readonly=True)

    sender_name = fields.Char(string="Sender Name",readonly=True)

    receiver_name = fields.Char(string="Receiver Name",readonly=True)

    sender_mobile = fields.Char(string="Sender Mobile",readonly=True)

    receiver_area = fields.Char(string="Receiver Area",readonly=True)

    receiver_sub_area = fields.Char(string="Receiver Sub Area",readonly=True)

    receiver_full_address = fields.Text(string="Receiver Full Address",readonly=True)

    receiver_mobile_number = fields.Char(string="Receiver Mobile Number",readonly=True)