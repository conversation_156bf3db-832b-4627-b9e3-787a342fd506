
<odoo>
  <data>
    <record id="view_form_rb_delivery_notification_center" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_notification_center</field>
      <field name="model">rb_delivery.notification_center</field>

      <field name="arch" type="xml">
        <form edit="false">

          <header>
            <!-- Buttons and status widget -->
          </header>

          <sheet>

            <group name="group_top">
              <group name="group_left">
              <field name="model_name"/>
                <field name="user_id"/>
                <field name="order_id"/>
                <field name="sequence"/>
              </group>
              <group name="group_right">
                <field name="notification_type"/>
                <field name="notification_title"/>
                <field name="notification_message"/>
                <field name="seen"/>
                <field name="is_chat_notification"/>   
                <field name="is_announcement"/>                
              </group>
            </group>
          </sheet>
        </form>

      </field>
    </record>


    <record id="view_tree_rb_delivery_notification_center" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_notification_center</field>
      <field name="model">rb_delivery.notification_center</field>

      <field name="arch" type="xml">
        <tree>
          <field name="model_name"/>
          <field name="user_id"/>
          <field name="order_id"/>
          <field name="sequence"/>
          <field name="notification_type"/>
          <field name="notification_title"/>
          <field name="notification_message"/>
          <field name="seen"/>
          <field name="create_uid" options="{'no_open': True}"/>
          <field name="create_date"/>
        </tree>
      </field>

    </record>

    <record id="view_search_rb_delivery_notification_center" model="ir.ui.view">
      <field name="name">view_search_rb_delivery_notification_center</field>
      <field name="model">rb_delivery.notification_center</field>

      <field name="arch" type="xml">
        <search >

        <group>
            <field name="name"/>
            <field name="order_id"/>
              <field name="sequence"/>
            <field name="record_id"/>
        </group>

           <group string="Groups">
            <filter name="group_by_model" string="By Model" icon="terp-partner" context="{'group_by':'name'}"/>
            <filter name="group_by_user" string="By User" icon="terp-partner" context="{'group_by':'user_id'}"/>
            <filter name="group_by_seen" string="By Seen" icon="terp-partner" context="{'group_by':'seen'}"/>
            <filter name="group_by_date" string="By Date" icon="terp-partner" context="{'group_by':'create_date'}"/>
            </group>
        </search>
      </field>

    </record>
    
  </data>
</odoo>
