# -*- coding: utf-8 -*-
from datetime import date
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import requests
import json
import os
import ast
from odoo import models, api
from functools import lru_cache


class rb_delivery_client_configuration(models.Model):

    _name = 'rb_delivery.client_configuration'
    _description = 'Client Parameter'
    _rec_name = 'key'
    _order = 'key'
    _inherit = 'mail.thread'
    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]


    def _get_default_model(self):
        model_id = self.env['ir.model'].sudo().search([('model','=',"rb_delivery.order")]).id
        return model_id

    #inherit module [olivery_branch_collection]
    @api.model
    def get_collection_type(self):
        collection_types = [('collection','Collection'),('returned_collection','Returned Collection'),('agent_collection','Agent Collection'),('agent_returned_collection','Agent Returned Collection'),('runsheet_collection','Runsheet Collection')]
        return collection_types

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    key = fields.Char('key', required=True,track_visibility="on_change")

    value = fields.Boolean("Value", required=True,track_visibility="on_change")

    status = fields.Many2many(comodel_name = 'rb_delivery.status',
        string = 'Status',
        relation = 'status_table',
        column1 = 'status_id',track_visibility="on_change")

    status_type = fields.Selection([('olivery_order','Olivery Order'),('storex_order','Storex Order'),('olivery_collection','Olivery Collection')], 'Status Type',default='olivery_order',track_visibility="on_change")

    collection_type = fields.Selection(selection='get_collection_type', string='Collection Type',track_visibility="on_change")

    related_to_status = fields.Boolean('Related to status',track_visibility="on_change")

    related_to_order_type = fields.Boolean('Related to order type',track_visibility="on_change")

    order_type = fields.Many2many(comodel_name = 'rb_delivery.order_type',
        string = 'Order Type',
        relation = 'order_type_conf',
        column1 = 'order_type_id',track_visibility="on_change")

    related_to_text = fields.Boolean('Related to Text',track_visibility="on_change")

    related_to_search = fields.Boolean('Related to Search',track_visibility="on_change")

    related_to_sms = fields.Boolean('Related to Text',track_visibility="on_change")

    related_to_group_ids = fields.Boolean('Related to Role',track_visibility="on_change")

    related_to_report = fields.Boolean('Related to Report',track_visibility="on_change")

    report_id = fields.Many2one(comodel_name = 'ir.actions.report',
        string = 'Report Name',
        relation = 'report_name_conf',
        column1 = 'conf_report_id',track_visibility="on_change", domain=[])

    platform_type = fields.Selection([
        ('web','Web'),
        ('mobile','Mobile'),
        ('web_mobile','Web and Mobile')
    ],track_visibility="on_change")

    text = fields.Char("Text",track_visibility="on_change")

    text_message = fields.Text("Text Message",track_visibility="on_change")

    description = fields.Char("Description",track_visibility="on_change")

    configuration_type_id = fields.Many2one("rb_delivery.client_configuration_type","Configuration type",track_visibility="on_change")

    group_ids = fields.Many2many('res.groups', string="Role", domain=get_groups,track_visibility="on_change")

    related_to_returned_discount = fields.Boolean('Related to returned discount',track_visibility="on_change")

    fixed_discount = fields.Integer('Fixed discount',track_visibility="on_change")

    returned_discount_type = fields.Selection([('0', 'Default'),('1', 'Fixed Discount'),('2', 'Based on pricelist')], track_visibility="on_change")

    search_default = fields.Selection([('all', 'All'),('sequence', 'Sequence'),('reference', 'Reference ID'),('partner_reference', 'Partner Reference ID'),('barcodes', 'Barcodes')], default="barcodes",track_visibility="on_change")

    partial_search = fields.Boolean("Partial search",default=True,track_visibility="on_change")

    sms_provider = fields.Selection([('twilio', 'Twilio'),('kastana', 'Kastana'),('general','General')],"SMS Provider", default="twilio",track_visibility="on_change")

    related_to_twilio = fields.Boolean("Related to twilio",track_visibility="on_change")

    related_to_vhub = fields.Boolean("Related to VHub",track_visibility="on_change")

    twilio_sid = fields.Char('Twilio SID',track_visibility="on_change")

    twilio_auth_token = fields.Char('Twilio Auth Token',track_visibility="on_change")

    twilio_messaging_service_sid = fields.Char('Twilio Messaging Service SID',track_visibility="on_change")

    related_to_user_ids = fields.Boolean('Related to User')

    related_to_text_message = fields.Boolean('Related to text message')

    user_ids = fields.Many2one('rb_delivery.user',string="Users")

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'order_client_configuration',
        column1 = 'client_configuration_id',
        column2 = 'order_id')

    all = fields.Char('All')

    user_id = fields.Many2one('rb_delivery.user',string="User")

    model_id = fields.Many2one('ir.model',default=_get_default_model)

    field_ids = fields.Many2many(comodel_name = 'ir.model.fields', string = 'Fields shown', relation = 'fields_client_configuration_item', column1 = 'client_configuration_id', column2 = 'field_id')

    admin_required = fields.Boolean('Admin required to change')

    attachment = fields.Binary('Attachment')

    related_to_field_ids = fields.Boolean('Related to Fields',track_visibility="on_change")

    # ----------------------------------------------------------------------
    # Constraints
    # ----------------------------------------------------------------------

    _sql_constraints = [
        ('key', 'unique(key)', 'Key already exists!')]

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------


    @api.model
    def get_param(self, keys):
        """Batch fetch and cache parameters, returning a single value if only one key is requested."""
        if not hasattr(self.env, '_param_cache'):
            self.env._param_cache = {}
        if isinstance(keys, str):
            keys = [keys]
            single_key = True
        else:
            single_key = False

        missing_keys = [key for key in keys if key not in self.env._param_cache]
        if missing_keys:
            if 'roles_who_can_not_export_data' in missing_keys:
                fetched_params = self.search_read([('key', 'in', missing_keys)],['key','value','search_default','related_to_search', 'partial_search','related_to_text','text','related_to_group_ids','group_ids'])
                for param in fetched_params:
                    value = self._extract_param_value(param)
                    self.env._param_cache[param['key']] = value
            else:
                fetched_params = self.search_read(
                    [('key', 'in', missing_keys)],
                    ['key', 'value', 'related_to_status', 'status', 'related_to_text', 'text',
                    'related_to_group_ids', 'group_ids', 'related_to_sms', 'sms_provider',
                    'related_to_returned_discount', 'returned_discount_type', 'fixed_discount',
                    'twilio_sid', 'twilio_auth_token', 'twilio_messaging_service_sid',
                    'related_to_twilio', 'related_to_user_ids', 'user_ids', 'related_to_text_message',
                    'text_message', 'related_to_search', 'search_default', 'partial_search',
                    'related_to_field_ids', 'field_ids','related_to_order_type','order_type','related_to_report','report_id']
                )

                for param in fetched_params:
                    value = self._extract_param_value(param)
                    self.env._param_cache[param['key']] = value

        if single_key:
            return self.env._param_cache.get(keys[0], False)
        return {key: self.env._param_cache.get(key, False) for key in keys}

    @api.model
    def _extract_param_value(self, param):
        """Extract the parameter value based on its type."""
        if param.get('related_to_report'):
            return param.get('report_id', None)[0] if param.get('report_id', None) else None
        if param.get('related_to_order_type'):
            return param.get('order_type', None)
        if param.get('related_to_status'):
            return param.get('status', None)
        if param.get('related_to_text'):
            return param.get('text', "")
        if param.get('related_to_group_ids'):
            return param.get('group_ids', None)
        if param.get('related_to_sms'):
            return param.get('sms_provider', None)
        if param.get('related_to_returned_discount'):
            return {'type': param.get('returned_discount_type'), 'value': param.get('fixed_discount')}
        if param.get('related_to_twilio'):
            return {
                'sid': param.get('twilio_sid'),
                'auth_token': param.get('twilio_auth_token'),
                'messaging_service_sid': param.get('twilio_messaging_service_sid')
            }
        if param.get('related_to_user_ids'):
            return param.get('user_ids', None)
        if param.get('related_to_text_message'):
            return param.get('text_message', "")
        if param.get('related_to_search'):
            return {'search_default': param.get('search_default'), 'partial_search': param.get('partial_search')}
        if param.get('related_to_field_ids'):
            return param.get('field_ids', None)
        return param.get('value', None)




    # @api.model
    # def _get_param(self, key):
    #     params = self.search_read([('key', '=', key)], ['related_to_status', 'status', 'related_to_text', 'text', 'related_to_group_ids', 'group_ids',
    #                                                     'related_to_sms', 'sms_provider', 'related_to_returned_discount', 'returned_discount_type',
    #                                                     'twilio_sid', 'twilio_auth_token', 'twilio_messaging_service_sid', 'related_to_twilio',
    #                                                     'related_to_user_ids', 'user_ids', 'related_to_text_message', 'text_message', 'related_to_search',
    #                                                     'search_default','partial_search', 'related_to_field_ids', 'field_ids', 'value', 'fixed_discount'
    #                                                     ], limit=1)
    #     if params and params[0]['related_to_status']:
    #         return params[0]['status'] if params and params[0]['related_to_status'] else None
    #     elif params and params[0]['related_to_text']:
    #         return params[0]['text'] if params and params[0]['related_to_text'] else ""
    #     elif params and params[0]['related_to_group_ids']:
    #         return params[0]['group_ids'] if params and params[0]['related_to_group_ids'] else None
    #     elif params and params[0]['related_to_sms']:
    #         return params[0]['sms_provider'] if params and params[0]['related_to_sms'] else None
    #     elif params and params[0]['related_to_returned_discount']:
    #         return {'type':params[0]['returned_discount_type'],'value':params[0]['fixed_discount']} if params and params[0]['related_to_returned_discount'] else None
    #     elif params and params[0]['related_to_twilio']:
    #         return {'sid':params[0]['twilio_sid'],'auth_token':params[0]['twilio_auth_token'],'messaging_service_sid':params[0]['twilio_messaging_service_sid']} if params and params[0]['related_to_twilio'] else None
    #     elif params and params[0]['related_to_user_ids']:
    #         return params[0]['user_ids'] if params and params[0]['related_to_user_ids'] else None
    #     elif params and params[0]['related_to_text_message']:
    #         return params[0]['text_message'] if params and params[0]['related_to_text_message'] else ""
    #     elif params and params[0]['related_to_search']:
    #         return {'search_default':params[0]['search_default'],'partial_search':params[0]['partial_search']} if params and params[0]['related_to_search'] else None
    #     elif params and params[0]['related_to_field_ids']:
    #         return params[0]['field_ids'] if params and params[0]['related_to_field_ids'] else None
    #     elif params:
    #         return params[0]['value'] if params else None
    @api.model
    def get_field_ids(self,key):
        params = self.search([('key', '=', key)], limit=1)
        fields_ids = []
        for field in params.field_ids:
            fields_ids.append(field.name)
        return fields_ids

    def migrate_agent_collection_function(self):
        collections = self.env['rb_delivery.agent_money_collection'].sudo().search([])
        batch_list = []
        batch_list = [collections[i:i + 1000] for i in range(0, len(collections), 1000)]
        for batch in batch_list:
            self.with_delay(channel="root.migrate_agent_collection",max_retries=2).update_collection_financials(batch)
        return True

    def migrate_returned_agent_collection_function(self):
        collections = self.env['rb_delivery.agent_returned_collection'].sudo().search([])
        batch_list = []
        batch_list = [collections[i:i + 1000] for i in range(0, len(collections), 1000)]
        for batch in batch_list:
            self.with_delay(channel="root.migrate_returned_agent_collection",max_retries=2).update_collection_financials(batch)
        return True

    def migrate_returned_collection_function(self):
        collections = self.env['rb_delivery.returned_money_collection'].sudo().search([])
        batch_list = []
        batch_list = [collections[i:i + 1000] for i in range(0, len(collections), 1000)]
        for batch in batch_list:
            self.with_delay(channel="root.migrate_returned_collection",max_retries=2).update_collection_financials(batch)
        return True

    def migrate_collection_function(self):
        collections = self.env['rb_delivery.multi_print_orders_money_collector'].sudo().search([])
        batch_list = []
        batch_list = [collections[i:i + 1000] for i in range(0, len(collections), 1000)]
        for batch in batch_list:
            self.with_delay(channel="root.migrate_collection",max_retries=2).update_collection_financials(batch)
        return True

    def update_collection_financials(self,collections):
        for collection in collections:
            collection.compute_company_profit_total()


    def check_collection(self,orders):
        username = self.env.user.name
        for order in orders:
            collection_id = self.env['rb_delivery.multi_print_orders_money_collector'].sudo().search([('order_ids','=',order.id)],limit=1)
            returned_collection_id = self.env['rb_delivery.returned_money_collection'].sudo().search([('order_ids','=',order.id)],limit=1)
            runsheet_collection_id = self.env['rb_delivery.runsheet'].sudo().search([('order_ids','=',order.id)],limit=1)
            agent_collection_id = self.env['rb_delivery.agent_money_collection'].sudo().search([('order_ids','=',order.id)],limit=1)
            agent_returned_collection_id = self.env['rb_delivery.agent_returned_collection'].sudo().search([('order_ids','=',order.id)],limit=1)

            if collection_id:
                order.write({'collection_id':collection_id.id})
            if returned_collection_id:
                order.write({'returned_collection_id':returned_collection_id.id})
            if runsheet_collection_id:
                order.write({'runsheet_collection_id':runsheet_collection_id.id})
            if agent_collection_id:
                order.write({'agent_collection_id':agent_collection_id.id})
            if agent_returned_collection_id:
                order.write({'agent_returned_collection_id':agent_returned_collection_id.id})
            if collection_id or returned_collection_id or runsheet_collection_id or agent_collection_id or agent_returned_collection_id:
                message=_("Order has been updated with collection value by %s through the system it self for migration purpose, you can check with support to know more about it.")%(username)
                data = {'uid':self._uid,'message':message,'records':order,'values':{},'update':False}
                self.env['rb_delivery.utility'].olivery_sudo(data)
        return True

    def send_status(self): #used in vhub
        if len(self.order_ids):
            orders = self.order_ids
            for order in orders:
                order.write({"state":"in_progress"})
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(400,self.id, {})
            #raise ValidationError(_("Please add orders."))

    def send_order(self):
        if self.user_id and len(self.order_ids):
            user = self.user_id
            group = user.group_id
            order_ids = self.order_ids
            orders = []
            if group.code == 'rb_delivery.role_driver' and user.is_company:
                for order in order_ids:
                    vals = {
                        "customer_address": order.customer_address,
                        "customer_mobile": order.customer_mobile,
                        "customer_name": order.customer_name,
                        "customer_area": order.customer_area.name,
                        "reference_id":order.sequence,
                        "follower_store_name":order.assign_to_business.username,
                        "follower_mobile_number":order.assign_to_business.mobile_number,
                        "follower_second_mobile_number":order.assign_to_business.second_mobile_number,
                        "follower_area":order.assign_to_business.area_id.name,
                        "follower_address":order.assign_to_business.address,
                        "note":order.note,
                        "second_mobile_number":order.second_mobile_number,
                        "cus_whatsapp_mobile":order.cus_whatsapp_mobile,
                        "cus_second_whatsapp_mobile":order.cus_second_whatsapp_mobile,
                        "order_weight":order.order_weight,
                        "product_note":order.product_note,
                        "longitude":order.longitude,
                        "latitude":order.latitude,
                        "is_partner_order":True}
                    if order.assign_to_business.inclusive_delivery:
                        vals['cost'] = order.copy_total_cost
                    else:
                        vals['cost'] = order.money_collection_cost
                    if order.second_mobile_number:
                        vals['second_mobile_number'] = order.second_mobile_number
                    orders.append(vals)
                if user.sudo().company_username and user.sudo().company_password and user.sudo().company_db and user.sudo().company_url:
                    self.with_delay(channel="root.vhub",max_retries=2).send_vhub_order(orders,user)
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(401,self.id, {'delivery_company': user.sudo().company_username})
                    #raise ValidationError(_("User must have company url, company password and company username."))

            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(402,self.id, {})
                #raise ValidationError(_("User must be a driver and company user."))
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(403,self.id, {})
            #raise ValidationError(_("Please add a user and an order."))

    def send_vhub_order(self,orders,user):
        params = {
            "jsonrpc": "2.0",
            "params": {
            "login":user.sudo().company_username,
            "password": user.sudo().company_password,
            "db":user.sudo().company_db,
            "orders_list":orders}}
        headers = {'content-type': 'application/json'}
        response = requests.post(user.sudo().company_url+'/create_multi_orders', data=json.dumps(params), headers=headers)
        messages =[]
        if response.status_code == 200:
            if response.json().get('result') and response.json().get('result').get('Sequences'):
                seqs = ''
                order_ref_list = response.json().get('result').get('References')
                for rec in order_ref_list:
                    order = self.env['rb_delivery.order'].search([('sequence','=',rec)])
                    order_seq =  response.json().get('result').get('Sequences')[order_ref_list.index(rec)]
                    order.write({'assign_to_agent':user.id,'is_sender_partner_order':True,'partner_reference_id':order_seq})
                    seqs = seqs + response.json().get('result').get('Sequences')[order_ref_list.index(rec)] + ' '
                    messages.append(str(len(order_ref_list))+_(' Orders has been succesfully sent to ') + user.sudo().company_db)
                    if 'Fail Message' in response.json().get('result') :
                        for mes in response.json().get('result').get('Fail Message'):
                            messages.append(mes)
            else:
                if response.json().get('result'):
                    response_messages = response.json().get('result').get("Message")
                    if isinstance(response_messages,list):
                        for message in response_messages:
                            messages.append(message)
                    else:
                        messages.append(response_messages)

                else:
                    fail_message = response.json().get('error').get('data').get('message')
                    messages.append(fail_message)
        else:
            message = response.json().get('error').get('data').get('message')
            messages.append(message)
        self.env['rb_delivery.utility'].send_toast('for_user', messages , str(self._uid))
        return messages

    @api.model
    def create(self,values):
        if values['key'] == 'default_delay_time':
            self.action_rb_delivery_delayed_report(values['text'])
        if values['key'] == 'default_status_financial_order':
            self.action_rb_delivery_financial_orders(values['status'])
        if values['key'] == 'default_status_financial_order_logs' and values.get('state'):
            self.action_rb_delivery_financial_order_logs(values['status'])
        if values['key'] == 'default_status_active_order' or values['key'] == 'consider_clone_when_get_dashboard_item':
            self.action_rb_delivery_active_orders(values,True)
        return super(rb_delivery_client_configuration, self).create(values)

    def get_collections(self):

        orders = self.env['rb_delivery.order'].sudo().search([])
        batch_list = []
        for order in orders:

            batch_list.append(order)
            if len(batch_list)==1000:
                self.with_delay(channel="root.collections",max_retries=2).check_collection(batch_list)
                batch_list.clear()
        return True

    def check_prevent_from_agent_collection(self,values):
        if values.get('value'):
            show_warn = self.get_param('show_warning_if_not_from_agent_collection')
            if show_warn:
                show_warn_rec = self.env['rb_delivery.client_configuration'].search([('key','=','show_warning_if_not_from_agent_collection')])
                show_warn_rec.write({'value':False})
        return
    
    def check_warning_from_agent_collection(self,values):
        if values.get('value'):
            prevent_collection = self.get_param('prevent_if_not_from_agent_collection')
            if prevent_collection:
                prevent_collection_rec = self.env['rb_delivery.client_configuration'].search([('key','=','prevent_if_not_from_agent_collection')])
                prevent_collection_rec.write({'value':False})
        return
    
    @api.one
    def write(self,values):
        if self.admin_required and self._uid != 2:
            self.env['rb_delivery.error_log'].raise_olivery_error(404,self.id, {})
        if self.key == 'default_delay_time':
            if 'text' in values and values['text']:
                self.action_rb_delivery_delayed_report(values['text'])
        if self.key == 'default_status_returned_order':
            if 'status' in values and values['status']:
                self.action_rb_delivery_returned_orders(values['status'])
        if self.key == 'default_status_financial_order':
            if 'status' in values and values['status']:
                self.action_rb_delivery_financial_orders(values['status'])
        if self.key == 'default_status_active_order' or self.key == 'consider_clone_when_get_dashboard_item':
            self.action_rb_delivery_active_orders(values)

        if self.key == 'prevent_if_not_from_agent_collection':
            self.check_prevent_from_agent_collection(values)
        if self.key == 'show_warning_if_not_from_agent_collection':
            self.check_warning_from_agent_collection(values)

        if self.key == 'default_status_financial_order_logs':
            if 'status' in values and values['status']:
                self.action_rb_delivery_financial_order_logs(values['status'])
        old_values = {field: self[field].ids if isinstance(self[field], models.BaseModel) else self[field] for field in values}


        result = super(rb_delivery_client_configuration, self).write(values)

        # Now log the changes
        self.log_changes(old_values, values)

        return result

    def log_changes(self, old_values, new_values):
        messages = []
        for field in new_values:
            field_definition = self._fields.get(field)
            if not field_definition:
                continue

            model_name = field_definition.comodel_name
            field_label = field_definition.string

            old_value = old_values[field]
            new_value = self[field].ids if isinstance(self[field], models.BaseModel) else self[field]

            if isinstance(old_value, list) and isinstance(new_value, list):
                old_status_names = self.get_status_names(model_name, old_value)
                new_status_names = self.get_status_names(model_name, new_value)
                message = f'{field_label} changed from {old_status_names or None} to {new_status_names or None} <br />'

            else:
                continue

            messages.append(message)

        messages_str = ''.join(messages)
        if messages_str:
            self.message_post(body=messages_str)


    def get_status_names(self, model_name, ids):
        status_records = self.env[model_name].browse(ids)
        return [status.name for status in status_records]

    def action_rb_delivery_delayed_report(self,text):
        rec = self.env.ref('rb_delivery.action_rb_delivery_delayed_report')
        domain = "['|',('state','=','in_progress'),('state','=','in_branch'),('write_date','<=',(datetime.date.today()-relativedelta(days="+text+")).strftime('%Y-%m-%d'))]"
        rec.sudo().write({'domain':domain})
        return True

    def action_rb_delivery_financial_order_logs(self,status):
        rec = self.env.ref('rb_delivery.action_rb_delivery_order_cost_logs')
        statuses = status[0][2]
        financial_states = self.env['rb_delivery.status'].browse(statuses).mapped('name')
        domain = [('order_status','in',financial_states)] if financial_states else []
        origin_domain = ast.literal_eval(rec.domain) if rec.domain else []
        sub_origin_domain = [item for item in origin_domain if item[0] != 'order_status']
        rec.sudo().write({'domain': sub_origin_domain + domain})
        return True
    
    def action_rb_delivery_financial_orders(self,status):
        rec = self.env.ref('rb_delivery.action_rb_delivery_financial_order')
        statuses = status[0][2]
        financial_states = []
        for state_id in statuses:
            state = self.env['rb_delivery.status'].search([('id','=',state_id)])
            financial_states.append(state.name)
        domain = [('state','in',financial_states)]
        rec.sudo().write({'domain':domain})
        return True

    def action_rb_delivery_active_orders(self,values,create=False):
        rec = self.env.ref('rb_delivery.action_rb_delivery_order')
        origin_domain = ast.literal_eval(rec.domain) if rec.domain else []
        sub_origin_domain = []
        for item in origin_domain:
            if item[0] not in ('state_id', 'state', 'is_cloned', 'is_replacement'):
                sub_origin_domain.append(item)
        if self.key == 'default_status_active_order' and values.get('status'):

            statuses = values['status'][0][2]
            domain = [('state_id','in',statuses)]
        else:
            statuses = self.env.ref('rb_delivery.client_configuration_default_status_active_order').status.ids if not create else False
            domain = [('state_id','in',statuses)]

        if self.key == 'consider_clone_when_get_dashboard_item' and 'value' in values:
            if values['value']:
                domain = [('state_id','in',statuses)]
            else:
                 domain += [('is_cloned','=',False),('is_replacement','=',False)]
        else:
            get_clone_orders = self.env.ref('rb_delivery.client_configuration_consider_clone_when_get_dashboard_item').value if not create else values['value']
            if get_clone_orders:
                domain = [('state_id','in',statuses)]
            else:
                domain += [('is_cloned','=',False),('is_replacement','=',False)]
        new_origin_domain = domain+sub_origin_domain
        rec.sudo().write({'domain':new_origin_domain})
        return True

    def action_rb_delivery_operation_orders(self,status):
        rec = self.env.ref('rb_delivery.action_rb_delivery_opertation_order')
        statuses = status[0][2]
        operation_states = []
        for state_id in statuses:
            state = self.env['rb_delivery.status'].search([('id','=',state_id)])
            operation_states.append(state.name)
        domain = [('state','in',operation_states)]
        rec.sudo().write({'domain':domain})
        return True

    @api.constrains('key', 'value', 'text')
    def _check_underscore(self):
        for record in self.filtered(lambda r: r.key in ['clone_order_postfix', 'clone_order_prefix']):
            if record.text and '_' in record.text:
                self.env['rb_delivery.error_log'].raise_olivery_error(405,record.id, {})
                #raise ValidationError(_("The '_' symbol cannot be used in 'text' when key is 'clone_order_postfix' or 'clone_order_prefix'"))
