from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json
from openerp.http import request, Response

class rb_delivery_mobile_compute_fields_function(models.Model):

    _name = 'rb_delivery.mobile_compute_functions'

    name = fields.Char('Function name')

    model = fields.Many2one('ir.model')

    fields = fields.Many2many('ir.model.fields')

    @api.model
    def do_function(self,formValues,inputs):
        updated_inputs = []
        inputs = self.sort_input_based_on_priority(inputs)
        for input in inputs:
            if 'compute_function_name' in input and  input['compute_function_name']:
                updated_input = self.call_function(input,formValues)
                updated_inputs.append(updated_input)
                if updated_input and isinstance(updated_input['value'], dict):
                    formValues[updated_input['field']['name']] = updated_input['value'].get('id', False)
                elif updated_input:
                    formValues[updated_input['field']['name']] = updated_input['value']

        return updated_inputs

    def call_function(self,input,formValues):
        method_to_call=getattr(rb_delivery_mobile_compute_fields_function,input['compute_function_name'])
        return method_to_call(self,formValues,input)
    
    def sort_input_based_on_priority(self,inputs):
        sorted_inputs = []
        for input in inputs:
            if 'compute_function_name' in input and input['compute_function_name'] and 'compute_function_fields' in input and input['compute_function_fields'] and 'field' in input and input['field'] and 'id' in input['field'] and input['field']['id']:
                for item in inputs:
                    if  'compute_function_name' in item and item['compute_function_name'] and 'compute_function_fields' in item and item['compute_function_fields'] and item['field']['id'] in input['compute_function_fields']:
                        sorted_inputs.append(item)
        for input in inputs:
            if input not in sorted_inputs:
                sorted_inputs.append(input)

        return sorted_inputs
    
    @api.model
    def compute_partial_delivered_swtich_core(self,values,input):
        order_type = values.get('order_type_id',False)
        if input and input['field'] and input['field']['name'] == 'is_minus_payment' :
            order_types = request.env['rb_delivery.client_configuration'].sudo().get_param('order_types_accept_minus_values')
            delivered_partial_order_type = self.env['rb_delivery.order_type'].search([('id', 'in', order_types)])
            if order_type and order_type == delivered_partial_order_type.id:
                input['invisible'] = False
            else:
                input['invisible'] = True
                input['validations']['required'] = False
        return input
    
    @api.model
    def check_inclusive_delivery_business(self,values,input):
        business = values.get('assign_to_business',False)
        paid = values.get('paid',False)
        is_minus = values.get('is_minus_payment', False)
        input_name = input['field']['name'] if input and input['field'] else None
        if business:
            business = self.env['rb_delivery.user'].search([('id','=',business)])
        else:
            user = self.env['rb_delivery.user'].sudo().search([('user_id','=',self._uid)])
            if user and user.role_code == 'rb_delivery.role_business':
                business = user
        if paid:
            input['invisible'] = True
            input['validations']['required'] = False
            return input
        if business:
            if input_name == 'cost' :
                if business and business.inclusive_delivery :
                    input['invisible'] = True
                    input['validations']['required'] = False
                else:
                    input['invisible'] = False
                
                if is_minus: 
                    input['field']['placeholder'] = 'REQUIRED_TO_PAY'
            elif input_name == 'copy_total_cost' : 
                if business and not business.inclusive_delivery :
                    input['invisible'] = True
                    input['validations']['required'] = False
                else:
                    input['invisible'] = False
        return input
    
    @api.model
    def check_address_tag_assigned(self,values,input):
        address_tag_id = values.get('address_tag_id',False)
        if address_tag_id and input and input.get('field'):
            address_tag = self.env['rb_delivery.address_tags'].browse(address_tag_id)
            field_name = input['field']['name']
            if field_name == 'customer_area':
                input['value'] = {'id': address_tag.area_id.id, 'display_name': address_tag.area_id.display_name}
            elif field_name == 'customer_sub_area':
                input['value'] = {'id': address_tag.sub_area_id.id, 'display_name': address_tag.sub_area_id.display_name}
            input['field']['readonly'] = True
        return input
    
    @api.model
    def show_if_business_inclusive(self,values,input):
        business = values.get('assign_to_business',False)
        if business:
            input['invisible'] = not self.check_inclusive(business)
        return input

    @api.model
    def show_if_business_not_inclusive(self,values,input):
        business = values.get('assign_to_business',False)
        if business:
            input['invisible'] = self.check_inclusive(business)
        return input
    
    @api.model
    def check_inclusive(self, business):
        business = self.env['rb_delivery.user'].browse([business])
        if business and business.role_code == 'rb_delivery.role_business':
            return business.inclusive_delivery
        return False
    

    @api.model
    def compute_delivery_fee(self,values,input): # TODO: unify compute functions with order compute function
        order_type_id = values.get('order_type_id',False)
        assign_to_business = values.get('assign_to_business',False)
        customer_area = values.get('customer_area',False)
        extra_cost = values.get('extra_cost',False)
        discount = values.get('discount',False)
        alt_area = values.get('business_alt_area',False)
        show_alt_address = values.get('show_alt_address',False)
        if input and input['field'] and order_type_id and assign_to_business and customer_area:
            sender_area = self.env['rb_delivery.user'].browse([values['assign_to_business']]).area_id.id
            get_price_values = {
                'sender_id':values['assign_to_business'],
                'sender_area':sender_area,
                'to_area_id':values['customer_area'],
                'sub_area_id':values['customer_sub_area'] if 'customer_sub_area' in values else False,
                'order_type_id':values['order_type_id'],
            }

            if alt_area and show_alt_address:
                alt_area_obj = self.env['rb_delivery.area'].sudo().browse(alt_area)
                delivery_fee = self.env['rb_delivery.pricelist'].sudo().get_price(get_price_values,alt_area_obj)
            else:
                delivery_fee = self.env['rb_delivery.pricelist'].sudo().get_price(get_price_values)

            if extra_cost:
                delivery_fee += float(extra_cost)

            if discount:
                delivery_fee = delivery_fee - float(discount)
            
            service_fee = self.env['rb_delivery.order'].get_service_fee(values.get('service'), values.get('default_service'), values.get('extra_service_fee_on_sender'), values.get('extra_service_fee_on_customer'))

            delivery_fee += service_fee['total_service_fee']

            input['value'] = delivery_fee
        else:
            input['value'] = 0

        return input
    

    @api.model
    def compute_reference_id(self, values, input):
        reference_id = values.get('reference_id',False)
        assign_to_business = values.get('assign_to_business',False)
        if input and input['field'] and assign_to_business and reference_id:
            sender = self.env['rb_delivery.user'].browse([values['assign_to_business']])

            orders = self.env['rb_delivery.order'].sudo().search([('assign_to_business', '=', sender.id),('reference_id', '=', reference_id)])
            if orders and len(orders) > 0:
                input['warning_message'] = 'WARNING_REFERENCE_ID_ALREADY_EXISTS_IT_WILL_BE_UPDATED_NOT_CREATED'
            else:
                input['warning_message'] = ''
        return input
    
    @api.model
    def compute_check_if_has_sub_business(self, values, input):
        assign_to_business = values.get('assign_to_business',False)
        if input and input['field'] and assign_to_business:
            sender = self.env['rb_delivery.user'].browse([assign_to_business])
            if sender.child_ids or sender.user_parent_id:
                input['invisible'] = False
            else:
                input['invisible'] = True
    
        return input
    
    @api.model
    def compute_default_order_type(self, values, input):
        assign_to_business = values.get('assign_to_business',False)
        order_type = values.get('order_type_id', False)
        if input and input['field']:
            if order_type and values.get('is_quick_order'):
                return False   
            elif assign_to_business:
                sender = self.env['rb_delivery.user'].browse([assign_to_business])
                if sender.order_type:
                    input['value'] = {'id': sender.order_type.id, 'display_name': sender.order_type.display_name}
                else:
                    order_type = self.env['rb_delivery.order_type'].search([('default','=',True)])
                    input['value'] = {'id': order_type.id, 'display_name': order_type.display_name}
            else:
                order_type = self.env['rb_delivery.order_type'].search([('default','=',True)])
                input['value'] = {'id': order_type.id, 'display_name': order_type.display_name}

    
        return input
    
    
    @api.model
    def compute_required_from_business(self, values, input):
        if input and input['field']:
            total_service_fee = 0
            sender_service_fee = 0
            
            
            money_collection_cost = values.get('money_collection_cost') if values.get('money_collection_cost') and values.get('money_collection_cost') != '' else 0
            delivery_cost = values.get('delivery_cost') if values.get('delivery_cost') and values.get('delivery_cost') != '' else 0
            delivery_cost_on_sender = values.get('delivery_cost_on_sender')
            returned_value = values.get('returned_value')
            incusive_delivery = False
            business = self.env['rb_delivery.user'].search([('id','=',values.get('assign_to_business'))])
            service_fee = self.env['rb_delivery.order'].get_service_fee(values.get('service'), values.get('default_service'), values.get('extra_service_fee_on_sender'), values.get('extra_service_fee_on_customer'))
            if service_fee['total_service_fee']:
                total_service_fee = service_fee['total_service_fee']
            if service_fee['sender_service_fee']:
                sender_service_fee = service_fee['sender_service_fee']
            if business and business.inclusive_delivery:
                incusive_delivery = True
            if business:                
                if delivery_cost_on_sender:
                    if returned_value == False:
                        total_amount = float(money_collection_cost)
                    else:
                        if incusive_delivery:
                            total_amount = float(delivery_cost)
                        else:
                            total_amount = float(money_collection_cost)
                    if incusive_delivery and not values.get('paid'):
                        input['value'] = float(total_amount) - float(total_service_fee)
                    else:
                        input['value'] = float(total_amount) - float(delivery_cost) - float(sender_service_fee)
                else:
                    input['value'] = float(money_collection_cost) - float(delivery_cost) - float(sender_service_fee)
        return input
    
    @api.model
    def compute_money_collection_cost(self,values,input):
        money_collection_cost = 0.0
        if input and input.get('field'):
            inclusive_delivery = False
            delivery_cost = values.get('delivery_cost') if values.get('delivery_cost') and values.get('delivery_cost') != '' else 0
            copy_total_cost = values.get('copy_total_cost') if values.get('copy_total_cost') and values.get('copy_total_cost') != '' else 0
            cost = values.get('cost') if values.get('cost') and values.get('cost') != '' else 0
            is_minus = values.get('is_minus_payment', False)
            is_minus_number= isinstance(is_minus, (int, float))
            if is_minus:
                if (not is_minus_number) or (is_minus_number and is_minus > 0):
                    if float(cost) > 0:
                        cost = float(cost) * -1
                    if float(copy_total_cost) > 0:
                        copy_total_cost = float(copy_total_cost) * -1

            customer_payment = values.get('customer_payment')
            if values.get('assign_to_business'):
                business = self.env['rb_delivery.user'].search([('id','=',values.get('assign_to_business'))])
                if business and business.inclusive_delivery:
                    inclusive_delivery = True
            if not values.get('returned_discount'):
                if not customer_payment:
                    if values.get('paid'):
                        if inclusive_delivery:
                            if values.get('delivery_cost_on_customer'):
                                money_collection_cost = float(delivery_cost)
                            else:
                                money_collection_cost = 0
                        else:
                            if values.get('delivery_cost_on_sender'):
                                money_collection_cost = 0
                            else:
                                money_collection_cost = float(delivery_cost)

                    else:
                        if values.get('delivery_cost_on_sender'):
                            if inclusive_delivery:
                                money_collection_cost = float(copy_total_cost) - float(delivery_cost)
                            else:
                                money_collection_cost = float(cost)
                        else:
                            if inclusive_delivery:
                                money_collection_cost = float(copy_total_cost)
                            else:
                                money_collection_cost = float(cost) + float(delivery_cost)
                        if values.get('service') or values.get('default_service') or values.get('extra_service_fee_on_customer') or values.get('extra_service_fee_on_sender'):
                            service_fee = self.env['rb_delivery.order'].get_service_fee(values.get('service'), values.get('default_service'), values.get('extra_service_fee_on_sender'), values.get('extra_service_fee_on_customer'))
                            if service_fee and service_fee.get('customer_service_fee'):
                                money_collection_cost += service_fee['customer_service_fee']
                else:
                    money_collection_cost = customer_payment
            else:
                if not customer_payment:
                    money_collection_cost = 0.00
                else:
                    money_collection_cost = customer_payment
        
        input['value'] = money_collection_cost
        
        return input
                
    @api.model
    def compute_service_fee(self,values,input):
        service_id = values.get('service',False)
        if service_id:
            if input and input['field'] :
                services = self.env['rb_delivery.service'].browse(service_id)
                service_fee = self.env['rb_delivery.order'].get_service_fee(services,False, values.get('extra_service_fee_on_sender'), values.get('extra_service_fee_on_customer'))
                input['value'] = service_fee['total_service_fee']
                

        return input

    @api.model
    def compute_visibility_for_alternative_address(self,values,input):
        show_alt_address = values.get('show_alt_address',False)
        if show_alt_address:
            input['invisible'] = False
        else:      
            input['invisible'] = True
        return input
    
    @api.model
    def compute_visibility_for_online_payment(self,values,input):
        delivery_cost_on_sender = values.get('delivery_cost_on_sender', False)
        if delivery_cost_on_sender:
            input['invisible']=False
        else:
            input['value']=False
            input['invisible']=True
        return input
    
    @api.model
    def compute_visibility_for_delivery_cost_on_customer_and_delivery_cost_on_sender(self, values, input):
        paid = values.get('paid', False)
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid),('role_code','=','rb_delivery.role_business')])
        business_id = values.get('assign_to_business') or user.id
        input_name = input['field']['name'] if input and input['field'] else None

        if not business_id:
            input['invisible'] = True
            input['value'] = False
        else:
            business = self.env['rb_delivery.user'].sudo().browse([business_id])
            inclusive_delivery = business and business.inclusive_delivery
            if not paid:
                input['invisible'] = True
                input['value'] = False
            else:
                if inclusive_delivery and paid:
                    if input_name == 'delivery_cost_on_sender':
                        input['invisible'] = True
                        input['value'] = False
                    elif input_name == 'delivery_cost_on_customer':
                        input['invisible'] = False
                    
                elif not inclusive_delivery and paid:
                    if input_name == 'delivery_cost_on_sender':
                        input['invisible'] = False
                    elif input_name == 'delivery_cost_on_customer':
                        input['invisible'] = True
                        input['value'] = False
        return input
    
    @api.model
    def compute_receiver_is_business(self, values, input):

        receiver_is_business = values.get('receiver_is_business', False)
        if input['field']['name'] == 'receiver_business':
            if receiver_is_business and input['invisible'] == True:
                input['invisible'] = False
            elif not receiver_is_business and input['invisible'] == False:
                input['invisible'] = True
                input['value'] = False

        
        if input['field']['name'] == 'client':
            if not receiver_is_business and input['invisible'] == True:
                input['invisible'] = False
            elif receiver_is_business and input['invisible'] == False:
                input['invisible'] = True
                input['value'] = False
                
        
        return input