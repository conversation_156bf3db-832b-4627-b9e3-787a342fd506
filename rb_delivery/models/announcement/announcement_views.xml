<odoo>
    <data>
    <record id="view_announcement_tree" model="ir.ui.view">
        <field name="name">view_announcement_tree</field>
        <field name="model">rb_delivery.announcement</field>
        <field name="arch" type="xml">
            <tree>
                <field name="title"/>
                <field name="description"/>
                <field name="link"/>
            </tree>
        </field>
    </record>
    <record id="view_announcement_form" model="ir.ui.view">
        <field name="name">view_announcement_form</field>
        <field name="model">rb_delivery.announcement</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_custom_button" type="object" string="Send Announcement" class="btn-primary"/>
                </header>
                <sheet>
                    <group>
                        <field name="users" widget="many2many_tags"/>
                        <field name="title"/>
                        <field name="description"/>
                        <field name="link"/>
                        <field string="Images (png,jpg,jpeg,gif)" name="images" widget="image" options="{'width': 100, 'height': 100}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    </data>
</odoo>