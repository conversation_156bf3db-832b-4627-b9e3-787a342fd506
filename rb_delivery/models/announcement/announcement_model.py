from odoo import models, fields
class Announcement(models.Model):
    _name = 'rb_delivery.announcement'
    _description = 'Announcement'
    _order = "create_date DESC"

    title = fields.Char(string='Title', required=True, size=45, translate=True)

    description = fields.Text(string='Description', required=True, translate=True)

    images = fields.Binary(string='Images')

    link = fields.Text(string='Link')

    users = fields.Many2many(comodel_name = 'rb_delivery.user', 
                                        string = 'Users',
                                        relation = 'announcement_user_rel',
                                        domain=[['role_code', '=', 'rb_delivery.role_business']],
                                        track_visibility="on_change")


    def action_custom_button(self):
        header = self.title 
        message = self.description or ''
        announcement_link = self.link or ''
        users = self.users if self.users else self.env['rb_delivery.user'].search([('role_code', '=', 'rb_delivery.role_business'), ('state', 'in', ['confirmed', 'reconfirmed'])])
        players, emails, mobile_numbers = [], [], []
        for user in users:
            if user.player_id:
                players.append(user.player_id)
            if user.email:
                emails.append(user.email)
            if user.mobile_number:
                mobile_numbers.append(user.mobile_number)
        self.env['rb_delivery.notification_center'].with_delay(channel="root.notification",max_retries=2).notification(
            collapse_id='anouncement_'+str(self.id),
            users=users,
            emails=emails,
            players=players,
            mobile_numbers=mobile_numbers,
            header=header,
            message=message,
            is_sms=False,
            is_email=False,
            is_notification=False,
            record_id=self.id,
            model='rb_delivery.announcement',
            sequence=False,
            is_announcement=True,
            announcement_link=announcement_link,
        )