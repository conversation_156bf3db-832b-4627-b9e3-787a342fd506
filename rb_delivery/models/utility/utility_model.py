# -*- coding: utf-8 -*-
from openerp import models, fields, api, _
from openerp.exceptions import ValidationError, Warning
from unidecode import unidecode
import re
from datetime import timedelta
import os
from odoo.tools import config
import pickle
from datetime import datetime
import base64
import json
import difflib
import requests


class rb_delivery_utility(models.Model):

    _name = 'rb_delivery.utility'
    _description = "Utility Model"

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    reports_list = {'rb_delivery.agent_report': {'fields': {'money_collection_total': ['money_collection_cost']}, 'function_name': 'get_order_collection_summation'},
                    'rb_delivery.agent_money_print': {'fields': {'money_collection_total': ['money_collection_cost'],
                                                                 'delivery_cost_total': ['delivery_cost'],
                                                                 'required_from_business_total': ['required_from_business']}, 'function_name': 'get_summation'},
                    'rb_delivery.agent_report_id': {'fields': {'money_collection_total': ['money_collection_cost']}, 'function_name': 'get_summation'},

                    'rb_delivery.company_profit': {'fields': {'money_collection_total': ['money_collection_cost'],
                                                              'delivery_cost_total': ['delivery_cost'],
                                                              'required_from_business_total': ['required_from_business'],
                                                              'delivery_profit_total': ['delivery_profit'],
                                                              'agent_cost_total': ['agent_cost']}, 'function_name': 'get_order_collection_summation'},
                    'rb_delivery.agent_returned_collection': {'fields': {'money_collection_total': ['money_collection_cost'],
                                                                         'returned_value_delivery_cost_total': ['returned_value_delivery_cost_total'],
                                                                         'required_from_business_total': ['required_from_business']}, 'function_name': 'get_collection_sum_returned_value_delivery_cost_total'},
                    'rb_delivery.billing_report': {'fields': {'amount_to_be_paid_total': ['amount_to_be_paid']}, 'function_name': 'get_summation'},

                    'rb_delivery.company_profit':{'fields':{'money_collection_total':['money_collection_cost'],
                                                    'delivery_cost_total':['delivery_cost'],
                                                    'required_from_business_total':['required_from_business'],
                                                    'delivery_profit_total':['delivery_profit'],
                                                    'agent_cost_total':['agent_cost']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.agent_returned_collection':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                'returned_value_delivery_cost_total':['returned_value_delivery_cost_total'],
                                                                'required_from_business_total':['required_from_business']},'function_name':'get_collection_sum_returned_value_delivery_cost_total'},
                    'rb_delivery.billing_report':{'fields':{'amount_to_be_paid_total':['amount_to_be_paid']},'function_name':'get_summation'},

                    'rb_delivery.agent_money_collection':{'fields':{'money_collection_total':['money_collection_cost'],
                                                            'delivery_cost_total':['delivery_cost'],
                                                            'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.branch_collection_report':{'fields':{'money_collection_total':['money_collection_cost'],
                                                            'delivery_cost_total':['delivery_cost'],
                                                            'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.money_collection_finanical_report':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                        'delivery_cost_total':['delivery_cost'],
                                                                        'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.multi_print_orders_partner_money_collection':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                                'delivery_cost_total':['delivery_cost'],
                                                                                'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.collective_report':{'fields':{'money_collection_total':['money_collection_cost']},'function_name':'get_summation'},
                    'rb_delivery.money_collecion_invoice_receipt': {'fields':{'sum_extra_cost_and_service_fee':['extra_cost','service_fee'],
                                                                    'delivery_cost_total':['delivery_cost']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.money_collection_receipt_a4':{'fields':{'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.money_collection_receipt_a5':{'fields':{'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.money_collection_receipt_a6':{'fields':{'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.money_collection_total_receipt_A4':{'fields':{'sum_total_cost':['total_cost']},'function_name':'get_summation'},
                    'rb_delivery.multi_print_orders_money_collector_and_receipt_print_report':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                                    'delivery_cost_total':['delivery_cost'],
                                                                                    'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.multi_print_orders_money_collector_print_report_bank_detail':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                                    'delivery_cost_total':['delivery_cost'],
                                                                                    'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.multi_print_orders_money_collector_print_report':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                                    'delivery_cost_total':['delivery_cost'],
                                                                                    'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.order_finanical_report':{'fields':{'money_collection_total':['money_collection_cost'],
                                                            'delivery_cost_total':['delivery_cost'],
                                                            'required_from_business_total':['required_from_business']},'function_name':'get_summation'},
                    'rb_delivery.collection_default_print':{'fields':{'money_collection_total':['money_collection_cost'],
                                                            'delivery_cost_total':['delivery_cost'],
                                                            'required_from_business_total':['required_from_business']},'function_name':'get_summation'},
                    'rb_delivery.collection_print':{'fields':{'money_collection_total':['money_collection_cost'],
                                                    'delivery_cost_total':['delivery_cost'],
                                                    'required_from_business_total':['required_from_business']},'function_name':'get_summation'},
                    'rb_delivery.dist':{'fields':{'money_collection_total':['money_collection_cost']},'function_name':'get_summation'},
                    'rb_delivery.partner_money_collection':{'fields':{'money_collection_total':['money_collection_cost'],
                                                            'delivery_cost_total':['delivery_cost'],
                                                            'required_from_business_total':['required_from_business']},'function_name':'get_summation'},
                    'rb_delivery.returned_collection_per_business_print':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                            'returned_value_delivery_cost_total':['returned_value_delivery_cost_total'],
                                                                            'required_from_business_total':['required_from_business']},'function_name':'get_order_sum_returned_value_delivery_cost_total'},
                    'rb_delivery.returned_collection_print':{'fields':{'money_collection_total':['money_collection_cost'],
                                                            'returned_value_delivery_cost_total':['returned_value_delivery_cost_total'],
                                                            'required_from_business_total':['required_from_business']},'function_name':'get_order_sum_returned_value_delivery_cost_total'},
                    'rb_delivery.sender_partner_money_collection':{'fields':{'money_collection_total':['money_collection_cost'],
                                                            'delivery_cost_total':['delivery_cost'],
                                                            'required_from_business_total':['required_from_business']},'function_name':'get_summation'},
                    'rb_delivery.returned_money_collection':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                'delivery_cost_total':['delivery_cost'],
                                                                'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.returned_money_collection_with_barcode':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                'delivery_cost_total':['delivery_cost'],
                                                                'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.agent_returned_collection_print':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                 'returned_value_delivery_cost_total':['returned_value_delivery_cost_total'],
                                                                 'required_from_business_total':['required_from_business']},'function_name':'get_order_sum_returned_value_delivery_cost_total'},
                    'rb_delivery.agent_profit_report':{'fields':{'money_collection_total':['money_collection_cost'],
                                            'delivery_cost_total':['delivery_cost'],
                                            'total_amount':['copy_total_cost'],
                                            'agent_profit_total':['required_to_company'],
                                            'agent_cost_total':['agent_cost']},'function_name':'get_order_collection_summation'},

                    'rb_delivery.returned_money_collection_with_reason':{'fields':{'required_from_business':['required_from_business']}
                                                                         ,'function_name':'get_order_collection_summation'},
                    'rb_delivery.multi_print_driver_receipt_report':{'fields':{'sum_total_cost':['required_from_business']}
                                                        ,'function_name':'get_order_collection_summation'},
                    'rb_delivery.runsheet_without_barcode':{'fields':{'money_collection_total':['money_collection_cost']},'function_name':'get_summation'},
                    'rb_delivery.collection_with_note_print':{'fields':{'required_from_business_total':['required_from_business'],'delivery_cost_total':['delivery_cost'],'money_collection_total':['money_collection_cost']},'function_name':'get_summation'},
                    'rb_delivery.agent_report_with_services':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                      'total_cost_of_service':['service_fee'],
                                                                      'total_cost_of_delivery':['delivery_cost'],
                                                                      'total_agent_collection':['agent_cost'],
                                                                      'required_to_company':['required_to_company']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.money_collection_with_services':{'fields':{'money_collection_total':['money_collection_cost'],
                                                                      'total_cost_of_service':['service_fee'],
                                                                      'total_cost_of_delivery':['delivery_cost'],
                                                                      'total_business_collection':['required_from_business']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.driver_receipt':{'fields':{'money_collection_total':['money_collection_cost'],'agent_cost_total':['agent_cost'],'required_from_business_total':['required_from_business'], 'total_amount':['copy_total_cost']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.multi_print_orders_money_collector_print_with_note_report':{'fields':{'required_from_business_total':['required_from_business'],'delivery_cost_total':['delivery_cost'],'money_collection_total':['money_collection_cost']},'function_name':'get_order_collection_summation'},
                    'rb_delivery.multi_partner_collection_detailed':{'fields':{'money_collection_total':['money_collection_cost'],
                                                            'delivery_cost_total':['delivery_cost'],
                                                            'total_amount':['copy_total_cost'],
                                                            'required_from_business_total':['required_from_business']},'function_name':'get_order_collection_summation' },
                    }

    COLLECTION_FIELDS_MAPPING= {
        'agent_money_collection_mapping_fields' : {
                'required_from_business' : 'total_cost',
                'delivery_cost' : 'total_delivery_cost' ,
                'money_collection_cost' : 'total_money_collection_cost' ,
                'delivery_profit' : 'total_delivery_profit' ,
                'agent_cost' : 'total_agent_cost' ,
                'required_to_company' : 'total_required_from_driver'
            },
        'money_collection_mapping_fields':{
            'required_from_business':'total_cost',
            'delivery_cost':'total_delivery_cost',
            'money_collection_cost': 'total_money_collection_cost',
            'delivery_profit':'company_profit_total',
            },
        'returned_monay_collection_mapping_fields':{
            'required_from_business': 'total_cost',
            'delivery_cost':'total_delivery_cost' ,
            'money_collection_cost':'total_money_collection_cost'
        },
        'runsheet_collection_mapping_fields':{
            'money_collection_cost':'total_money_collection_runsheet'
        },
        'returned_agent_money_collection_mapping_fields':{
            'required_from_business': 'total_cost',
            'delivery_cost': 'total_delivery_cost',
            'money_collection_cost': 'total_money_collection_cost'
        }
    }
    def olivery_sudo(self,data):
        message = data.get('message')
        records= data.get('records')
        values = data.get('values')
        update = data.get('update')
        uid = self.env.user.partner_id.id
        if records:
            if update and values:
                records.with_context(original_uid=uid,automated=True,automation_message=message).sudo().write(values)
            if not isinstance(records, self.env['rb_delivery.order'].__class__):
                for rec in records:
                    rec.with_context(original_uid=uid).sudo().message_post(body=message)
        return

    def get_collection_sum_returned_value_delivery_cost_total(self, records, fields):
        value = 0
        if 'returned_value_delivery_cost_total' in fields and fields['returned_value_delivery_cost_total']:
            del fields['returned_value_delivery_cost_total']
        data = self.get_order_collection_summation(records, fields)
        for record in records:
            for order in record.order_ids:
                if order.returned_value:
                    value = value + float(order.returned_value)
                else:
                    value = value + order.delivery_cost
        data['returned_value_delivery_cost_total'] = value
        return data

    def get_order_sum_returned_value_delivery_cost_total(self, records, fields):
        value = 0
        if 'returned_value_delivery_cost_total' in fields and fields['returned_value_delivery_cost_total']:
            del fields['returned_value_delivery_cost_total']
        data = self.get_summation(records, fields)
        for record in records:
            if record.returned_value:
                value = value + float(record.returned_value)
            else:
                value = value + record.delivery_cost
        data['returned_value_delivery_cost_total'] = value
        return data

    def get_summation(self, records, fields):
        value = 0
        data = {}
        for key, field_names in fields.items():
            value = 0
            for record in records:
                for field_name in field_names:
                    value = value + record.sudo()[field_name]
            data[key] = value
        return data
    def get_round_up_summation(self, records, fields, decimal_value):
        data = {}
        for key, field_names in fields.items():
            data[key] = sum(self.custom_round(record.sudo()[field_name], decimal_value)  for record in records for field_name in field_names)
        return data

    def custom_round(self,number, decimal_places):

        if decimal_places == 0 and number - int(number) == 0.5:
            return int(number) + 1

        multiplier = 10 ** decimal_places

        result = round(number * multiplier) / multiplier

        return int(result) if result.is_integer() else result

    def get_order_collection_summation(self, records, fields):
        order_ids = []
        for record in records:
            order_ids += record.order_ids.ids
        orders = self.env['rb_delivery.order'].sudo().browse(order_ids)
        decimal_value = int(self.env['rb_delivery.client_configuration'].get_param('global_dicimal_value'))
        if (records._name=='rb_delivery.multi_print_orders_money_collector' and decimal_value!=2):
            return self.get_round_up_summation(orders, fields,decimal_value)
        else:
            summation_result = self.get_summation(orders, fields)
            rounded_summation_result = {key: round(value, decimal_value) for key, value in summation_result.items()}
            return rounded_summation_result

    def get_meta_data(self, record_ids, report_name):
        report = self.env['ir.actions.report'].sudo().search(
            [('report_name', '=', report_name)])
        if report and report.model:
            if not isinstance(record_ids, list):
                record_ids = record_ids.split(',')
            record_ids = [eval(i) for i in record_ids]
            records = self.env[report.model].sudo().browse(record_ids)
            if report_name in self.reports_list and self.reports_list[report_name] and 'fields' in self.reports_list[report_name] and self.reports_list[report_name]['fields'] and 'function_name' in self.reports_list[report_name] and self.reports_list[report_name]['function_name']:
                try:
                    method_to_call = getattr(
                        rb_delivery_utility, self.reports_list[report_name]['function_name'])
                    data = method_to_call(
                        self, records, self.reports_list[report_name]['fields'])
                    return data
                except Exception as e:
                    self.env['rb_delivery.error_log'].raise_olivery_error(550,self.id,{'error':str(e)})
                    #raise ValidationError(str(_(e)))
        return {}

    @api.model
    def delete_old_sessions(self):
        session_storage_path = config.session_dir
        for filename in os.listdir(session_storage_path):
            filepath = os.path.join(session_storage_path, filename)
            with open(filepath, 'rb') as file:
                session_data = pickle.load(file)
                creation_timestamp = os.path.getctime(filepath)
                session_date = datetime.fromtimestamp(
                    creation_timestamp).strftime('%Y-%m-%d %H:%M:%S')
                session_datetime_obj = datetime.strptime(
                    session_date, '%Y-%m-%d %H:%M:%S')
                one_month_ago = datetime.now() - timedelta(days=30)
                if session_datetime_obj < one_month_ago:
                    os.remove(filepath)
                if ('session_token' not in session_data) or ('session_token' in session_data and not session_data['session_token']):
                    os.remove(filepath)

    def send_toast(self, toast_type, message, uid):
        # use for_all if you want to show the toast for everyone but the logged user
        if toast_type == 'for_all':
            sent_message = str(message) + '/' + str(uid)
            self.env['bus.bus'].sendone('auto_refresh', sent_message)
        # use for_user if you want to show the toast only for the logged user
        if toast_type == 'for_user':
            messages = []
            for mes in message:
                sent_message = str(mes) + '/' + str(uid)
                messages.append(sent_message)
            self.env['bus.bus'].sendone(uid, messages)
        if toast_type == 'for_users':
            messages = []
            index = 0
            for mes in message:
                index = index + 1
                if index != 2:
                    sent_message = str(mes) + '/' + str(uid)
                else:
                    sent_message = mes

                messages.append(sent_message)
            self.env['bus.bus'].sendone(uid, messages)

    def check_arabic_letter(self, number):
        pattern2 = "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufc3f]|[\ufe70-\ufefc]"
        pattern = "^[\u0600-\u065F\u066A-\u06EF\u06FA-\u06FF]"
        arabic_letter_pattern = "[أ  ب  ت  ث  ج  ح  خ  د  ذ  ر  ز  س  ش  ص  ض  ط  ظ  ع  غ  ف  ق  ك  ل  م  ن  هـ  و ي]"

        has_arabic_letter = False
        if (number and re.findall(str(pattern), str(number)) or
                number and re.findall(str(pattern2), str(number)) or
            '\u0600' <= str(number) <= '\u06FF' or
                    '\u0750' <= str(number) <= '\u077F' or
                '\u08A0' <= str(number) <= '\u08FF' or
                '\uFB50' <= str(number) <= '\uFDFF' or
                    '\uFE70' <= str(number) <= '\uFEFF' or
                    '\U00010E60' <= str(number) <= '\U00010E7F' or
                '\U0001EE00' <= str(number) <= '\U0001EEFF' or
                '\u0600-\u065F\u066A-\u06EF\u06FA-\u06FF' <= str(
            number) <= '\u0600-\u065F\u066A-\u06EF\u06FA-\u06FF'
            ) or re.findall(str(arabic_letter_pattern), str(number)) :
            has_arabic_letter = True
        return has_arabic_letter

    def normalize_mobile_number(self, mobile_number):
        if mobile_number:
            char_removed_str = self.env['rb_delivery.client_configuration'].get_param(
                'normalize_mobile_number')
            intro_removed_str = self.env['rb_delivery.client_configuration'].get_param('normalize_intros')
            prefix_one = self.env['rb_delivery.client_configuration'].get_param(
                'mobile_number_prefix_one')
            prefix_two = self.env['rb_delivery.client_configuration'].get_param(
                'mobile_number_prefix_two')
            add_zero_at_the_beginning_of_the_mobile_number = self.env['rb_delivery.client_configuration'].get_param('add_zero_at_the_beginning_of_the_mobile_number')
            char_remove = char_removed_str.split(',')
            intro_remove = intro_removed_str.split(',')
            char_remove.sort(key=lambda x: (x == 'intro', x))
            for char in char_remove:
                if char == 'intro':
                    if prefix_one:
                        intro_remove.append(prefix_one)
                    if prefix_two:
                        intro_remove.append(prefix_two)
                    for prefix in intro_remove:
                        prefix_len = len(prefix)
                        mobile_prefix = mobile_number[:prefix_len]
                        if mobile_number.startswith(prefix):
                            mobile_number = mobile_number[len(mobile_prefix):]
                            if add_zero_at_the_beginning_of_the_mobile_number:
                                mobile_number = "0" + mobile_number
                else:
                    mobile_number = mobile_number.replace(char, "")
            if mobile_number[:1] != "0" and add_zero_at_the_beginning_of_the_mobile_number:
                mobile_number = "0"+mobile_number
        return mobile_number

    def check_mobile_number(self, mobile_number, field_name=_('Mobile number')):
        mobile_number = str(mobile_number)
        mobile_number = mobile_number.strip()
        mobile_digits = self.env['rb_delivery.client_configuration'].get_param(
            'mobile_number_digit')
        pattern = "[١٢٣٤٥٦٧٨٩]"
        arabic_letter_pattern = "[أ  ب  ت  ث  ج  ح  خ  د  ذ  ر  ز  س  ش  ص  ض  ط  ظ  ع  غ  ف  ق  ك  ل  م  ن  هـ  و ي]"
        number = mobile_number
        # remove spaces from mobile number if exist
        if number:
            number = ''.join(number.split())

        if number and re.findall(str(arabic_letter_pattern), str(number)) or re.findall(str(pattern), str(number)):
            number = unidecode(u''+number)

        try:
            digits = mobile_digits.split('-')
            if len(digits)==1:
                min_digit = digits[0]
                max_digit = digits[0]
            else:
                min_digit = digits[0]
                max_digit = digits[1]
        except:
            min_digit = 10
            max_digit = 10

        number = self.normalize_mobile_number(number)
        self.check_mobile_number_len(number, min_digit, max_digit, field_name)

        mobile_pattern = "^[\u0621-\u064A\u0660-\u06690-9 ]+$"
        check_arabic_letter = self.check_arabic_letter(number)
        if number and not re.match(str(mobile_pattern), str(number)):
            self.env['rb_delivery.error_log'].raise_olivery_error(551,self.id,{'field_name':field_name})
            #raise ValidationError(
            #    _("Your %s must contain only numbers") % (field_name))
        if check_arabic_letter:
            self.env['rb_delivery.error_log'].raise_olivery_error(551,self.id,{'field_name':field_name})
            #raise ValidationError(
            #    _("Your %s must contain only numbers") % (field_name))

        if number and re.match(str(pattern), str(number)):
            mobile = unidecode(u''+number)
            number = mobile

        if number == mobile_number:
            return False
        else:
            return number

    def check_text_characters_or_numbers(self, number):
        pattern = "^[\u0621-\u064A\u0660-\u06690-9 ]+$"
        email_registration = False
        if number and re.findall(str(pattern), str(number)):
            email_registration = False
        else:
            email_registration = True

        return email_registration
    
    @api.model
    def check_mobile_number_len(self, number, min_digit, max_digit, field_name):
        if number and ( len(str(number)) < int(min_digit) or len(str(number)) > int(max_digit)):
            if min_digit == max_digit:
                self.env['rb_delivery.error_log'].raise_olivery_error(552,self.id,{'field_name':field_name, 'min_digit':str(min_digit)})
            self.env['rb_delivery.error_log'].raise_olivery_error(553,self.id,{'field_name':field_name, 'min_digit':str(min_digit), 'max_digit':str(max_digit)})


    @api.model
    def print_report(self,report_name,model_name,records_ids):
        pdf, _ = self.env['ir.actions.report'].search([('report_name','=',report_name),('model','=',model_name)]).sudo().with_context(lang=self.env.user.lang).render_qweb_pdf(records_ids)
        data = base64.encodestring(pdf)
        return data

    @api.model
    def print_report_job_queued(self,report_name,model_name,records_ids):
        context = dict(self._context)
        context = json.dumps(context) if context else json.dumps({'uid': self.env.uid, 'lang': self.env.user.lang})
        uid = self.env.user.partner_id.id

        jq_id = self.env['rb_delivery.report_job_queue'].with_context(lang=self.env.user.lang, original_uid=uid).sudo().with_delay(channel='root.report', max_retries=2).generate_mobile_pdf_report(report_name, model_name, records_ids, context)
        return jq_id.uuid


    @api.model
    def execute_status_actions(self, status_action, collections, model_name):
        # Mapping model names to their corresponding signature field in the 'rb_delivery.signature' model
        model_field_mapping = {
            'rb_delivery.multi_print_orders_money_collector': 'collection_id',
            'rb_delivery.returned_money_collection': 'returned_collection_id',
            'rb_delivery.agent_money_collection': 'agent_money_collection_id',
            'rb_delivery.agent_returned_collection': 'agent_returned_collection_id',
            'rb_delivery.runsheet': 'runsheet_id',
        }

        # Find the user
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)], limit=1)
        if not status_action:
            return

        # Determine the correct signature field based on the model name parameter, if applicable
        signature_field_name = model_field_mapping.get(model_name)

        for collection_id in collections:
            collection_record = self.env[model_name].search([('id', '=', collection_id)], limit=1)
            if not collection_record:
                continue

            # Process signature if 'signature_image' is in the status_action and a mapping exists
            if 'signature_image' in status_action and status_action['signature_image'] and 'state' in status_action and status_action['state'] and signature_field_name:
                image_data = status_action['signature_image'].split(',')[1]
                signature_values = {
                    'signature_image': image_data,
                    'status': status_action['state'],
                    'user_id': [(6, 0, [user.id])],
                }
                # Add the specific collection ID to the signature values based on the determined field name
                signature_values[signature_field_name] = [(6, 0, [collection_id])]
                self.env['rb_delivery.signature'].create(signature_values)

            # Update the collection record with other status_action values excluding the ignored ones
            collection_values = {'state': status_action.get('state', False)}
            for key, value in status_action.items():
                if key not in ['signature', 'signature_image', 'state']:
                    collection_values[key] = value
            collection_record.write(collection_values)

        return


    def convert_names_to_ids(self,model_name, vals, env):
            Model = env[model_name]
            for field_name, value in vals.items():
                # Skip fields that don't need conversion
                if isinstance(value, int) or not isinstance(value, str):
                    continue

                # Get the field type
                if field_name in Model._fields:
                    field_type = Model._fields[field_name].type

                    # Handle Many2one fields
                    if field_type == 'many2one':
                        related_model = Model._fields[field_name].comodel_name
                        if related_model == 'rb_delivery.user':
                         record = env[related_model].search([('username', '=', value)], limit=1)
                        else:
                         record = env[related_model].search([('name', '=', value)], limit=1)
                        if record:
                            vals[field_name] = record.id
                            print(f"Converted {field_name} Name to ID: {record.id}")
                        else:
                            print(f"No matching record found for {field_name} with name {value}")

                    # Handle Many2many fields
                    elif field_type == 'many2many':
                        related_model = Model._fields[field_name].comodel_name
                        # This assumes names are unique and you can use name to find a record
                        # For more complex cases, you might need additional logic
                        records = env[related_model].search([('name', 'in', value if isinstance(value, list) else [value])])
                        if records:
                            vals[field_name] = [(6, 0, records.ids)]
                            print(f"Converted {field_name} Names to IDs: {records.ids}")
                        else:
                            print(f"No matching records found for {field_name} with names {value}")

            return vals

    def reflect_changes_to_collections(self,order_ids,collection_type):
        orders = self.env['rb_delivery.order'].browse(order_ids)
        order_fields = self.env['rb_delivery.order'].fields_get()
        total_amount = 0
        fields_to_change_mapping = False
        values_to_change = {}
        if collection_type == 'agent_money_collection':
            fields_to_change_mapping = self.COLLECTION_FIELDS_MAPPING['agent_money_collection_mapping_fields']
        if collection_type == 'money_collection':
            fields_to_change_mapping = self.COLLECTION_FIELDS_MAPPING['money_collection_mapping_fields']
        if collection_type == 'returned_money_collection':
            fields_to_change_mapping = self.COLLECTION_FIELDS_MAPPING['returned_monay_collection_mapping_fields']
        if collection_type == 'runsheet_collection':
            fields_to_change_mapping = self.COLLECTION_FIELDS_MAPPING['runsheet_collection_mapping_fields']
        if collection_type == 'returned_agent_money_collection':
            fields_to_change_mapping = self.COLLECTION_FIELDS_MAPPING['returned_agent_money_collection_mapping_fields']

        if orders and fields_to_change_mapping:
            values_to_change = {value: 0 for value in fields_to_change_mapping.values()}
            for order in orders:
                for key, value in fields_to_change_mapping.items():
                    if order_fields[key]['type'] in ['integer','float']:
                        values_to_change[value] += getattr(order, key, 0)
                    elif order_fields[key]['type'] in ['many2one']:
                        values_to_change[value] = getattr(order, key, 0).id
                    else:
                        values_to_change[value] = getattr(order, key, 0)
                if order.inclusive_delivery:
                    total_amount += order.copy_total_cost
                else:
                    total_amount += order.cost + order.delivery_cost

            if total_amount:
                values_to_change['total_ammount'] = total_amount

        return values_to_change


    @api.model
    def create_money_collection(self, context):
        orders = self.env['rb_delivery.order'].browse(context.get('order_ids'))

        collection_groups = {}

        for order in orders:
            if order.collection_id:
                collection_id = order.collection_id.id
                if collection_id not in collection_groups:
                    collection_groups[collection_id] = {'orders': [], 'message': ''}
                collection_groups[collection_id]['orders'].append(order)

        for collection_id, group_data in collection_groups.items():
            if collection_id is not None:
                orders_in_collection_sequences = [str(order.sequence) for order in group_data['orders'][:3]] + (['...'] if len(group_data['orders']) > 3 else [])
                collection_sequence = group_data['orders'][0].collection_id.sequence

                self.env['rb_delivery.error_log'].raise_olivery_error(102, self.id, {'collection_sequence': collection_sequence, 'order_sequence': ', '.join(orders_in_collection_sequences)})


        orders_not_in_collection_ids = context.get('orders_not_in_collection')
        if not orders_not_in_collection_ids:
            orders_not_in_collection_ids = [order.id for order in orders if not order.collection_id]

        orders_in_collection_ids = context.get('orders_in_collection')
        if not orders_in_collection_ids:
            orders_in_collection_ids = [order.id for order in orders if order.collection_id]

        orders = self.env['rb_delivery.order'].browse(orders_not_in_collection_ids)
        orders_in_collection = self.env['rb_delivery.order'].browse(orders_in_collection_ids)
        orders_by_collection = {}
        for order in orders_in_collection:
            collection_id = order.collection_id.id
            collection_state = order.collection_id.state
            is_prepaid_collection = order.collection_id.is_pre_paid_collection
            if is_prepaid_collection:
                if collection_id not in orders_by_collection:
                    orders_by_collection[collection_id] = {'state':collection_state,'orders':[]}
                if order.state != collection_state :
                    orders_by_collection[collection_id]['orders'].append(order.id)

        for collection_id, collection_values in orders_by_collection.items():
            if len(collection_values['orders']) > 0 :
                orders_to_change_state = self.env['rb_delivery.order'].browse(collection_values['orders'])
                for order in orders_to_change_state:
                    order.message_post(_('Orders status changed automatically to %s because order exist in prepaid collection')%(collection_values['state']))
                orders_to_change_state.write({'state':collection_values['state']})
        context_type = context.get('context_type')
        name = context.get('name')
        user_id = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
        recs = self.env['rb_delivery.multi_print_orders_money_collector'].create_multi_collection(orders)
        ids = []
        for rec in recs:
            values = {'order_ids':rec}
            if name:
                values['name'] = name
            if context_type == 'branch':
                values['branch_id']=user_id.branch_id.id
            report = self.env['rb_delivery.multi_print_orders_money_collector'].create(values)
            if report:
                ids.append(report.id)

        return ids



    @api.model
    def create_agent_collection(self, orders=False, order_ids=False, name=False):
        orders_list = False
        if orders:
            orders_list = orders
        else:
            orders_list = self.env['rb_delivery.order'].browse(order_ids)

        recs = self.env['rb_delivery.agent_money_collection'].create_multi_collection(orders_list)
        ids = []
        for rec in recs:
            values = {'order_ids':rec}
            if name:
                values['name'] = name
            report = self.env['rb_delivery.agent_money_collection'].create(values)
            if report:
                ids.append(report.id)
        return ids

    @api.model
    def create_returned_collection(self, orders=False, order_ids=False, name=False):
        orders_list = False
        if orders:
            orders_list = orders
        else:
            orders_list = self.env['rb_delivery.order'].browse(order_ids)

        recs = self.env['rb_delivery.returned_money_collection'].create_multi_collection(orders_list)
        ids = []
        for rec in recs:
            values = {'order_ids':rec}
            if name:
                values['name'] = name
            report = self.env['rb_delivery.returned_money_collection'].create(values)
            if report:
                ids.append(report.id)
        return ids

    @api.model
    def fuzzy_search_matcher(self, target, original):
        matcher = difflib.SequenceMatcher(None, target, original)
        return matcher.ratio()
    

    def find_closest_language(self, lang):
        available_langs = self.env['res.lang'].sudo().search([]).mapped('code')
        if lang in available_langs:
            return lang
        
        lang_prefix = lang.split('_')[0]
        closest_match = next((al for al in available_langs if al.startswith(lang_prefix)), None)
        
        return closest_match or 'ar_SY'
    
    @api.model
    def update_orders_zone(self, new_zone_id, domain):
        zone_status_ids = self.env['rb_delivery.client_configuration'].get_param('zone_assignment_allowed_statuses')
        if not zone_status_ids:
            return

        zone_status_arr = self.env['rb_delivery.status'].browse(zone_status_ids).mapped('name')
        domain += [('state', 'in', zone_status_arr)]

        orders = self.env['rb_delivery.order'].sudo().search(domain)
        if orders:
            orders.write({'zone_id': new_zone_id})


    @api.model
    def convert_lines_to_orders(self, model_name, values):
        if not values or not model_name:
            return values
        Model = self.env[model_name]
        one2many_fields = {
            field.name
            for field in Model._fields.values()
            if field.type == 'one2many'
        }

        ignore_fields = {"Don't import"}
        result = []
        current_record = None

        for line in values:
            keys = set(line.keys())

            one2many_keys = {
                key for key in keys
                if '/' in key and key.split('/')[0] in one2many_fields
            }

            non_one2many_keys = {
                key for key in keys - one2many_keys
                if key not in ignore_fields and line.get(key) not in [None, '']
            }

            if non_one2many_keys:

                current_record = {
                    key: value for key, value in line.items()
                    if '/' not in key and key.split('/')[0] not in one2many_fields
                }
                result.append(current_record)

                for key in one2many_keys:
                    o2m_field, sub_field = key.split('/', 1)
                    if o2m_field not in current_record:
                        current_record[o2m_field] = []

            if current_record:
                sub_records = {}
                for key in one2many_keys:
                    o2m_field, sub_field = key.split('/', 1)
                    value = line[key]
                    sub_records.setdefault(o2m_field, {})[sub_field] = value

                for o2m_field, sub_data in sub_records.items():
                    if any(v not in (None, '', [], {}, False) for v in sub_data.values()):
                        if o2m_field not in current_record:
                            current_record[o2m_field] = []
                        current_record[o2m_field].append((0, 0, sub_data))

        return result
    
    @api.model
    def get_excel_result(self, new_orders):
        result_ids = []
        result_sequences = []
        result_references = []
        result_money_collection_costs = []

        for order in new_orders.sudo():
            follow_orders = order.follow_up_order
            count = len(follow_orders) if follow_orders else 1
            for __ in range(count):
                result_ids.append(order.id)
                result_sequences.append(order.sequence)
                result_references.append(order.reference_id)
            result_money_collection_costs.append(order.money_collection_cost)

        total_result_money_collection_costs = 0
        try:
            decimal_value = int(self.env['rb_delivery.client_configuration'].get_param('global_dicimal_value'))
            result_money_collection_costs = [self.custom_round(cost, decimal_value) for cost in result_money_collection_costs]
            total_result_money_collection_costs = sum(result_money_collection_costs)
        except Exception as e:
            result_money_collection_costs = [round(cost, 2) for cost in result_money_collection_costs]
            total_result_money_collection_costs = sum(result_money_collection_costs)

        return {
            'code': 200,
            'success': True,
            'message': _('Orders successfully created'),
            'ids': result_ids,
            'sequences': result_sequences,
            'references': result_references,
            'totalCOD': total_result_money_collection_costs,
        }
    @api.model
    def check_true_buyer(self, phone_number):
        try:
            if not phone_number:
                return {
                    'success': False,
                    'error': 'phone_number is required'
                }
            
            token = self.env['rb_delivery.client_configuration'].sudo().get_param('true_buyer_token')

            url = "https://truebuyer.olivery.io/true_buyer"
            headers = {"X-API-Key": token}
            params = {"phone_number": phone_number}


            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()

            data = response.json()

            return {
                'success': True,
                'data': data
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }